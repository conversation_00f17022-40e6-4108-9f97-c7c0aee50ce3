lockfileVersion: '6.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

dependencies:
  '@bachdgvn/vue-otp-input':
    specifier: ^1.0.8
    version: 1.0.8
  '@ckeditor/ckeditor5-build-classic':
    specifier: ^19.0.2
    version: 19.0.2
  '@ckeditor/ckeditor5-vue':
    specifier: ^1.0.1
    version: 1.0.3
  '@highcharts/map-collection':
    specifier: ^2.1.0
    version: 2.3.0
  '@lottiefiles/vue-lottie-player':
    specifier: ^1.1.0
    version: 1.1.0
  '@nuxt/ufo':
    specifier: ^0.5.4
    version: 0.5.4
  '@nuxtjs/proxy':
    specifier: ^2.1.0
    version: 2.1.0
  '@tinymce/tinymce-vue':
    specifier: ^3.0.0
    version: 3.2.8(vue@2.7.16)
  at-least-node:
    specifier: ^1.0.0
    version: 1.0.0
  axios:
    specifier: ^0.19.2
    version: 0.19.2
  babel-runtime:
    specifier: ^6.26.0
    version: 6.26.0
  cheerio:
    specifier: ^1.0.0
    version: 1.1.0
  chokidar:
    specifier: ^3.5.1
    version: 3.6.0
  color:
    specifier: ^4.2.3
    version: 4.2.3
  cookie-universal:
    specifier: ^2.1.4
    version: 2.2.2
  cookie-universal-nuxt:
    specifier: ^2.1.3
    version: 2.2.2
  decimal.js:
    specifier: ^10.2.0
    version: 10.4.3
  decode-uri-component:
    specifier: ^0.2.0
    version: 0.2.2
  element-ui:
    specifier: ^2.15.14
    version: 2.15.14(vue@2.7.16)
  file-saver:
    specifier: ^2.0.5
    version: 2.0.5
  graceful-fs:
    specifier: ^4.2.6
    version: 4.2.11
  highcharts:
    specifier: ^8.1.2
    version: 8.2.2
  highcharts-vue:
    specifier: ^1.3.5
    version: 1.4.3(highcharts@8.2.2)(vue@2.7.16)
  is-what:
    specifier: ^4.1.16
    version: 4.1.16
  isarray:
    specifier: ^2.0.5
    version: 2.0.5
  isobject:
    specifier: ^4.0.0
    version: 4.0.0
  jquery:
    specifier: ^3.6.3
    version: 3.7.1
  json-parse-better-errors:
    specifier: ^1.0.2
    version: 1.0.2
  less:
    specifier: ^3.11.3
    version: 3.13.1
  less-loader:
    specifier: ^6.1.2
    version: 6.2.0(webpack@4.47.0)
  moment:
    specifier: ^2.26.0
    version: 2.30.1
  neo-async:
    specifier: ^2.6.2
    version: 2.6.2
  nuxt:
    specifier: ^2.17.3
    version: 2.18.1(consola@3.2.3)(vue@2.7.16)
  papaparse:
    specifier: ^5.4.1
    version: 5.4.1
  path-to-regexp:
    specifier: ^6.2.0
    version: 6.3.0
  qrcode.vue:
    specifier: ^1.7.0
    version: 1.7.0(vue@2.7.16)
  sortablejs:
    specifier: ^1.14.0
    version: 1.15.3
  spark-md5:
    specifier: ^3.0.2
    version: 3.0.2
  tinymce:
    specifier: ^4.9.11
    version: 4.9.11
  union-value:
    specifier: ^2.0.1
    version: 2.0.1
  universalify:
    specifier: ^2.0.0
    version: 2.0.1
  via-editor:
    specifier: ^1.1.4
    version: 1.1.4
  vue:
    specifier: ^2.7.16
    version: 2.7.16
  vue-clipboard2:
    specifier: ^0.3.1
    version: 0.3.3
  vue-focus-lock:
    specifier: ^1.4.0
    version: 1.4.1(vue@2.7.16)
  vue-template-compiler:
    specifier: ^2.7.16
    version: 2.7.16
  vuedraggable:
    specifier: ^2.24.3
    version: 2.24.3
  vuex:
    specifier: ^3.6.2
    version: 3.6.2(vue@2.7.16)
  vuex-persistedstate:
    specifier: ^3.0.1
    version: 3.2.1(vue@2.7.16)(vuex@3.6.2)
  webpack-sources:
    specifier: ^2.2.0
    version: 2.3.1
  xlsx:
    specifier: ^0.16.9
    version: 0.16.9

packages:

  /@ampproject/remapping@2.3.0:
    resolution: {integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25
    dev: false

  /@babel/code-frame@7.24.7:
    resolution: {integrity: sha512-BcYH1CVJBO9tvyIZ2jVeXgSIMvGZ2FDRvDdOIVQyuklNKSsx+eppDEBq/g47Ayw+RqNFE+URvOShmf+f/qwAlA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/highlight': 7.24.7
      picocolors: 1.1.0
    dev: false

  /@babel/compat-data@7.25.4:
    resolution: {integrity: sha512-+LGRog6RAsCJrrrg/IO6LGmpphNe5DiK30dGjCoxxeGv49B10/3XYGxPsAwrDlMFcFEvdAUavDT8r9k/hSyQqQ==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/core@7.25.2:
    resolution: {integrity: sha512-BBt3opiCOxUr9euZ5/ro/Xv8/V7yJ5bjYMqG/C1YAo8MIKAnumZalCN+msbci3Pigy4lIQfPUpfMM27HMGaYEA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.24.7
      '@babel/generator': 7.25.6
      '@babel/helper-compilation-targets': 7.25.2
      '@babel/helper-module-transforms': 7.25.2(@babel/core@7.25.2)
      '@babel/helpers': 7.25.6
      '@babel/parser': 7.25.6
      '@babel/template': 7.25.0
      '@babel/traverse': 7.25.6
      '@babel/types': 7.25.6
      convert-source-map: 2.0.0
      debug: 4.3.7
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/generator@7.25.6:
    resolution: {integrity: sha512-VPC82gr1seXOpkjAAKoLhP50vx4vGNlF4msF64dSFq1P8RfB+QAuJWGHPXXPc8QyfVWwwB/TNNU4+ayZmHNbZw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.25.6
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 2.5.2
    dev: false

  /@babel/helper-annotate-as-pure@7.24.7:
    resolution: {integrity: sha512-BaDeOonYvhdKw+JoMVkAixAAJzG2jVPIwWoKBPdYuY9b452e2rPuI9QPYh3KpofZ3pW2akOmwZLOiOsHMiqRAg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.25.6
    dev: false

  /@babel/helper-builder-binary-assignment-operator-visitor@7.24.7:
    resolution: {integrity: sha512-xZeCVVdwb4MsDBkkyZ64tReWYrLRHlMN72vP7Bdm3OUOuyFZExhsHUUnuWnm2/XOlAJzR0LfPpB56WXZn0X/lA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/traverse': 7.25.6
      '@babel/types': 7.25.6
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helper-compilation-targets@7.25.2:
    resolution: {integrity: sha512-U2U5LsSaZ7TAt3cfaymQ8WHh0pxvdHoEk6HVpaexxixjyEquMh0L0YNJNM6CTGKMXV1iksi0iZkGw4AcFkPaaw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/compat-data': 7.25.4
      '@babel/helper-validator-option': 7.24.8
      browserslist: 4.23.3
      lru-cache: 5.1.1
      semver: 6.3.1
    dev: false

  /@babel/helper-create-class-features-plugin@7.25.4(@babel/core@7.25.2):
    resolution: {integrity: sha512-ro/bFs3/84MDgDmMwbcHgDa8/E6J3QKNTk4xJJnVeFtGE+tL0K26E3pNxhYz2b67fJpt7Aphw5XcploKXuCvCQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-annotate-as-pure': 7.24.7
      '@babel/helper-member-expression-to-functions': 7.24.8
      '@babel/helper-optimise-call-expression': 7.24.7
      '@babel/helper-replace-supers': 7.25.0(@babel/core@7.25.2)
      '@babel/helper-skip-transparent-expression-wrappers': 7.24.7
      '@babel/traverse': 7.25.6
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helper-create-regexp-features-plugin@7.25.2(@babel/core@7.25.2):
    resolution: {integrity: sha512-+wqVGP+DFmqwFD3EH6TMTfUNeqDehV3E/dl+Sd54eaXqm17tEUNbEIn4sVivVowbvUpOtIGxdo3GoXyDH9N/9g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-annotate-as-pure': 7.24.7
      regexpu-core: 5.3.2
      semver: 6.3.1
    dev: false

  /@babel/helper-define-polyfill-provider@0.6.2(@babel/core@7.25.2):
    resolution: {integrity: sha512-LV76g+C502biUK6AyZ3LK10vDpDyCzZnhZFXkH1L75zHPj68+qc8Zfpx2th+gzwA2MzyK+1g/3EPl62yFnVttQ==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-compilation-targets': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      debug: 4.3.7
      lodash.debounce: 4.0.8
      resolve: 1.22.8
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helper-member-expression-to-functions@7.24.8:
    resolution: {integrity: sha512-LABppdt+Lp/RlBxqrh4qgf1oEH/WxdzQNDJIu5gC/W1GyvPVrOBiItmmM8wan2fm4oYqFuFfkXmlGpLQhPY8CA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/traverse': 7.25.6
      '@babel/types': 7.25.6
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helper-module-imports@7.24.7:
    resolution: {integrity: sha512-8AyH3C+74cgCVVXow/myrynrAGv+nTVg5vKu2nZph9x7RcRwzmh0VFallJuFTZ9mx6u4eSdXZfcOzSqTUm0HCA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/traverse': 7.25.6
      '@babel/types': 7.25.6
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helper-module-transforms@7.25.2(@babel/core@7.25.2):
    resolution: {integrity: sha512-BjyRAbix6j/wv83ftcVJmBt72QtHI56C7JXZoG2xATiLpmoC7dpd8WnkikExHDVPpi/3qCmO6WY1EaXOluiecQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-module-imports': 7.24.7
      '@babel/helper-simple-access': 7.24.7
      '@babel/helper-validator-identifier': 7.24.7
      '@babel/traverse': 7.25.6
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helper-optimise-call-expression@7.24.7:
    resolution: {integrity: sha512-jKiTsW2xmWwxT1ixIdfXUZp+P5yURx2suzLZr5Hi64rURpDYdMW0pv+Uf17EYk2Rd428Lx4tLsnjGJzYKDM/6A==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.25.6
    dev: false

  /@babel/helper-plugin-utils@7.24.8:
    resolution: {integrity: sha512-FFWx5142D8h2Mgr/iPVGH5G7w6jDn4jUSpZTyDnQO0Yn7Ks2Kuz6Pci8H6MPCoUJegd/UZQ3tAvfLCxQSnWWwg==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/helper-remap-async-to-generator@7.25.0(@babel/core@7.25.2):
    resolution: {integrity: sha512-NhavI2eWEIz/H9dbrG0TuOicDhNexze43i5z7lEqwYm0WEZVTwnPpA0EafUTP7+6/W79HWIP2cTe3Z5NiSTVpw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-annotate-as-pure': 7.24.7
      '@babel/helper-wrap-function': 7.25.0
      '@babel/traverse': 7.25.6
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helper-replace-supers@7.25.0(@babel/core@7.25.2):
    resolution: {integrity: sha512-q688zIvQVYtZu+i2PsdIu/uWGRpfxzr5WESsfpShfZECkO+d2o+WROWezCi/Q6kJ0tfPa5+pUGUlfx2HhrA3Bg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-member-expression-to-functions': 7.24.8
      '@babel/helper-optimise-call-expression': 7.24.7
      '@babel/traverse': 7.25.6
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helper-simple-access@7.24.7:
    resolution: {integrity: sha512-zBAIvbCMh5Ts+b86r/CjU+4XGYIs+R1j951gxI3KmmxBMhCg4oQMsv6ZXQ64XOm/cvzfU1FmoCyt6+owc5QMYg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/traverse': 7.25.6
      '@babel/types': 7.25.6
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helper-skip-transparent-expression-wrappers@7.24.7:
    resolution: {integrity: sha512-IO+DLT3LQUElMbpzlatRASEyQtfhSE0+m465v++3jyyXeBTBUjtVZg28/gHeV5mrTJqvEKhKroBGAvhW+qPHiQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/traverse': 7.25.6
      '@babel/types': 7.25.6
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helper-string-parser@7.24.8:
    resolution: {integrity: sha512-pO9KhhRcuUyGnJWwyEgnRJTSIZHiT+vMD0kPeD+so0l7mxkMT19g3pjY9GTnHySck/hDzq+dtW/4VgnMkippsQ==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/helper-validator-identifier@7.24.7:
    resolution: {integrity: sha512-rR+PBcQ1SMQDDyF6X0wxtG8QyLCgUB0eRAGguqRLfkCA87l7yAP7ehq8SNj96OOGTO8OBV70KhuFYcIkHXOg0w==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/helper-validator-option@7.24.8:
    resolution: {integrity: sha512-xb8t9tD1MHLungh/AIoWYN+gVHaB9kwlu8gffXGSt3FFEIT7RjS+xWbc2vUD1UTZdIpKj/ab3rdqJ7ufngyi2Q==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/helper-wrap-function@7.25.0:
    resolution: {integrity: sha512-s6Q1ebqutSiZnEjaofc/UKDyC4SbzV5n5SrA2Gq8UawLycr3i04f1dX4OzoQVnexm6aOCh37SQNYlJ/8Ku+PMQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': 7.25.0
      '@babel/traverse': 7.25.6
      '@babel/types': 7.25.6
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helpers@7.25.6:
    resolution: {integrity: sha512-Xg0tn4HcfTijTwfDwYlvVCl43V6h4KyVVX2aEm4qdO/PC6L2YvzLHFdmxhoeSA3eslcE6+ZVXHgWwopXYLNq4Q==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': 7.25.0
      '@babel/types': 7.25.6
    dev: false

  /@babel/highlight@7.24.7:
    resolution: {integrity: sha512-EStJpq4OuY8xYfhGVXngigBJRWxftKX9ksiGDnmlY3o7B/V7KIAc9X4oiK87uPJSc/vs5L869bem5fhZa8caZw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': 7.24.7
      chalk: 2.4.2
      js-tokens: 4.0.0
      picocolors: 1.1.0
    dev: false

  /@babel/parser@7.25.6:
    resolution: {integrity: sha512-trGdfBdbD0l1ZPmcJ83eNxB9rbEax4ALFTF7fN386TMYbeCQbyme5cOEXQhbGXKebwGaB/J52w1mrklMcbgy6Q==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': 7.25.6
    dev: false

  /@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.25.3(@babel/core@7.25.2):
    resolution: {integrity: sha512-wUrcsxZg6rqBXG05HG1FPYgsP6EvwF4WpBbxIpWIIYnH8wG0gzx3yZY3dtEHas4sTAOGkbTsc9EGPxwff8lRoA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/traverse': 7.25.6
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-bugfix-safari-class-field-initializer-scope@7.25.0(@babel/core@7.25.2):
    resolution: {integrity: sha512-Bm4bH2qsX880b/3ziJ8KD711LT7z4u8CFudmjqle65AZj/HNUFhEf90dqYv6O86buWvSBmeQDjv0Tn2aF/bIBA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.25.0(@babel/core@7.25.2):
    resolution: {integrity: sha512-lXwdNZtTmeVOOFtwM/WDe7yg1PL8sYhRk/XH0FzbR2HDQ0xC+EnQ/JHeoMYSavtU115tnUk0q9CDyq8si+LMAA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-+izXIbke1T33mY4MSNnrqhPXDz01WYhEf3yF5NbnUtkiNnm+XBZJl3kNfoK6NKmYlz/D07+l2GWVK/QfDkNCuQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.13.0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/helper-skip-transparent-expression-wrappers': 7.24.7
      '@babel/plugin-transform-optional-chaining': 7.24.8(@babel/core@7.25.2)
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.25.0(@babel/core@7.25.2):
    resolution: {integrity: sha512-tggFrk1AIShG/RUQbEwt2Tr/E+ObkfwrPjR6BjbRvsx24+PSjK8zrq0GWPNCjo8qpRx4DuJzlcvWJqlm+0h3kw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/traverse': 7.25.6
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-proposal-class-properties@7.18.6(@babel/core@7.25.2):
    resolution: {integrity: sha512-cumfXOF0+nzZrrN8Rf0t7M+tF6sZc7vhQwYQck9q1/5w2OExlD+b4v4RpMJFaV1Z7WcDRgO6FqvxqxGlwo+RHQ==}
    engines: {node: '>=6.9.0'}
    deprecated: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead.
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-create-class-features-plugin': 7.25.4(@babel/core@7.25.2)
      '@babel/helper-plugin-utils': 7.24.8
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-proposal-decorators@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-RL9GR0pUG5Kc8BUWLNDm2T5OpYwSX15r98I0IkgmRQTXuELq/OynH8xtMTMvTJFjXbMWFVTKtYkTaYQsuAwQlQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-create-class-features-plugin': 7.25.4(@babel/core@7.25.2)
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/plugin-syntax-decorators': 7.24.7(@babel/core@7.25.2)
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-proposal-nullish-coalescing-operator@7.18.6(@babel/core@7.25.2):
    resolution: {integrity: sha512-wQxQzxYeJqHcfppzBDnm1yAY0jSRkUXR2z8RePZYrKwMKgMlE8+Z6LUno+bd6LvbGh8Gltvy74+9pIYkr+XkKA==}
    engines: {node: '>=6.9.0'}
    deprecated: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-nullish-coalescing-operator instead.
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3(@babel/core@7.25.2)
    dev: false

  /@babel/plugin-proposal-optional-chaining@7.21.0(@babel/core@7.25.2):
    resolution: {integrity: sha512-p4zeefM72gpmEe2fkUr/OnOXpWEf8nAgk7ZYVqqfFiyIG7oFfVZcCrU64hWn5xp4tQ9LkV4bTIa5rD0KANpKNA==}
    engines: {node: '>=6.9.0'}
    deprecated: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-optional-chaining instead.
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/helper-skip-transparent-expression-wrappers': 7.24.7
      '@babel/plugin-syntax-optional-chaining': 7.8.3(@babel/core@7.25.2)
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-proposal-private-methods@7.18.6(@babel/core@7.25.2):
    resolution: {integrity: sha512-nutsvktDItsNn4rpGItSNV2sz1XwS+nfU0Rg8aCx3W3NOKVzdMjJRu0O5OkgDp3ZGICSTbgRpxZoWsxoKRvbeA==}
    engines: {node: '>=6.9.0'}
    deprecated: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-methods instead.
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-create-class-features-plugin': 7.25.4(@babel/core@7.25.2)
      '@babel/helper-plugin-utils': 7.24.8
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.25.2):
    resolution: {integrity: sha512-SOSkfJDddaM7mak6cPEpswyTRnuRltl429hMraQEglW+OkovnCzsiszTmsrlY//qLFjCpQDFRvjdm2wA5pPm9w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
    dev: false

  /@babel/plugin-proposal-private-property-in-object@7.21.11(@babel/core@7.25.2):
    resolution: {integrity: sha512-0QZ8qP/***********************************************************+vzwTAg/sMWVNeWeNyaw==}
    engines: {node: '>=6.9.0'}
    deprecated: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead.
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-annotate-as-pure': 7.24.7
      '@babel/helper-create-class-features-plugin': 7.25.4(@babel/core@7.25.2)
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/plugin-syntax-private-property-in-object': 7.14.5(@babel/core@7.25.2)
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.25.2):
    resolution: {integrity: sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.25.2):
    resolution: {integrity: sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.25.2):
    resolution: {integrity: sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-syntax-decorators@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-Ui4uLJJrRV1lb38zg1yYTmRKmiZLiftDEvZN2iq3kd9kUFU+PttmzTbAFC2ucRk/XJmtek6G23gPsuZbhrT8fQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-syntax-dynamic-import@7.8.3(@babel/core@7.25.2):
    resolution: {integrity: sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-syntax-export-namespace-from@7.8.3(@babel/core@7.25.2):
    resolution: {integrity: sha512-MXf5laXo6c1IbEbegDmzGPwGNTsHZmEy6QGznu5Sh2UCWvueywb2ee+CCE4zQiZstxU9BMoQO9i6zUFSY0Kj0Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-syntax-import-assertions@7.25.6(@babel/core@7.25.2):
    resolution: {integrity: sha512-aABl0jHw9bZ2karQ/uUD6XP4u0SG22SJrOHFoL6XB1R7dTovOP4TzTlsxOYC5yQ1pdscVK2JTUnF6QL3ARoAiQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-syntax-import-attributes@7.25.6(@babel/core@7.25.2):
    resolution: {integrity: sha512-sXaDXaJN9SNLymBdlWFA+bjzBhFD617ZaFiY13dGt7TVslVvVgA6fkZOP7Ki3IGElC45lwHdOTrCtKZGVAWeLQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.25.2):
    resolution: {integrity: sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.25.2):
    resolution: {integrity: sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-syntax-jsx@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-6ddciUPe/mpMnOKv/U+RSd2vvVy+Yw/JfBB0ZHYjEZt9NLHmCUylNYlsbqCCS1Bffjlb0fCwC9Vqz+sBz6PsiQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.25.2):
    resolution: {integrity: sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.25.2):
    resolution: {integrity: sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.25.2):
    resolution: {integrity: sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.25.2):
    resolution: {integrity: sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.25.2):
    resolution: {integrity: sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.25.2):
    resolution: {integrity: sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.25.2):
    resolution: {integrity: sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.25.2):
    resolution: {integrity: sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.25.2):
    resolution: {integrity: sha512-727YkEAPwSIQTv5im8QHz3upqp92JTWhidIC81Tdx4VJYIte/VndKf1qKrfnnhPLiPghStWfvC/iFaMCQu7Nqg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-create-regexp-features-plugin': 7.25.2(@babel/core@7.25.2)
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-transform-arrow-functions@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-Dt9LQs6iEY++gXUwY03DNFat5C2NbO48jj+j/bSAz6b3HgPs39qcPiYt77fDObIcFwj3/C2ICX9YMwGflUoSHQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-transform-async-generator-functions@7.25.4(@babel/core@7.25.2):
    resolution: {integrity: sha512-jz8cV2XDDTqjKPwVPJBIjORVEmSGYhdRa8e5k5+vN+uwcjSrSxUaebBRa4ko1jqNF2uxyg8G6XYk30Jv285xzg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/helper-remap-async-to-generator': 7.25.0(@babel/core@7.25.2)
      '@babel/plugin-syntax-async-generators': 7.8.4(@babel/core@7.25.2)
      '@babel/traverse': 7.25.6
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-async-to-generator@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-SQY01PcJfmQ+4Ash7NE+rpbLFbmqA2GPIgqzxfFTL4t1FKRq4zTms/7htKpoCUI9OcFYgzqfmCdH53s6/jn5fA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-module-imports': 7.24.7
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/helper-remap-async-to-generator': 7.25.0(@babel/core@7.25.2)
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-block-scoped-functions@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-yO7RAz6EsVQDaBH18IDJcMB1HnrUn2FJ/Jslc/WtPPWcjhpUJXU/rjbwmluzp7v/ZzWcEhTMXELnnsz8djWDwQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-transform-block-scoping@7.25.0(@babel/core@7.25.2):
    resolution: {integrity: sha512-yBQjYoOjXlFv9nlXb3f1casSHOZkWr29NX+zChVanLg5Nc157CrbEX9D7hxxtTpuFy7Q0YzmmWfJxzvps4kXrQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-transform-class-properties@7.25.4(@babel/core@7.25.2):
    resolution: {integrity: sha512-nZeZHyCWPfjkdU5pA/uHiTaDAFUEqkpzf1YoQT2NeSynCGYq9rxfyI3XpQbfx/a0hSnFH6TGlEXvae5Vi7GD8g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-create-class-features-plugin': 7.25.4(@babel/core@7.25.2)
      '@babel/helper-plugin-utils': 7.24.8
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-class-static-block@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-HMXK3WbBPpZQufbMG4B46A90PkuuhN9vBCb5T8+VAHqvAqvcLi+2cKoukcpmUYkszLhScU3l1iudhrks3DggRQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.12.0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-create-class-features-plugin': 7.25.4(@babel/core@7.25.2)
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/plugin-syntax-class-static-block': 7.14.5(@babel/core@7.25.2)
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-classes@7.25.4(@babel/core@7.25.2):
    resolution: {integrity: sha512-oexUfaQle2pF/b6E0dwsxQtAol9TLSO88kQvym6HHBWFliV2lGdrPieX+WgMRLSJDVzdYywk7jXbLPuO2KLTLg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-annotate-as-pure': 7.24.7
      '@babel/helper-compilation-targets': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/helper-replace-supers': 7.25.0(@babel/core@7.25.2)
      '@babel/traverse': 7.25.6
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-computed-properties@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-25cS7v+707Gu6Ds2oY6tCkUwsJ9YIDbggd9+cu9jzzDgiNq7hR/8dkzxWfKWnTic26vsI3EsCXNd4iEB6e8esQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/template': 7.25.0
    dev: false

  /@babel/plugin-transform-destructuring@7.24.8(@babel/core@7.25.2):
    resolution: {integrity: sha512-36e87mfY8TnRxc7yc6M9g9gOB7rKgSahqkIKwLpz4Ppk2+zC2Cy1is0uwtuSG6AE4zlTOUa+7JGz9jCJGLqQFQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-transform-dotall-regex@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-ZOA3W+1RRTSWvyqcMJDLqbchh7U4NRGqwRfFSVbOLS/ePIP4vHB5e8T8eXcuqyN1QkgKyj5wuW0lcS85v4CrSw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-create-regexp-features-plugin': 7.25.2(@babel/core@7.25.2)
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-transform-duplicate-keys@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-JdYfXyCRihAe46jUIliuL2/s0x0wObgwwiGxw/UbgJBr20gQBThrokO4nYKgWkD7uBaqM7+9x5TU7NkExZJyzw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.25.0(@babel/core@7.25.2):
    resolution: {integrity: sha512-YLpb4LlYSc3sCUa35un84poXoraOiQucUTTu8X1j18JV+gNa8E0nyUf/CjZ171IRGr4jEguF+vzJU66QZhn29g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-create-regexp-features-plugin': 7.25.2(@babel/core@7.25.2)
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-transform-dynamic-import@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-sc3X26PhZQDb3JhORmakcbvkeInvxz+A8oda99lj7J60QRuPZvNAk9wQlTBS1ZynelDrDmTU4pw1tyc5d5ZMUg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/plugin-syntax-dynamic-import': 7.8.3(@babel/core@7.25.2)
    dev: false

  /@babel/plugin-transform-exponentiation-operator@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-Rqe/vSc9OYgDajNIK35u7ot+KeCoetqQYFXM4Epf7M7ez3lWlOjrDjrwMei6caCVhfdw+mIKD4cgdGNy5JQotQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-builder-binary-assignment-operator-visitor': 7.24.7
      '@babel/helper-plugin-utils': 7.24.8
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-export-namespace-from@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-v0K9uNYsPL3oXZ/7F9NNIbAj2jv1whUEtyA6aujhekLs56R++JDQuzRcP2/z4WX5Vg/c5lE9uWZA0/iUoFhLTA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/plugin-syntax-export-namespace-from': 7.8.3(@babel/core@7.25.2)
    dev: false

  /@babel/plugin-transform-for-of@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-wo9ogrDG1ITTTBsy46oGiN1dS9A7MROBTcYsfS8DtsImMkHk9JXJ3EWQM6X2SUw4x80uGPlwj0o00Uoc6nEE3g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/helper-skip-transparent-expression-wrappers': 7.24.7
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-function-name@7.25.1(@babel/core@7.25.2):
    resolution: {integrity: sha512-TVVJVdW9RKMNgJJlLtHsKDTydjZAbwIsn6ySBPQaEAUU5+gVvlJt/9nRmqVbsV/IBanRjzWoaAQKLoamWVOUuA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-compilation-targets': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/traverse': 7.25.6
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-json-strings@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-2yFnBGDvRuxAaE/f0vfBKvtnvvqU8tGpMHqMNpTN2oWMKIR3NqFkjaAgGwawhqK/pIN2T3XdjGPdaG0vDhOBGw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/plugin-syntax-json-strings': 7.8.3(@babel/core@7.25.2)
    dev: false

  /@babel/plugin-transform-literals@7.25.2(@babel/core@7.25.2):
    resolution: {integrity: sha512-HQI+HcTbm9ur3Z2DkO+jgESMAMcYLuN/A7NRw9juzxAezN9AvqvUTnpKP/9kkYANz6u7dFlAyOu44ejuGySlfw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-transform-logical-assignment-operators@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-4D2tpwlQ1odXmTEIFWy9ELJcZHqrStlzK/dAOWYyxX3zT0iXQB6banjgeOJQXzEc4S0E0a5A+hahxPaEFYftsw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4(@babel/core@7.25.2)
    dev: false

  /@babel/plugin-transform-member-expression-literals@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-T/hRC1uqrzXMKLQ6UCwMT85S3EvqaBXDGf0FaMf4446Qx9vKwlghvee0+uuZcDUCZU5RuNi4781UQ7R308zzBw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-transform-modules-amd@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-9+pB1qxV3vs/8Hdmz/CulFB8w2tuu6EB94JZFsjdqxQokwGa9Unap7Bo2gGBGIvPmDIVvQrom7r5m/TCDMURhg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-module-transforms': 7.25.2(@babel/core@7.25.2)
      '@babel/helper-plugin-utils': 7.24.8
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-modules-commonjs@7.24.8(@babel/core@7.25.2):
    resolution: {integrity: sha512-WHsk9H8XxRs3JXKWFiqtQebdh9b/pTk4EgueygFzYlTKAg0Ud985mSevdNjdXdFBATSKVJGQXP1tv6aGbssLKA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-module-transforms': 7.25.2(@babel/core@7.25.2)
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/helper-simple-access': 7.24.7
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-modules-systemjs@7.25.0(@babel/core@7.25.2):
    resolution: {integrity: sha512-YPJfjQPDXxyQWg/0+jHKj1llnY5f/R6a0p/vP4lPymxLu7Lvl4k2WMitqi08yxwQcCVUUdG9LCUj4TNEgAp3Jw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-module-transforms': 7.25.2(@babel/core@7.25.2)
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/helper-validator-identifier': 7.24.7
      '@babel/traverse': 7.25.6
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-modules-umd@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-3aytQvqJ/h9z4g8AsKPLvD4Zqi2qT+L3j7XoFFu1XBlZWEl2/1kWnhmAbxpLgPrHSY0M6UA02jyTiwUVtiKR6A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-module-transforms': 7.25.2(@babel/core@7.25.2)
      '@babel/helper-plugin-utils': 7.24.8
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-named-capturing-groups-regex@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-/jr7h/EWeJtk1U/uz2jlsCioHkZk1JJZVcc8oQsJ1dUlaJD83f4/6Zeh2aHt9BIFokHIsSeDfhUmju0+1GPd6g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-create-regexp-features-plugin': 7.25.2(@babel/core@7.25.2)
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-transform-new-target@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-RNKwfRIXg4Ls/8mMTza5oPF5RkOW8Wy/WgMAp1/F1yZ8mMbtwXW+HDoJiOsagWrAhI5f57Vncrmr9XeT4CVapA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-transform-nullish-coalescing-operator@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-Ts7xQVk1OEocqzm8rHMXHlxvsfZ0cEF2yomUqpKENHWMF4zKk175Y4q8H5knJes6PgYad50uuRmt3UJuhBw8pQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3(@babel/core@7.25.2)
    dev: false

  /@babel/plugin-transform-numeric-separator@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-e6q1TiVUzvH9KRvicuxdBTUj4AdKSRwzIyFFnfnezpCfP2/7Qmbb8qbU2j7GODbl4JMkblitCQjKYUaX/qkkwA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/plugin-syntax-numeric-separator': 7.10.4(@babel/core@7.25.2)
    dev: false

  /@babel/plugin-transform-object-rest-spread@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-4QrHAr0aXQCEFni2q4DqKLD31n2DL+RxcwnNjDFkSG0eNQ/xCavnRkfCUjsyqGC2OviNJvZOF/mQqZBw7i2C5Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-compilation-targets': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/plugin-syntax-object-rest-spread': 7.8.3(@babel/core@7.25.2)
      '@babel/plugin-transform-parameters': 7.24.7(@babel/core@7.25.2)
    dev: false

  /@babel/plugin-transform-object-super@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-A/vVLwN6lBrMFmMDmPPz0jnE6ZGx7Jq7d6sT/Ev4H65RER6pZ+kczlf1DthF5N0qaPHBsI7UXiE8Zy66nmAovg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/helper-replace-supers': 7.25.0(@babel/core@7.25.2)
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-optional-catch-binding@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-uLEndKqP5BfBbC/5jTwPxLh9kqPWWgzN/f8w6UwAIirAEqiIVJWWY312X72Eub09g5KF9+Zn7+hT7sDxmhRuKA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3(@babel/core@7.25.2)
    dev: false

  /@babel/plugin-transform-optional-chaining@7.24.8(@babel/core@7.25.2):
    resolution: {integrity: sha512-5cTOLSMs9eypEy8JUVvIKOu6NgvbJMnpG62VpIHrTmROdQ+L5mDAaI40g25k5vXti55JWNX5jCkq3HZxXBQANw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/helper-skip-transparent-expression-wrappers': 7.24.7
      '@babel/plugin-syntax-optional-chaining': 7.8.3(@babel/core@7.25.2)
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-parameters@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-yGWW5Rr+sQOhK0Ot8hjDJuxU3XLRQGflvT4lhlSY0DFvdb3TwKaY26CJzHtYllU0vT9j58hc37ndFPsqT1SrzA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-transform-private-methods@7.25.4(@babel/core@7.25.2):
    resolution: {integrity: sha512-ao8BG7E2b/URaUQGqN3Tlsg+M3KlHY6rJ1O1gXAEUnZoyNQnvKyH87Kfg+FoxSeyWUB8ISZZsC91C44ZuBFytw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-create-class-features-plugin': 7.25.4(@babel/core@7.25.2)
      '@babel/helper-plugin-utils': 7.24.8
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-private-property-in-object@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-9z76mxwnwFxMyxZWEgdgECQglF2Q7cFLm0kMf8pGwt+GSJsY0cONKj/UuO4bOH0w/uAel3ekS4ra5CEAyJRmDA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-annotate-as-pure': 7.24.7
      '@babel/helper-create-class-features-plugin': 7.25.4(@babel/core@7.25.2)
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/plugin-syntax-private-property-in-object': 7.14.5(@babel/core@7.25.2)
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-property-literals@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-EMi4MLQSHfd2nrCqQEWxFdha2gBCqU4ZcCng4WBGZ5CJL4bBRW0ptdqqDdeirGZcpALazVVNJqRmsO8/+oNCBA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-transform-regenerator@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-lq3fvXPdimDrlg6LWBoqj+r/DEWgONuwjuOuQCSYgRroXDH/IdM1C0IZf59fL5cHLpjEH/O6opIRBbqv7ELnuA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      regenerator-transform: 0.15.2
    dev: false

  /@babel/plugin-transform-reserved-words@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-0DUq0pHcPKbjFZCfTss/pGkYMfy3vFWydkUBd9r0GHpIyfs2eCDENvqadMycRS9wZCXR41wucAfJHJmwA0UmoQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-transform-runtime@7.25.4(@babel/core@7.25.2):
    resolution: {integrity: sha512-8hsyG+KUYGY0coX6KUCDancA0Vw225KJ2HJO0yCNr1vq5r+lJTleDaJf0K7iOhjw4SWhu03TMBzYTJ9krmzULQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-module-imports': 7.24.7
      '@babel/helper-plugin-utils': 7.24.8
      babel-plugin-polyfill-corejs2: 0.4.11(@babel/core@7.25.2)
      babel-plugin-polyfill-corejs3: 0.10.6(@babel/core@7.25.2)
      babel-plugin-polyfill-regenerator: 0.6.2(@babel/core@7.25.2)
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-shorthand-properties@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-KsDsevZMDsigzbA09+vacnLpmPH4aWjcZjXdyFKGzpplxhbeB4wYtury3vglQkg6KM/xEPKt73eCjPPf1PgXBA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-transform-spread@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-x96oO0I09dgMDxJaANcRyD4ellXFLLiWhuwDxKZX5g2rWP1bTPkBSwCYv96VDXVT1bD9aPj8tppr5ITIh8hBng==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/helper-skip-transparent-expression-wrappers': 7.24.7
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-transform-sticky-regex@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-kHPSIJc9v24zEml5geKg9Mjx5ULpfncj0wRpYtxbvKyTtHCYDkVE3aHQ03FrpEo4gEe2vrJJS1Y9CJTaThA52g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-transform-template-literals@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-AfDTQmClklHCOLxtGoP7HkeMw56k1/bTQjwsfhL6pppo/M4TOBSq+jjBUBLmV/4oeFg4GWMavIl44ZeCtmmZTw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-transform-typeof-symbol@7.24.8(@babel/core@7.25.2):
    resolution: {integrity: sha512-adNTUpDCVnmAE58VEqKlAA6ZBlNkMnWD0ZcW76lyNFN3MJniyGFZfNwERVk8Ap56MCnXztmDr19T4mPTztcuaw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-transform-unicode-escapes@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-U3ap1gm5+4edc2Q/P+9VrBNhGkfnf+8ZqppY71Bo/pzZmXhhLdqgaUl6cuB07O1+AQJtCLfaOmswiNbSQ9ivhw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-transform-unicode-property-regex@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-uH2O4OV5M9FZYQrwc7NdVmMxQJOCCzFeYudlZSzUAHRFeOujQefa92E74TQDVskNHCzOXoigEuoyzHDhaEaK5w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-create-regexp-features-plugin': 7.25.2(@babel/core@7.25.2)
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-transform-unicode-regex@7.24.7(@babel/core@7.25.2):
    resolution: {integrity: sha512-hlQ96MBZSAXUq7ltkjtu3FJCCSMx/j629ns3hA3pXnBXjanNP0LHi+JpPeA81zaWgVK1VGH95Xuy7u0RyQ8kMg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-create-regexp-features-plugin': 7.25.2(@babel/core@7.25.2)
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/plugin-transform-unicode-sets-regex@7.25.4(@babel/core@7.25.2):
    resolution: {integrity: sha512-qesBxiWkgN1Q+31xUE9RcMk79eOXXDCv6tfyGMRSs4RGlioSg2WVyQAm07k726cSE56pa+Kb0y9epX2qaXzTvA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-create-regexp-features-plugin': 7.25.2(@babel/core@7.25.2)
      '@babel/helper-plugin-utils': 7.24.8
    dev: false

  /@babel/preset-env@7.25.4(@babel/core@7.25.2):
    resolution: {integrity: sha512-W9Gyo+KmcxjGahtt3t9fb14vFRWvPpu5pT6GBlovAK6BTBcxgjfVMSQCfJl4oi35ODrxP6xx2Wr8LNST57Mraw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/compat-data': 7.25.4
      '@babel/core': 7.25.2
      '@babel/helper-compilation-targets': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/helper-validator-option': 7.24.8
      '@babel/plugin-bugfix-firefox-class-in-computed-class-key': 7.25.3(@babel/core@7.25.2)
      '@babel/plugin-bugfix-safari-class-field-initializer-scope': 7.25.0(@babel/core@7.25.2)
      '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': 7.25.0(@babel/core@7.25.2)
      '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': 7.25.0(@babel/core@7.25.2)
      '@babel/plugin-proposal-private-property-in-object': 7.21.0-placeholder-for-preset-env.2(@babel/core@7.25.2)
      '@babel/plugin-syntax-async-generators': 7.8.4(@babel/core@7.25.2)
      '@babel/plugin-syntax-class-properties': 7.12.13(@babel/core@7.25.2)
      '@babel/plugin-syntax-class-static-block': 7.14.5(@babel/core@7.25.2)
      '@babel/plugin-syntax-dynamic-import': 7.8.3(@babel/core@7.25.2)
      '@babel/plugin-syntax-export-namespace-from': 7.8.3(@babel/core@7.25.2)
      '@babel/plugin-syntax-import-assertions': 7.25.6(@babel/core@7.25.2)
      '@babel/plugin-syntax-import-attributes': 7.25.6(@babel/core@7.25.2)
      '@babel/plugin-syntax-import-meta': 7.10.4(@babel/core@7.25.2)
      '@babel/plugin-syntax-json-strings': 7.8.3(@babel/core@7.25.2)
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4(@babel/core@7.25.2)
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3(@babel/core@7.25.2)
      '@babel/plugin-syntax-numeric-separator': 7.10.4(@babel/core@7.25.2)
      '@babel/plugin-syntax-object-rest-spread': 7.8.3(@babel/core@7.25.2)
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3(@babel/core@7.25.2)
      '@babel/plugin-syntax-optional-chaining': 7.8.3(@babel/core@7.25.2)
      '@babel/plugin-syntax-private-property-in-object': 7.14.5(@babel/core@7.25.2)
      '@babel/plugin-syntax-top-level-await': 7.14.5(@babel/core@7.25.2)
      '@babel/plugin-syntax-unicode-sets-regex': 7.18.6(@babel/core@7.25.2)
      '@babel/plugin-transform-arrow-functions': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-async-generator-functions': 7.25.4(@babel/core@7.25.2)
      '@babel/plugin-transform-async-to-generator': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-block-scoped-functions': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-block-scoping': 7.25.0(@babel/core@7.25.2)
      '@babel/plugin-transform-class-properties': 7.25.4(@babel/core@7.25.2)
      '@babel/plugin-transform-class-static-block': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-classes': 7.25.4(@babel/core@7.25.2)
      '@babel/plugin-transform-computed-properties': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-destructuring': 7.24.8(@babel/core@7.25.2)
      '@babel/plugin-transform-dotall-regex': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-duplicate-keys': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-duplicate-named-capturing-groups-regex': 7.25.0(@babel/core@7.25.2)
      '@babel/plugin-transform-dynamic-import': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-exponentiation-operator': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-export-namespace-from': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-for-of': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-function-name': 7.25.1(@babel/core@7.25.2)
      '@babel/plugin-transform-json-strings': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-literals': 7.25.2(@babel/core@7.25.2)
      '@babel/plugin-transform-logical-assignment-operators': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-member-expression-literals': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-modules-amd': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-modules-commonjs': 7.24.8(@babel/core@7.25.2)
      '@babel/plugin-transform-modules-systemjs': 7.25.0(@babel/core@7.25.2)
      '@babel/plugin-transform-modules-umd': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-named-capturing-groups-regex': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-new-target': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-nullish-coalescing-operator': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-numeric-separator': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-object-rest-spread': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-object-super': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-optional-catch-binding': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-optional-chaining': 7.24.8(@babel/core@7.25.2)
      '@babel/plugin-transform-parameters': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-private-methods': 7.25.4(@babel/core@7.25.2)
      '@babel/plugin-transform-private-property-in-object': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-property-literals': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-regenerator': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-reserved-words': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-shorthand-properties': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-spread': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-sticky-regex': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-template-literals': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-typeof-symbol': 7.24.8(@babel/core@7.25.2)
      '@babel/plugin-transform-unicode-escapes': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-unicode-property-regex': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-unicode-regex': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-transform-unicode-sets-regex': 7.25.4(@babel/core@7.25.2)
      '@babel/preset-modules': 0.1.6-no-external-plugins(@babel/core@7.25.2)
      babel-plugin-polyfill-corejs2: 0.4.11(@babel/core@7.25.2)
      babel-plugin-polyfill-corejs3: 0.10.6(@babel/core@7.25.2)
      babel-plugin-polyfill-regenerator: 0.6.2(@babel/core@7.25.2)
      core-js-compat: 3.38.1
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/preset-modules@0.1.6-no-external-plugins(@babel/core@7.25.2):
    resolution: {integrity: sha512-HrcgcIESLm9aIR842yhJ5RWan/gebQUJ6E/E5+rf0y9o6oj7w0Br+sWuL6kEQ/o/AdfvR1Je9jG18/gnpwjEyA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/types': 7.25.6
      esutils: 2.0.3
    dev: false

  /@babel/regjsgen@0.8.0:
    resolution: {integrity: sha512-x/rqGMdzj+fWZvCOYForTghzbtqPDZ5gPwaoNGHdgDfF2QA/XZbCBp4Moo5scrkAMPhB7z26XM/AaHuIJdgauA==}
    dev: false

  /@babel/runtime@7.25.6:
    resolution: {integrity: sha512-VBj9MYyDb9tuLq7yzqjgzt6Q+IBQLrGZfdjOekyEirZPHxXWoTSGUTMrpsfi58Up73d13NfYLv8HT9vmznjzhQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      regenerator-runtime: 0.14.1
    dev: false

  /@babel/template@7.25.0:
    resolution: {integrity: sha512-aOOgh1/5XzKvg1jvVz7AVrx2piJ2XBi227DHmbY6y+bM9H2FlN+IfecYu4Xl0cNiiVejlsCri89LUsbj8vJD9Q==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.24.7
      '@babel/parser': 7.25.6
      '@babel/types': 7.25.6
    dev: false

  /@babel/traverse@7.25.6:
    resolution: {integrity: sha512-9Vrcx5ZW6UwK5tvqsj0nGpp/XzqthkT0dqIc9g1AdtygFToNtTF67XzYS//dm+SAK9cp3B9R4ZO/46p63SCjlQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.24.7
      '@babel/generator': 7.25.6
      '@babel/parser': 7.25.6
      '@babel/template': 7.25.0
      '@babel/types': 7.25.6
      debug: 4.3.7
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/types@7.25.6:
    resolution: {integrity: sha512-/l42B1qxpG6RdfYf343Uw1vmDjeNhneUXtzhojE7pDgfpEypmRhI6j1kr17XCVv4Cgl9HdAiQY2x0GwKm7rWCw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-string-parser': 7.24.8
      '@babel/helper-validator-identifier': 7.24.7
      to-fast-properties: 2.0.0
    dev: false

  /@bachdgvn/vue-otp-input@1.0.8:
    resolution: {integrity: sha512-yZJD3ONjwEfwrgvP1M+aMYK8VirMzsU4mUihz0SQokda4KX7zFPiWMUP0JNYvpomkrhLMXvFQaA+DsTmrfHSUg==}
    dependencies:
      vue: 2.7.16
    dev: false

  /@ckeditor/ckeditor5-build-classic@19.0.2:
    resolution: {integrity: sha512-yz2M9Q6jMy9zF+JiQiSoaG/m7Eu48H+Yi3FgtVGDFpLDqGUJntNsW26g3qZld5Pa4pgVPRmDcfKHzLTWAo3iRg==}
    engines: {node: '>=8.0.0', npm: '>=5.7.1'}
    dev: false

  /@ckeditor/ckeditor5-vue@1.0.3:
    resolution: {integrity: sha512-8pYcWkOSUTZ4KD6nT2JbD4rQycvhCaoOAJeOiaSrjWb3X/xRWoDEOOJzXC6VRk9AeNE1UYY9wKBAsq3VoR5V9w==}
    engines: {node: '>=8.0.0', npm: '>=5.7.1'}
    dev: false

  /@csstools/cascade-layer-name-parser@1.0.13(@csstools/css-parser-algorithms@2.7.1)(@csstools/css-tokenizer@2.4.1):
    resolution: {integrity: sha512-MX0yLTwtZzr82sQ0zOjqimpZbzjMaK/h2pmlrLK7DCzlmiZLYFpoO94WmN1akRVo6ll/TdpHb53vihHLUMyvng==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      '@csstools/css-parser-algorithms': ^2.7.1
      '@csstools/css-tokenizer': ^2.4.1
    dependencies:
      '@csstools/css-parser-algorithms': 2.7.1(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-tokenizer': 2.4.1
    dev: false

  /@csstools/color-helpers@4.2.1:
    resolution: {integrity: sha512-CEypeeykO9AN7JWkr1OEOQb0HRzZlPWGwV0Ya6DuVgFdDi6g3ma/cPZ5ZPZM4AWQikDpq/0llnGGlIL+j8afzw==}
    engines: {node: ^14 || ^16 || >=18}
    dev: false

  /@csstools/css-calc@1.2.4(@csstools/css-parser-algorithms@2.7.1)(@csstools/css-tokenizer@2.4.1):
    resolution: {integrity: sha512-tfOuvUQeo7Hz+FcuOd3LfXVp+342pnWUJ7D2y8NUpu1Ww6xnTbHLpz018/y6rtbHifJ3iIEf9ttxXd8KG7nL0Q==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      '@csstools/css-parser-algorithms': ^2.7.1
      '@csstools/css-tokenizer': ^2.4.1
    dependencies:
      '@csstools/css-parser-algorithms': 2.7.1(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-tokenizer': 2.4.1
    dev: false

  /@csstools/css-color-parser@2.0.5(@csstools/css-parser-algorithms@2.7.1)(@csstools/css-tokenizer@2.4.1):
    resolution: {integrity: sha512-lRZSmtl+DSjok3u9hTWpmkxFZnz7stkbZxzKc08aDUsdrWwhSgWo8yq9rq9DaFUtbAyAq2xnH92fj01S+pwIww==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      '@csstools/css-parser-algorithms': ^2.7.1
      '@csstools/css-tokenizer': ^2.4.1
    dependencies:
      '@csstools/color-helpers': 4.2.1
      '@csstools/css-calc': 1.2.4(@csstools/css-parser-algorithms@2.7.1)(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-parser-algorithms': 2.7.1(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-tokenizer': 2.4.1
    dev: false

  /@csstools/css-parser-algorithms@2.7.1(@csstools/css-tokenizer@2.4.1):
    resolution: {integrity: sha512-2SJS42gxmACHgikc1WGesXLIT8d/q2l0UFM7TaEeIzdFCE/FPMtTiizcPGGJtlPo2xuQzY09OhrLTzRxqJqwGw==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      '@csstools/css-tokenizer': ^2.4.1
    dependencies:
      '@csstools/css-tokenizer': 2.4.1
    dev: false

  /@csstools/css-tokenizer@2.4.1:
    resolution: {integrity: sha512-eQ9DIktFJBhGjioABJRtUucoWR2mwllurfnM8LuNGAqX3ViZXaUchqk+1s7jjtkFiT9ySdACsFEA3etErkALUg==}
    engines: {node: ^14 || ^16 || >=18}
    dev: false

  /@csstools/media-query-list-parser@2.1.13(@csstools/css-parser-algorithms@2.7.1)(@csstools/css-tokenizer@2.4.1):
    resolution: {integrity: sha512-XaHr+16KRU9Gf8XLi3q8kDlI18d5vzKSKCY510Vrtc9iNR0NJzbY9hhTmwhzYZj/ZwGL4VmB3TA9hJW0Um2qFA==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      '@csstools/css-parser-algorithms': ^2.7.1
      '@csstools/css-tokenizer': ^2.4.1
    dependencies:
      '@csstools/css-parser-algorithms': 2.7.1(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-tokenizer': 2.4.1
    dev: false

  /@csstools/postcss-cascade-layers@4.0.6(postcss@8.4.47):
    resolution: {integrity: sha512-Xt00qGAQyqAODFiFEJNkTpSUz5VfYqnDLECdlA/Vv17nl/OIV5QfTRHGAXrBGG5YcJyHpJ+GF9gF/RZvOQz4oA==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/selector-specificity': 3.1.1(postcss-selector-parser@6.1.2)
      postcss: 8.4.47
      postcss-selector-parser: 6.1.2
    dev: false

  /@csstools/postcss-color-function@3.0.19(postcss@8.4.47):
    resolution: {integrity: sha512-d1OHEXyYGe21G3q88LezWWx31ImEDdmINNDy0LyLNN9ChgN2bPxoubUPiHf9KmwypBMaHmNcMuA/WZOKdZk/Lg==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/css-color-parser': 2.0.5(@csstools/css-parser-algorithms@2.7.1)(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-parser-algorithms': 2.7.1(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-tokenizer': 2.4.1
      '@csstools/postcss-progressive-custom-properties': 3.3.0(postcss@8.4.47)
      '@csstools/utilities': 1.0.0(postcss@8.4.47)
      postcss: 8.4.47
    dev: false

  /@csstools/postcss-color-mix-function@2.0.19(postcss@8.4.47):
    resolution: {integrity: sha512-mLvQlMX+keRYr16AuvuV8WYKUwF+D0DiCqlBdvhQ0KYEtcQl9/is9Ssg7RcIys8x0jIn2h1zstS4izckdZj9wg==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/css-color-parser': 2.0.5(@csstools/css-parser-algorithms@2.7.1)(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-parser-algorithms': 2.7.1(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-tokenizer': 2.4.1
      '@csstools/postcss-progressive-custom-properties': 3.3.0(postcss@8.4.47)
      '@csstools/utilities': 1.0.0(postcss@8.4.47)
      postcss: 8.4.47
    dev: false

  /@csstools/postcss-content-alt-text@1.0.0(postcss@8.4.47):
    resolution: {integrity: sha512-SkHdj7EMM/57GVvSxSELpUg7zb5eAndBeuvGwFzYtU06/QXJ/h9fuK7wO5suteJzGhm3GDF/EWPCdWV2h1IGHQ==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/css-parser-algorithms': 2.7.1(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-tokenizer': 2.4.1
      '@csstools/postcss-progressive-custom-properties': 3.3.0(postcss@8.4.47)
      '@csstools/utilities': 1.0.0(postcss@8.4.47)
      postcss: 8.4.47
    dev: false

  /@csstools/postcss-exponential-functions@1.0.9(postcss@8.4.47):
    resolution: {integrity: sha512-x1Avr15mMeuX7Z5RJUl7DmjhUtg+Amn5DZRD0fQ2TlTFTcJS8U1oxXQ9e5mA62S2RJgUU6db20CRoJyDvae2EQ==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/css-calc': 1.2.4(@csstools/css-parser-algorithms@2.7.1)(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-parser-algorithms': 2.7.1(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-tokenizer': 2.4.1
      postcss: 8.4.47
    dev: false

  /@csstools/postcss-font-format-keywords@3.0.2(postcss@8.4.47):
    resolution: {integrity: sha512-E0xz2sjm4AMCkXLCFvI/lyl4XO6aN1NCSMMVEOngFDJ+k2rDwfr6NDjWljk1li42jiLNChVX+YFnmfGCigZKXw==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/utilities': 1.0.0(postcss@8.4.47)
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /@csstools/postcss-gamut-mapping@1.0.11(postcss@8.4.47):
    resolution: {integrity: sha512-KrHGsUPXRYxboXmJ9wiU/RzDM7y/5uIefLWKFSc36Pok7fxiPyvkSHO51kh+RLZS1W5hbqw9qaa6+tKpTSxa5g==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/css-color-parser': 2.0.5(@csstools/css-parser-algorithms@2.7.1)(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-parser-algorithms': 2.7.1(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-tokenizer': 2.4.1
      postcss: 8.4.47
    dev: false

  /@csstools/postcss-gradients-interpolation-method@4.0.20(postcss@8.4.47):
    resolution: {integrity: sha512-ZFl2JBHano6R20KB5ZrB8KdPM2pVK0u+/3cGQ2T8VubJq982I2LSOvQ4/VtxkAXjkPkk1rXt4AD1ni7UjTZ1Og==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/css-color-parser': 2.0.5(@csstools/css-parser-algorithms@2.7.1)(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-parser-algorithms': 2.7.1(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-tokenizer': 2.4.1
      '@csstools/postcss-progressive-custom-properties': 3.3.0(postcss@8.4.47)
      '@csstools/utilities': 1.0.0(postcss@8.4.47)
      postcss: 8.4.47
    dev: false

  /@csstools/postcss-hwb-function@3.0.18(postcss@8.4.47):
    resolution: {integrity: sha512-3ifnLltR5C7zrJ+g18caxkvSRnu9jBBXCYgnBznRjxm6gQJGnnCO9H6toHfywNdNr/qkiVf2dymERPQLDnjLRQ==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/css-color-parser': 2.0.5(@csstools/css-parser-algorithms@2.7.1)(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-parser-algorithms': 2.7.1(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-tokenizer': 2.4.1
      '@csstools/postcss-progressive-custom-properties': 3.3.0(postcss@8.4.47)
      '@csstools/utilities': 1.0.0(postcss@8.4.47)
      postcss: 8.4.47
    dev: false

  /@csstools/postcss-ic-unit@3.0.7(postcss@8.4.47):
    resolution: {integrity: sha512-YoaNHH2wNZD+c+rHV02l4xQuDpfR8MaL7hD45iJyr+USwvr0LOheeytJ6rq8FN6hXBmEeoJBeXXgGmM8fkhH4g==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/postcss-progressive-custom-properties': 3.3.0(postcss@8.4.47)
      '@csstools/utilities': 1.0.0(postcss@8.4.47)
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /@csstools/postcss-initial@1.0.1(postcss@8.4.47):
    resolution: {integrity: sha512-wtb+IbUIrIf8CrN6MLQuFR7nlU5C7PwuebfeEXfjthUha1+XZj2RVi+5k/lukToA24sZkYAiSJfHM8uG/UZIdg==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.47
    dev: false

  /@csstools/postcss-is-pseudo-class@4.0.8(postcss@8.4.47):
    resolution: {integrity: sha512-0aj591yGlq5Qac+plaWCbn5cpjs5Sh0daovYUKJUOMjIp70prGH/XPLp7QjxtbFXz3CTvb0H9a35dpEuIuUi3Q==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/selector-specificity': 3.1.1(postcss-selector-parser@6.1.2)
      postcss: 8.4.47
      postcss-selector-parser: 6.1.2
    dev: false

  /@csstools/postcss-light-dark-function@1.0.8(postcss@8.4.47):
    resolution: {integrity: sha512-x0UtpCyVnERsplUeoaY6nEtp1HxTf4lJjoK/ULEm40DraqFfUdUSt76yoOyX5rGY6eeOUOkurHyYlFHVKv/pew==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/css-parser-algorithms': 2.7.1(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-tokenizer': 2.4.1
      '@csstools/postcss-progressive-custom-properties': 3.3.0(postcss@8.4.47)
      '@csstools/utilities': 1.0.0(postcss@8.4.47)
      postcss: 8.4.47
    dev: false

  /@csstools/postcss-logical-float-and-clear@2.0.1(postcss@8.4.47):
    resolution: {integrity: sha512-SsrWUNaXKr+e/Uo4R/uIsqJYt3DaggIh/jyZdhy/q8fECoJSKsSMr7nObSLdvoULB69Zb6Bs+sefEIoMG/YfOA==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.47
    dev: false

  /@csstools/postcss-logical-overflow@1.0.1(postcss@8.4.47):
    resolution: {integrity: sha512-Kl4lAbMg0iyztEzDhZuQw8Sj9r2uqFDcU1IPl+AAt2nue8K/f1i7ElvKtXkjhIAmKiy5h2EY8Gt/Cqg0pYFDCw==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.47
    dev: false

  /@csstools/postcss-logical-overscroll-behavior@1.0.1(postcss@8.4.47):
    resolution: {integrity: sha512-+kHamNxAnX8ojPCtV8WPcUP3XcqMFBSDuBuvT6MHgq7oX4IQxLIXKx64t7g9LiuJzE7vd06Q9qUYR6bh4YnGpQ==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.47
    dev: false

  /@csstools/postcss-logical-resize@2.0.1(postcss@8.4.47):
    resolution: {integrity: sha512-W5Gtwz7oIuFcKa5SmBjQ2uxr8ZoL7M2bkoIf0T1WeNqljMkBrfw1DDA8/J83k57NQ1kcweJEjkJ04pUkmyee3A==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /@csstools/postcss-logical-viewport-units@2.0.11(postcss@8.4.47):
    resolution: {integrity: sha512-ElITMOGcjQtvouxjd90WmJRIw1J7KMP+M+O87HaVtlgOOlDt1uEPeTeii8qKGe2AiedEp0XOGIo9lidbiU2Ogg==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/css-tokenizer': 2.4.1
      '@csstools/utilities': 1.0.0(postcss@8.4.47)
      postcss: 8.4.47
    dev: false

  /@csstools/postcss-media-minmax@1.1.8(postcss@8.4.47):
    resolution: {integrity: sha512-KYQCal2i7XPNtHAUxCECdrC7tuxIWQCW+s8eMYs5r5PaAiVTeKwlrkRS096PFgojdNCmHeG0Cb7njtuNswNf+w==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/css-calc': 1.2.4(@csstools/css-parser-algorithms@2.7.1)(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-parser-algorithms': 2.7.1(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-tokenizer': 2.4.1
      '@csstools/media-query-list-parser': 2.1.13(@csstools/css-parser-algorithms@2.7.1)(@csstools/css-tokenizer@2.4.1)
      postcss: 8.4.47
    dev: false

  /@csstools/postcss-media-queries-aspect-ratio-number-values@2.0.11(postcss@8.4.47):
    resolution: {integrity: sha512-YD6jrib20GRGQcnOu49VJjoAnQ/4249liuz7vTpy/JfgqQ1Dlc5eD4HPUMNLOw9CWey9E6Etxwf/xc/ZF8fECA==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/css-parser-algorithms': 2.7.1(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-tokenizer': 2.4.1
      '@csstools/media-query-list-parser': 2.1.13(@csstools/css-parser-algorithms@2.7.1)(@csstools/css-tokenizer@2.4.1)
      postcss: 8.4.47
    dev: false

  /@csstools/postcss-nested-calc@3.0.2(postcss@8.4.47):
    resolution: {integrity: sha512-ySUmPyawiHSmBW/VI44+IObcKH0v88LqFe0d09Sb3w4B1qjkaROc6d5IA3ll9kjD46IIX/dbO5bwFN/swyoyZA==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/utilities': 1.0.0(postcss@8.4.47)
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /@csstools/postcss-normalize-display-values@3.0.2(postcss@8.4.47):
    resolution: {integrity: sha512-fCapyyT/dUdyPtrelQSIV+d5HqtTgnNP/BEG9IuhgXHt93Wc4CfC1bQ55GzKAjWrZbgakMQ7MLfCXEf3rlZJOw==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /@csstools/postcss-oklab-function@3.0.19(postcss@8.4.47):
    resolution: {integrity: sha512-e3JxXmxjU3jpU7TzZrsNqSX4OHByRC3XjItV3Ieo/JEQmLg5rdOL4lkv/1vp27gXemzfNt44F42k/pn0FpE21Q==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/css-color-parser': 2.0.5(@csstools/css-parser-algorithms@2.7.1)(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-parser-algorithms': 2.7.1(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-tokenizer': 2.4.1
      '@csstools/postcss-progressive-custom-properties': 3.3.0(postcss@8.4.47)
      '@csstools/utilities': 1.0.0(postcss@8.4.47)
      postcss: 8.4.47
    dev: false

  /@csstools/postcss-progressive-custom-properties@3.3.0(postcss@8.4.47):
    resolution: {integrity: sha512-W2oV01phnILaRGYPmGFlL2MT/OgYjQDrL9sFlbdikMFi6oQkFki9B86XqEWR7HCsTZFVq7dbzr/o71B75TKkGg==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /@csstools/postcss-relative-color-syntax@2.0.19(postcss@8.4.47):
    resolution: {integrity: sha512-MxUMSNvio1WwuS6WRLlQuv6nNPXwIWUFzBBAvL/tBdWfiKjiJnAa6eSSN5gtaacSqUkQ/Ce5Z1OzLRfeaWhADA==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/css-color-parser': 2.0.5(@csstools/css-parser-algorithms@2.7.1)(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-parser-algorithms': 2.7.1(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-tokenizer': 2.4.1
      '@csstools/postcss-progressive-custom-properties': 3.3.0(postcss@8.4.47)
      '@csstools/utilities': 1.0.0(postcss@8.4.47)
      postcss: 8.4.47
    dev: false

  /@csstools/postcss-scope-pseudo-class@3.0.1(postcss@8.4.47):
    resolution: {integrity: sha512-3ZFonK2gfgqg29gUJ2w7xVw2wFJ1eNWVDONjbzGkm73gJHVCYK5fnCqlLr+N+KbEfv2XbWAO0AaOJCFB6Fer6A==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.47
      postcss-selector-parser: 6.1.2
    dev: false

  /@csstools/postcss-stepped-value-functions@3.0.10(postcss@8.4.47):
    resolution: {integrity: sha512-MZwo0D0TYrQhT5FQzMqfy/nGZ28D1iFtpN7Su1ck5BPHS95+/Y5O9S4kEvo76f2YOsqwYcT8ZGehSI1TnzuX2g==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/css-calc': 1.2.4(@csstools/css-parser-algorithms@2.7.1)(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-parser-algorithms': 2.7.1(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-tokenizer': 2.4.1
      postcss: 8.4.47
    dev: false

  /@csstools/postcss-text-decoration-shorthand@3.0.7(postcss@8.4.47):
    resolution: {integrity: sha512-+cptcsM5r45jntU6VjotnkC9GteFR7BQBfZ5oW7inLCxj7AfLGAzMbZ60hKTP13AULVZBdxky0P8um0IBfLHVA==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/color-helpers': 4.2.1
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /@csstools/postcss-trigonometric-functions@3.0.10(postcss@8.4.47):
    resolution: {integrity: sha512-G9G8moTc2wiad61nY5HfvxLiM/myX0aYK4s1x8MQlPH29WDPxHQM7ghGgvv2qf2xH+rrXhztOmjGHJj4jsEqXw==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/css-calc': 1.2.4(@csstools/css-parser-algorithms@2.7.1)(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-parser-algorithms': 2.7.1(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-tokenizer': 2.4.1
      postcss: 8.4.47
    dev: false

  /@csstools/postcss-unset-value@3.0.1(postcss@8.4.47):
    resolution: {integrity: sha512-dbDnZ2ja2U8mbPP0Hvmt2RMEGBiF1H7oY6HYSpjteXJGihYwgxgTr6KRbbJ/V6c+4wd51M+9980qG4gKVn5ttg==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.47
    dev: false

  /@csstools/selector-resolve-nested@1.1.0(postcss-selector-parser@6.1.2):
    resolution: {integrity: sha512-uWvSaeRcHyeNenKg8tp17EVDRkpflmdyvbE0DHo6D/GdBb6PDnCYYU6gRpXhtICMGMcahQmj2zGxwFM/WC8hCg==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss-selector-parser: ^6.0.13
    dependencies:
      postcss-selector-parser: 6.1.2
    dev: false

  /@csstools/selector-specificity@3.1.1(postcss-selector-parser@6.1.2):
    resolution: {integrity: sha512-a7cxGcJ2wIlMFLlh8z2ONm+715QkPHiyJcxwQlKOz/03GPw1COpfhcmC9wm4xlZfp//jWHNNMwzjtqHXVWU9KA==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss-selector-parser: ^6.0.13
    dependencies:
      postcss-selector-parser: 6.1.2
    dev: false

  /@csstools/utilities@1.0.0(postcss@8.4.47):
    resolution: {integrity: sha512-tAgvZQe/t2mlvpNosA4+CkMiZ2azISW5WPAcdSalZlEjQvUfghHxfQcrCiK/7/CrfAWVxyM88kGFYO82heIGDg==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.47
    dev: false

  /@discoveryjs/json-ext@0.5.7:
    resolution: {integrity: sha512-dBVuXR082gk3jsFp7Rd/JI4kytwGHecnCoTtXFb7DB6CNHp4rg5k1bhg0nWdLGLnOV71lmDzGQaLMy8iPLY0pw==}
    engines: {node: '>=10.0.0'}
    dev: false

  /@gar/promisify@1.1.3:
    resolution: {integrity: sha512-k2Ty1JcVojjJFwrg/ThKi2ujJ7XNLYaFGNB/bWT9wGR+oSMJHMa5w+CUq6p/pVrKeNNgA7pCqEcjSnHVoqJQFw==}
    dev: false

  /@highcharts/map-collection@2.3.0:
    resolution: {integrity: sha512-lknEzFw9u8BA+8J7Y1cDP0N0FxZKSJgKwTn+e8vHsBH1Xj5RYNmPJeac/3Tse0JDgKfsCT43ZHG95W9xZbSTWw==}
    dev: false

  /@jridgewell/gen-mapping@0.3.5:
    resolution: {integrity: sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.5.0
      '@jridgewell/trace-mapping': 0.3.25
    dev: false

  /@jridgewell/resolve-uri@3.1.2:
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==}
    engines: {node: '>=6.0.0'}
    dev: false

  /@jridgewell/set-array@1.2.1:
    resolution: {integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==}
    engines: {node: '>=6.0.0'}
    dev: false

  /@jridgewell/source-map@0.3.6:
    resolution: {integrity: sha512-1ZJTZebgqllO79ue2bm3rIGud/bOe0pP5BjSRCRxxYkEZS8STV7zN84UBbiYu7jy+eCKSnVIUgoWWE/tt+shMQ==}
    dependencies:
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25
    dev: false

  /@jridgewell/sourcemap-codec@1.5.0:
    resolution: {integrity: sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==}
    dev: false

  /@jridgewell/trace-mapping@0.3.25:
    resolution: {integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==}
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.0
    dev: false

  /@jsonjoy.com/base64@1.1.2(tslib@2.7.0):
    resolution: {integrity: sha512-q6XAnWQDIMA3+FTiOYajoYqySkO+JSat0ytXGSuRdq9uXE7o92gzuQwQM14xaCRlBLGq3v5miDGC4vkVTn54xA==}
    engines: {node: '>=10.0'}
    peerDependencies:
      tslib: '2'
    dependencies:
      tslib: 2.7.0
    dev: false

  /@jsonjoy.com/json-pack@1.1.0(tslib@2.7.0):
    resolution: {integrity: sha512-zlQONA+msXPPwHWZMKFVS78ewFczIll5lXiVPwFPCZUsrOKdxc2AvxU1HoNBmMRhqDZUR9HkC3UOm+6pME6Xsg==}
    engines: {node: '>=10.0'}
    peerDependencies:
      tslib: '2'
    dependencies:
      '@jsonjoy.com/base64': 1.1.2(tslib@2.7.0)
      '@jsonjoy.com/util': 1.3.0(tslib@2.7.0)
      hyperdyperid: 1.2.0
      thingies: 1.21.0(tslib@2.7.0)
      tslib: 2.7.0
    dev: false

  /@jsonjoy.com/util@1.3.0(tslib@2.7.0):
    resolution: {integrity: sha512-Cebt4Vk7k1xHy87kHY7KSPLT77A7Ev7IfOblyLZhtYEhrdQ6fX4EoLq3xOQ3O/DRMEh2ok5nyC180E+ABS8Wmw==}
    engines: {node: '>=10.0'}
    peerDependencies:
      tslib: '2'
    dependencies:
      tslib: 2.7.0
    dev: false

  /@lit-labs/ssr-dom-shim@1.2.1:
    resolution: {integrity: sha512-wx4aBmgeGvFmOKucFKY+8VFJSYZxs9poN3SDNQFF6lT6NrQUnHiPB2PWz2sc4ieEcAaYYzN+1uWahEeTq2aRIQ==}
    dev: false

  /@lit/reactive-element@1.6.3:
    resolution: {integrity: sha512-QuTgnG52Poic7uM1AN5yJ09QMe0O28e10XzSvWDz02TJiiKee4stsiownEIadWm8nYzyDAyT+gKzUoZmiWQtsQ==}
    dependencies:
      '@lit-labs/ssr-dom-shim': 1.2.1
    dev: false

  /@lottiefiles/lottie-player@1.7.1:
    resolution: {integrity: sha512-U2gokX7ZXHCD+DgRXfaFyxuVsc0SYfC2mXndnCVwGvpdApUGqvpZovihdB8p9fDKU2SBWLWBq38CuJTVz6k3Hg==}
    dependencies:
      '@types/pako': 1.0.7
      lit: 2.8.0
      lottie-web: 5.12.2
      pako: 2.1.0
      resize-observer-polyfill: 1.5.1
    dev: false

  /@lottiefiles/vue-lottie-player@1.1.0:
    resolution: {integrity: sha512-NX6aS+QdtHG++SUbF0um+hs9rT5ZYyWJeFzbKFPNHZxV1dk+PhREsHlPixZ4iVRW7TJndrp5FE7CAgURf59f0Q==}
    dependencies:
      '@lottiefiles/lottie-player': 1.7.1
      vue: 2.7.16
    dev: false

  /@nodelib/fs.scandir@2.1.5:
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0
    dev: false

  /@nodelib/fs.stat@2.0.5:
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}
    dev: false

  /@nodelib/fs.walk@1.2.8:
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.17.1
    dev: false

  /@npmcli/fs@1.1.1:
    resolution: {integrity: sha512-8KG5RD0GVP4ydEzRn/I4BNDuxDtqVbOdm8675T49OIG/NGhaK0pjPX7ZcDlvKYbA+ulvVK3ztfcF4uBdOxuJbQ==}
    dependencies:
      '@gar/promisify': 1.1.3
      semver: 7.6.3
    dev: false

  /@npmcli/move-file@1.1.2:
    resolution: {integrity: sha512-1SUf/Cg2GzGDyaf15aR9St9TWlb+XvbZXWpDx8YKs7MLzMH/BCeopv+y9vzrzgkfykCGuWOlSu3mZhj2+FQcrg==}
    engines: {node: '>=10'}
    deprecated: This functionality has been moved to @npmcli/fs
    dependencies:
      mkdirp: 1.0.4
      rimraf: 3.0.2
    dev: false

  /@nuxt/babel-preset-app@2.18.1(vue@2.7.16):
    resolution: {integrity: sha512-7AYAGVjykrvta7k+koMGbt6y6PTMwl74PX2i9Ubyc1VC9ewy9U/b6cW0gVJOR/ZJWPzaABAgVZC7N58PprUDfA==}
    engines: {node: ^14.18.0 || >=16.10.0}
    dependencies:
      '@babel/compat-data': 7.25.4
      '@babel/core': 7.25.2
      '@babel/helper-compilation-targets': 7.25.2
      '@babel/helper-module-imports': 7.24.7
      '@babel/plugin-proposal-class-properties': 7.18.6(@babel/core@7.25.2)
      '@babel/plugin-proposal-decorators': 7.24.7(@babel/core@7.25.2)
      '@babel/plugin-proposal-nullish-coalescing-operator': 7.18.6(@babel/core@7.25.2)
      '@babel/plugin-proposal-optional-chaining': 7.21.0(@babel/core@7.25.2)
      '@babel/plugin-proposal-private-methods': 7.18.6(@babel/core@7.25.2)
      '@babel/plugin-proposal-private-property-in-object': 7.21.11(@babel/core@7.25.2)
      '@babel/plugin-transform-runtime': 7.25.4(@babel/core@7.25.2)
      '@babel/preset-env': 7.25.4(@babel/core@7.25.2)
      '@babel/runtime': 7.25.6
      '@vue/babel-preset-jsx': 1.4.0(@babel/core@7.25.2)(vue@2.7.16)
      core-js: 3.38.1
      core-js-compat: 3.38.1
      regenerator-runtime: 0.14.1
    transitivePeerDependencies:
      - supports-color
      - vue
    dev: false

  /@nuxt/builder@2.18.1(vue@2.7.16):
    resolution: {integrity: sha512-hc4AUP3Nvov7jL0BEP7jFXt8zOfa6gt+y1kyoVvU1WHEVNcWnrGtRKvJuCwi1IwCVlx7Weh+luvHI4nzQwEeKg==}
    engines: {node: ^14.18.0 || >=16.10.0}
    dependencies:
      '@nuxt/devalue': 2.0.2
      '@nuxt/utils': 2.18.1
      '@nuxt/vue-app': 2.18.1
      '@nuxt/webpack': 2.18.1(vue@2.7.16)
      chalk: 4.1.2
      chokidar: 3.6.0
      consola: 3.2.3
      fs-extra: 11.2.0
      glob: 8.1.0
      hash-sum: 2.0.0
      ignore: 5.3.2
      lodash: 4.17.21
      pify: 5.0.0
      serialize-javascript: 6.0.2
      upath: 2.0.1
    transitivePeerDependencies:
      - '@vue/compiler-sfc'
      - arc-templates
      - atpl
      - babel-core
      - bluebird
      - bracket-template
      - bufferutil
      - coffee-script
      - dot
      - dust
      - dustjs-helpers
      - dustjs-linkedin
      - eco
      - ect
      - ejs
      - haml-coffee
      - hamlet
      - hamljs
      - handlebars
      - hogan.js
      - htmling
      - jade
      - jazz
      - jqtpl
      - just
      - liquid-node
      - liquor
      - marko
      - mote
      - mustache
      - nunjucks
      - plates
      - prettier
      - pug
      - qejs
      - ractive
      - razor-tmpl
      - react
      - react-dom
      - slm
      - squirrelly
      - supports-color
      - swig
      - swig-templates
      - teacup
      - templayed
      - then-jade
      - then-pug
      - tinyliquid
      - toffee
      - twig
      - twing
      - typescript
      - underscore
      - utf-8-validate
      - vash
      - velocityjs
      - vue
      - walrus
      - webpack-cli
      - webpack-command
      - whiskers
    dev: false

  /@nuxt/cli@2.18.1:
    resolution: {integrity: sha512-ZOoDlE4Fw1Cum6oG8DVnb7B4ivovXySxdDI8vnIt49Ypx22pBGt5y2ErF7g+5TAxGMIHpyh7peJWJwYp88PqPA==}
    engines: {node: ^14.18.0 || >=16.10.0}
    hasBin: true
    dependencies:
      '@nuxt/config': 2.18.1
      '@nuxt/utils': 2.18.1
      boxen: 5.1.2
      chalk: 4.1.2
      compression: 1.7.4
      connect: 3.7.0
      consola: 3.2.3
      crc: 4.3.2
      defu: 6.1.4
      destr: 2.0.3
      execa: 5.1.1
      exit: 0.1.2
      fs-extra: 11.2.0
      globby: 11.1.0
      hookable: 4.4.1
      lodash: 4.17.21
      minimist: 1.2.8
      opener: 1.5.2
      pretty-bytes: 5.6.0
      semver: 7.6.3
      serve-static: 1.16.2
      std-env: 3.7.0
      upath: 2.0.1
      wrap-ansi: 7.0.0
    transitivePeerDependencies:
      - buffer
      - supports-color
    dev: false

  /@nuxt/components@2.2.1(consola@3.2.3):
    resolution: {integrity: sha512-r1LHUzifvheTnJtYrMuA+apgsrEJbxcgFKIimeXKb+jl8TnPWdV3egmrxBCaDJchrtY/wmHyP47tunsft7AWwg==}
    peerDependencies:
      consola: '*'
    dependencies:
      chalk: 4.1.2
      chokidar: 3.6.0
      consola: 3.2.3
      glob: 7.2.3
      globby: 11.1.0
      scule: 0.2.1
      semver: 7.6.3
      upath: 2.0.1
      vue-template-compiler: 2.7.16
    dev: false

  /@nuxt/config@2.18.1:
    resolution: {integrity: sha512-CTsUMFtNCJ6+7AkgMRz53zM9vxmsMYVJWBQOnikVzwFxm/jsWzjyXkp3pQb5/fNZuqR7qXmpUKIRtrdeUeN4JQ==}
    engines: {node: ^14.18.0 || >=16.10.0}
    dependencies:
      '@nuxt/utils': 2.18.1
      consola: 3.2.3
      defu: 6.1.4
      destr: 2.0.3
      dotenv: 16.4.5
      lodash: 4.17.21
      rc9: 2.1.2
      std-env: 3.7.0
      ufo: 1.5.4
    dev: false

  /@nuxt/core@2.18.1:
    resolution: {integrity: sha512-BFnKVH7caEdDrK04qQ2U9F4Rf4hV/BqqXBJiIeHp7vM9CLKjTL5/yhiognDw3SBefmSJkpOATx1HJl3XM8c4fg==}
    engines: {node: ^14.18.0 || >=16.10.0}
    dependencies:
      '@nuxt/config': 2.18.1
      '@nuxt/server': 2.18.1
      '@nuxt/utils': 2.18.1
      consola: 3.2.3
      fs-extra: 11.2.0
      hash-sum: 2.0.0
      hookable: 4.4.1
      lodash: 4.17.21
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@nuxt/devalue@2.0.2:
    resolution: {integrity: sha512-GBzP8zOc7CGWyFQS6dv1lQz8VVpz5C2yRszbXufwG/9zhStTIH50EtD87NmWbTMwXDvZLNg8GIpb1UFdH93JCA==}
    dev: false

  /@nuxt/friendly-errors-webpack-plugin@2.6.0(webpack@4.47.0):
    resolution: {integrity: sha512-3IZj6MXbzlvUxDncAxgBMLQwGPY/JlNhy2i+AGyOHCAReR5HcBxYjVRBvyaKM9R3s5k4OODYKeHAbrToZH/47w==}
    engines: {node: '>=14.18.0', npm: '>=5.0.0'}
    peerDependencies:
      webpack: ^2.0.0 || ^3.0.0 || ^4.0.0 || ^5.0.0
    dependencies:
      chalk: 2.4.2
      consola: 3.2.3
      error-stack-parser: 2.1.4
      string-width: 4.2.3
      webpack: 4.47.0
    dev: false

  /@nuxt/generator@2.18.1:
    resolution: {integrity: sha512-kZMfB5Ymvd/5ek+xfk2svQiMJWEAjZf5XNFTG+2WiNsitHb01Bo3W2QGidy+dwfuLtHoiOJkMovRlyAKWxTohg==}
    engines: {node: ^14.18.0 || >=16.10.0}
    dependencies:
      '@nuxt/utils': 2.18.1
      chalk: 4.1.2
      consola: 3.2.3
      defu: 6.1.4
      devalue: 2.0.1
      fs-extra: 11.2.0
      html-minifier-terser: 7.2.0
      node-html-parser: 6.1.13
      ufo: 1.5.4
    dev: false

  /@nuxt/loading-screen@2.0.4:
    resolution: {integrity: sha512-xpEDAoRu75tLUYCkUJCIvJkWJSuwr8pqomvQ+fkXpSrkxZ/9OzlBFjAbVdOAWTMj4aV/LVQso4vcEdircKeFIQ==}
    dependencies:
      connect: 3.7.0
      defu: 5.0.1
      get-port-please: 2.6.1
      node-res: 5.0.1
      serve-static: 1.16.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@nuxt/opencollective@0.4.0:
    resolution: {integrity: sha512-uUsxOcO2lFeotV+BGOwNLeau+U17mhpaCRhE7v8nJLdWJ2iErQXadl28HaHe6btuT8RD0LDSpvwCiKrHznDxUA==}
    engines: {node: '>=8.0.0', npm: '>=5.0.0'}
    hasBin: true
    dependencies:
      chalk: 4.1.2
      consola: 3.2.3
      node-fetch-native: 1.6.4
    dev: false

  /@nuxt/server@2.18.1:
    resolution: {integrity: sha512-4GHmgi1NS6uCL+3QzlxmHmEoKkejQKTDrKPtA16w8iw/8EBgCrAkvXukcIMxF7Of+IYi1I/duVmCyferxo7jyw==}
    engines: {node: ^14.18.0 || >=16.10.0}
    dependencies:
      '@nuxt/utils': 2.18.1
      '@nuxt/vue-renderer': 2.18.1
      '@nuxtjs/youch': 4.2.3
      compression: 1.7.4
      connect: 3.7.0
      consola: 3.2.3
      etag: 1.8.1
      fresh: 0.5.2
      fs-extra: 11.2.0
      ip: 2.0.1
      launch-editor-middleware: 2.9.1
      on-headers: 1.0.2
      pify: 5.0.0
      serve-placeholder: 2.0.2
      serve-static: 1.16.2
      server-destroy: 1.0.1
      ufo: 1.5.4
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@nuxt/telemetry@1.5.0:
    resolution: {integrity: sha512-MhxiiYCFe0MayN2TvmpcsCV66zBePtrSVkFLJHwTFuneQ5Qma5x0NmCwdov7O4NSuTfgSZels9qPJh0zy0Kc4g==}
    hasBin: true
    dependencies:
      arg: 5.0.2
      chalk: 4.1.2
      ci-info: 3.9.0
      consola: 3.2.3
      create-require: 1.1.1
      defu: 6.1.4
      destr: 2.0.3
      dotenv: 9.0.2
      fs-extra: 8.1.0
      git-url-parse: 13.1.1
      inquirer: 7.3.3
      jiti: 1.21.6
      nanoid: 3.3.7
      node-fetch: 2.7.0
      parse-git-config: 3.0.0
      rc9: 2.1.2
      std-env: 3.7.0
    transitivePeerDependencies:
      - encoding
    dev: false

  /@nuxt/ufo@0.5.4:
    resolution: {integrity: sha512-JZOGDlt2BvLCBmLhrchhRJ6tP9PBfSWLdsHC+JVJ5Qp8/hT/2u61cSsLS30zUOwcPRaQ3y2oHNkEiKJ5v90iEQ==}
    deprecated: please migrate to ufo npm package
    dev: false

  /@nuxt/utils@2.18.1:
    resolution: {integrity: sha512-aWeB8VMhtymo5zXUiQaohCu8IqJqENF9iCag3wyJpdhpNDVoghGUJAl0F6mQvNTJgQzseFtf4XKqTfvcgVzyGg==}
    engines: {node: ^14.18.0 || >=16.10.0}
    dependencies:
      consola: 3.2.3
      create-require: 1.1.1
      fs-extra: 11.2.0
      hash-sum: 2.0.0
      jiti: 1.21.6
      lodash: 4.17.21
      proper-lockfile: 4.1.2
      semver: 7.6.3
      serialize-javascript: 6.0.2
      signal-exit: 4.1.0
      ua-parser-js: 1.0.39
      ufo: 1.5.4
    dev: false

  /@nuxt/vue-app@2.18.1:
    resolution: {integrity: sha512-yxkunoTv6EVa42xM7qES0N1DNMo4UbP/s89L7HjqngQ4KzVWyyzK0qqJ9u3Gu4CabXhHFSquu11gtn+dylKyTA==}
    engines: {node: ^14.18.0 || >=16.10.0}
    dependencies:
      node-fetch-native: 1.6.4
      ufo: 1.5.4
      unfetch: 5.0.0
      vue: 2.7.16
      vue-client-only: 2.1.0
      vue-meta: 2.4.0
      vue-no-ssr: 1.1.1
      vue-router: 3.6.5(vue@2.7.16)
      vue-template-compiler: 2.7.16
      vuex: 3.6.2(vue@2.7.16)
    dev: false

  /@nuxt/vue-renderer@2.18.1:
    resolution: {integrity: sha512-Nl8/IbV+sTEWCczHKcjLbZrFO6y5fCcFxZwd6Opatcbr2z380abwpDf3a9UjnVW3wPEM+/xoy1/MBCLY3VmWcw==}
    engines: {node: ^14.18.0 || >=16.10.0}
    dependencies:
      '@nuxt/devalue': 2.0.2
      '@nuxt/utils': 2.18.1
      consola: 3.2.3
      defu: 6.1.4
      fs-extra: 11.2.0
      lodash: 4.17.21
      lru-cache: 5.1.1
      ufo: 1.5.4
      vue: 2.7.16
      vue-meta: 2.4.0
      vue-server-renderer: 2.7.16
    dev: false

  /@nuxt/webpack@2.18.1(vue@2.7.16):
    resolution: {integrity: sha512-6EqbIoheLAJ0E7dfQB5ftOKL4d74N98dFMY3q89QTaoS9VXBFB5D1MLd27WuyfhChmzuHRwHfjaBW8QFdhjwew==}
    engines: {node: ^14.18.0 || >=16.10.0}
    dependencies:
      '@babel/core': 7.25.2
      '@nuxt/babel-preset-app': 2.18.1(vue@2.7.16)
      '@nuxt/friendly-errors-webpack-plugin': 2.6.0(webpack@4.47.0)
      '@nuxt/utils': 2.18.1
      babel-loader: 8.4.1(@babel/core@7.25.2)(webpack@4.47.0)
      cache-loader: 4.1.0(webpack@4.47.0)
      caniuse-lite: 1.0.30001662
      consola: 3.2.3
      css-loader: 5.2.7(webpack@4.47.0)
      cssnano: 7.0.6(postcss@8.4.47)
      eventsource-polyfill: 0.9.6
      extract-css-chunks-webpack-plugin: 4.10.0(webpack@4.47.0)
      file-loader: 6.2.0(webpack@4.47.0)
      glob: 8.1.0
      hard-source-webpack-plugin: 0.13.1(webpack@4.47.0)
      hash-sum: 2.0.0
      html-webpack-plugin: 4.5.2(webpack@4.47.0)
      lodash: 4.17.21
      memfs: 4.12.0
      mkdirp: 0.5.6
      optimize-css-assets-webpack-plugin: 6.0.1(webpack@4.47.0)
      pify: 5.0.0
      pnp-webpack-plugin: 1.7.0
      postcss: 8.4.47
      postcss-import: 15.1.0(postcss@8.4.47)
      postcss-import-resolver: 2.0.0
      postcss-loader: 4.3.0(postcss@8.4.47)(webpack@4.47.0)
      postcss-preset-env: 9.6.0(postcss@8.4.47)
      postcss-url: 10.1.3(postcss@8.4.47)
      semver: 7.6.3
      std-env: 3.7.0
      style-resources-loader: 1.5.0(webpack@4.47.0)
      terser-webpack-plugin: 4.2.3(webpack@4.47.0)
      thread-loader: 3.0.4(webpack@4.47.0)
      time-fix-plugin: 2.0.7(webpack@4.47.0)
      ufo: 1.5.4
      upath: 2.0.1
      url-loader: 4.1.1(file-loader@6.2.0)(webpack@4.47.0)
      vue-loader: 15.11.1(cache-loader@4.1.0)(css-loader@5.2.7)(lodash@4.17.21)(vue-template-compiler@2.7.16)(webpack@4.47.0)
      vue-style-loader: 4.1.3
      vue-template-compiler: 2.7.16
      watchpack: 2.4.2
      webpack: 4.47.0
      webpack-bundle-analyzer: 4.10.2
      webpack-dev-middleware: 5.3.4(webpack@4.47.0)
      webpack-hot-middleware: 2.26.1
      webpack-node-externals: 3.0.0
      webpackbar: 6.0.1(webpack@4.47.0)
    transitivePeerDependencies:
      - '@vue/compiler-sfc'
      - arc-templates
      - atpl
      - babel-core
      - bluebird
      - bracket-template
      - bufferutil
      - coffee-script
      - dot
      - dust
      - dustjs-helpers
      - dustjs-linkedin
      - eco
      - ect
      - ejs
      - haml-coffee
      - hamlet
      - hamljs
      - handlebars
      - hogan.js
      - htmling
      - jade
      - jazz
      - jqtpl
      - just
      - liquid-node
      - liquor
      - marko
      - mote
      - mustache
      - nunjucks
      - plates
      - prettier
      - pug
      - qejs
      - ractive
      - razor-tmpl
      - react
      - react-dom
      - slm
      - squirrelly
      - supports-color
      - swig
      - swig-templates
      - teacup
      - templayed
      - then-jade
      - then-pug
      - tinyliquid
      - toffee
      - twig
      - twing
      - typescript
      - underscore
      - utf-8-validate
      - vash
      - velocityjs
      - vue
      - walrus
      - webpack-cli
      - webpack-command
      - whiskers
    dev: false

  /@nuxtjs/proxy@2.1.0:
    resolution: {integrity: sha512-/qtoeqXgZ4Mg6LRg/gDUZQrFpOlOdHrol/vQYMnKu3aN3bP90UfOUB3QSDghUUK7OISAJ0xp8Ld78aHyCTcKCQ==}
    dependencies:
      http-proxy-middleware: 1.3.1
    transitivePeerDependencies:
      - debug
    dev: false

  /@nuxtjs/youch@4.2.3:
    resolution: {integrity: sha512-XiTWdadTwtmL/IGkNqbVe+dOlT+IMvcBu7TvKI7plWhVQeBCQ9iKhk3jgvVWFyiwL2yHJDlEwOM5v9oVES5Xmw==}
    dependencies:
      cookie: 0.3.1
      mustache: 2.3.2
      stack-trace: 0.0.10
    dev: false

  /@polka/url@1.0.0-next.28:
    resolution: {integrity: sha512-8LduaNlMZGwdZ6qWrKlfa+2M4gahzFkprZiAt2TF8uS0qQgBizKXpXURqvTJ4WtmupWxaLqjRb2UCTe72mu+Aw==}
    dev: false

  /@tinymce/tinymce-vue@3.2.8(vue@2.7.16):
    resolution: {integrity: sha512-jEz+NZ0g+FZFz273OEUWz9QkwPMyjc5AJYyxOgu51O1Y5UaJ/6IUddXTX6A20mwCleEv5ebwNYdalviafx4fnA==}
    peerDependencies:
      vue: ^2.4.3
    dependencies:
      vue: 2.7.16
    dev: false

  /@trysound/sax@0.2.0:
    resolution: {integrity: sha512-L7z9BgrNEcYyUYtF+HaEfiS5ebkh9jXqbszz7pC0hRBPaatV0XjSD3+eHrpqFemQfgwiFF0QPIarnIihIDn7OA==}
    engines: {node: '>=10.13.0'}
    dev: false

  /@types/cookie@0.3.3:
    resolution: {integrity: sha512-LKVP3cgXBT9RYj+t+9FDKwS5tdI+rPBXaNSkma7hvqy35lc7mAokC2zsqWJH0LaqIt3B962nuYI77hsJoT1gow==}
    dev: false

  /@types/html-minifier-terser@5.1.2:
    resolution: {integrity: sha512-h4lTMgMJctJybDp8CQrxTUiiYmedihHWkjnF/8Pxseu2S6Nlfcy8kwboQ8yejh456rP2yWoEVm1sS/FVsfM48w==}
    dev: false

  /@types/http-proxy@1.17.15:
    resolution: {integrity: sha512-25g5atgiVNTIv0LBDTg1H74Hvayx0ajtJPLLcYE3whFv75J0pWNtOBzaXJQgDTmrX1bx5U9YC2w/n65BN1HwRQ==}
    dependencies:
      '@types/node': 22.5.5
    dev: false

  /@types/json-schema@7.0.15:
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==}
    dev: false

  /@types/node@22.5.5:
    resolution: {integrity: sha512-Xjs4y5UPO/CLdzpgR6GirZJx36yScjh73+2NlLlkFRSoQN8B0DpfXPdZGnvVmLRLOsqDpOfTNv7D9trgGhmOIA==}
    dependencies:
      undici-types: 6.19.8
    dev: false

  /@types/pako@1.0.7:
    resolution: {integrity: sha512-YBtzT2ztNF6R/9+UXj2wTGFnC9NklAnASt3sC0h2m1bbH7G6FyBIkt4AN8ThZpNfxUo1b2iMVO0UawiJymEt8A==}
    dev: false

  /@types/parse-json@4.0.2:
    resolution: {integrity: sha512-dISoDXWWQwUquiKsyZ4Ng+HX2KsPL7LyHKHQwgGFEA3IaKac4Obd+h2a/a6waisAoepJlBcx9paWqjA8/HVjCw==}
    dev: false

  /@types/source-list-map@0.1.6:
    resolution: {integrity: sha512-5JcVt1u5HDmlXkwOD2nslZVllBBc7HDuOICfiZah2Z0is8M8g+ddAEawbmd3VjedfDHBzxCaXLs07QEmb7y54g==}
    dev: false

  /@types/tapable@1.0.12:
    resolution: {integrity: sha512-bTHG8fcxEqv1M9+TD14P8ok8hjxoOCkfKc8XXLaaD05kI7ohpeI956jtDOD3XHKBQrlyPughUtzm1jtVhHpA5Q==}
    dev: false

  /@types/trusted-types@2.0.7:
    resolution: {integrity: sha512-ScaPdn1dQczgbl0QFTeTOmVHFULt394XJgOQNoyVhZ6r2vLnMLJfBPd53SB52T/3G36VI1/g2MZaX0cwDuXsfw==}
    dev: false

  /@types/uglify-js@3.17.5:
    resolution: {integrity: sha512-TU+fZFBTBcXj/GpDpDaBmgWk/gn96kMZ+uocaFUlV2f8a6WdMzzI44QBCmGcCiYR0Y6ZlNRiyUyKKt5nl/lbzQ==}
    dependencies:
      source-map: 0.6.1
    dev: false

  /@types/webpack-sources@3.2.3:
    resolution: {integrity: sha512-4nZOdMwSPHZ4pTEZzSp0AsTM4K7Qmu40UKW4tJDiOVs20UzYF9l+qUe4s0ftfN0pin06n+5cWWDJXH+sbhAiDw==}
    dependencies:
      '@types/node': 22.5.5
      '@types/source-list-map': 0.1.6
      source-map: 0.7.4
    dev: false

  /@types/webpack@4.41.39:
    resolution: {integrity: sha512-otxUJvoi6FbBq/64gGH34eblpKLgdi+gf08GaAh8Bx6So0ZZic028Ev/SUxD22gbthMKCkeeiXEat1kHLDJfYg==}
    dependencies:
      '@types/node': 22.5.5
      '@types/tapable': 1.0.12
      '@types/uglify-js': 3.17.5
      '@types/webpack-sources': 3.2.3
      anymatch: 3.1.3
      source-map: 0.6.1
    dev: false

  /@vue/babel-helper-vue-jsx-merge-props@1.4.0:
    resolution: {integrity: sha512-JkqXfCkUDp4PIlFdDQ0TdXoIejMtTHP67/pvxlgeY+u5k3LEdKuWZ3LK6xkxo52uDoABIVyRwqVkfLQJhk7VBA==}
    dev: false

  /@vue/babel-plugin-transform-vue-jsx@1.4.0(@babel/core@7.25.2):
    resolution: {integrity: sha512-Fmastxw4MMx0vlgLS4XBX0XiBbUFzoMGeVXuMV08wyOfXdikAFqBTuYPR0tlk+XskL19EzHc39SgjrPGY23JnA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-module-imports': 7.24.7
      '@babel/plugin-syntax-jsx': 7.24.7(@babel/core@7.25.2)
      '@vue/babel-helper-vue-jsx-merge-props': 1.4.0
      html-tags: 2.0.0
      lodash.kebabcase: 4.1.1
      svg-tags: 1.0.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@vue/babel-preset-jsx@1.4.0(@babel/core@7.25.2)(vue@2.7.16):
    resolution: {integrity: sha512-QmfRpssBOPZWL5xw7fOuHNifCQcNQC1PrOo/4fu6xlhlKJJKSA3HqX92Nvgyx8fqHZTUGMPHmFA+IDqwXlqkSA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
      vue: '*'
    peerDependenciesMeta:
      vue:
        optional: true
    dependencies:
      '@babel/core': 7.25.2
      '@vue/babel-helper-vue-jsx-merge-props': 1.4.0
      '@vue/babel-plugin-transform-vue-jsx': 1.4.0(@babel/core@7.25.2)
      '@vue/babel-sugar-composition-api-inject-h': 1.4.0(@babel/core@7.25.2)
      '@vue/babel-sugar-composition-api-render-instance': 1.4.0(@babel/core@7.25.2)
      '@vue/babel-sugar-functional-vue': 1.4.0(@babel/core@7.25.2)
      '@vue/babel-sugar-inject-h': 1.4.0(@babel/core@7.25.2)
      '@vue/babel-sugar-v-model': 1.4.0(@babel/core@7.25.2)
      '@vue/babel-sugar-v-on': 1.4.0(@babel/core@7.25.2)
      vue: 2.7.16
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@vue/babel-sugar-composition-api-inject-h@1.4.0(@babel/core@7.25.2):
    resolution: {integrity: sha512-VQq6zEddJHctnG4w3TfmlVp5FzDavUSut/DwR0xVoe/mJKXyMcsIibL42wPntozITEoY90aBV0/1d2KjxHU52g==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/plugin-syntax-jsx': 7.24.7(@babel/core@7.25.2)
    dev: false

  /@vue/babel-sugar-composition-api-render-instance@1.4.0(@babel/core@7.25.2):
    resolution: {integrity: sha512-6ZDAzcxvy7VcnCjNdHJ59mwK02ZFuP5CnucloidqlZwVQv5CQLijc3lGpR7MD3TWFi78J7+a8J56YxbCtHgT9Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/plugin-syntax-jsx': 7.24.7(@babel/core@7.25.2)
    dev: false

  /@vue/babel-sugar-functional-vue@1.4.0(@babel/core@7.25.2):
    resolution: {integrity: sha512-lTEB4WUFNzYt2In6JsoF9sAYVTo84wC4e+PoZWSgM6FUtqRJz7wMylaEhSRgG71YF+wfLD6cc9nqVeXN2rwBvw==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/plugin-syntax-jsx': 7.24.7(@babel/core@7.25.2)
    dev: false

  /@vue/babel-sugar-inject-h@1.4.0(@babel/core@7.25.2):
    resolution: {integrity: sha512-muwWrPKli77uO2fFM7eA3G1lAGnERuSz2NgAxuOLzrsTlQl8W4G+wwbM4nB6iewlKbwKRae3nL03UaF5ffAPMA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/plugin-syntax-jsx': 7.24.7(@babel/core@7.25.2)
    dev: false

  /@vue/babel-sugar-v-model@1.4.0(@babel/core@7.25.2):
    resolution: {integrity: sha512-0t4HGgXb7WHYLBciZzN5s0Hzqan4Ue+p/3FdQdcaHAb7s5D9WZFGoSxEZHrR1TFVZlAPu1bejTKGeAzaaG3NCQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/plugin-syntax-jsx': 7.24.7(@babel/core@7.25.2)
      '@vue/babel-helper-vue-jsx-merge-props': 1.4.0
      '@vue/babel-plugin-transform-vue-jsx': 1.4.0(@babel/core@7.25.2)
      camelcase: 5.3.1
      html-tags: 2.0.0
      svg-tags: 1.0.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@vue/babel-sugar-v-on@1.4.0(@babel/core@7.25.2):
    resolution: {integrity: sha512-m+zud4wKLzSKgQrWwhqRObWzmTuyzl6vOP7024lrpeJM4x2UhQtRDLgYjXAw9xBXjCwS0pP9kXjg91F9ZNo9JA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/plugin-syntax-jsx': 7.24.7(@babel/core@7.25.2)
      '@vue/babel-plugin-transform-vue-jsx': 1.4.0(@babel/core@7.25.2)
      camelcase: 5.3.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@vue/compiler-sfc@2.7.16:
    resolution: {integrity: sha512-KWhJ9k5nXuNtygPU7+t1rX6baZeqOYLEforUPjgNDBnLicfHCoi48H87Q8XyLZOrNNsmhuwKqtpDQWjEFe6Ekg==}
    dependencies:
      '@babel/parser': 7.25.6
      postcss: 8.4.47
      source-map: 0.6.1
    optionalDependencies:
      prettier: 2.8.8
    dev: false

  /@vue/component-compiler-utils@3.3.0(lodash@4.17.21):
    resolution: {integrity: sha512-97sfH2mYNU+2PzGrmK2haqffDpVASuib9/w2/noxiFi31Z54hW+q3izKQXXQZSNhtiUpAI36uSuYepeBe4wpHQ==}
    dependencies:
      consolidate: 0.15.1(lodash@4.17.21)
      hash-sum: 1.0.2
      lru-cache: 4.1.5
      merge-source-map: 1.1.0
      postcss: 7.0.39
      postcss-selector-parser: 6.1.2
      source-map: 0.6.1
      vue-template-es2015-compiler: 1.9.1
    optionalDependencies:
      prettier: 2.8.8
    transitivePeerDependencies:
      - arc-templates
      - atpl
      - babel-core
      - bracket-template
      - coffee-script
      - dot
      - dust
      - dustjs-helpers
      - dustjs-linkedin
      - eco
      - ect
      - ejs
      - haml-coffee
      - hamlet
      - hamljs
      - handlebars
      - hogan.js
      - htmling
      - jade
      - jazz
      - jqtpl
      - just
      - liquid-node
      - liquor
      - lodash
      - marko
      - mote
      - mustache
      - nunjucks
      - plates
      - pug
      - qejs
      - ractive
      - razor-tmpl
      - react
      - react-dom
      - slm
      - squirrelly
      - swig
      - swig-templates
      - teacup
      - templayed
      - then-jade
      - then-pug
      - tinyliquid
      - toffee
      - twig
      - twing
      - underscore
      - vash
      - velocityjs
      - walrus
      - whiskers
    dev: false

  /@webassemblyjs/ast@1.9.0:
    resolution: {integrity: sha512-C6wW5L+b7ogSDVqymbkkvuW9kruN//YisMED04xzeBBqjHa2FYnmvOlS6Xj68xWQRgWvI9cIglsjFowH/RJyEA==}
    dependencies:
      '@webassemblyjs/helper-module-context': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/wast-parser': 1.9.0
    dev: false

  /@webassemblyjs/floating-point-hex-parser@1.9.0:
    resolution: {integrity: sha512-TG5qcFsS8QB4g4MhrxK5TqfdNe7Ey/7YL/xN+36rRjl/BlGE/NcBvJcqsRgCP6Z92mRE+7N50pRIi8SmKUbcQA==}
    dev: false

  /@webassemblyjs/helper-api-error@1.9.0:
    resolution: {integrity: sha512-NcMLjoFMXpsASZFxJ5h2HZRcEhDkvnNFOAKneP5RbKRzaWJN36NC4jqQHKwStIhGXu5mUWlUUk7ygdtrO8lbmw==}
    dev: false

  /@webassemblyjs/helper-buffer@1.9.0:
    resolution: {integrity: sha512-qZol43oqhq6yBPx7YM3m9Bv7WMV9Eevj6kMi6InKOuZxhw+q9hOkvq5e/PpKSiLfyetpaBnogSbNCfBwyB00CA==}
    dev: false

  /@webassemblyjs/helper-code-frame@1.9.0:
    resolution: {integrity: sha512-ERCYdJBkD9Vu4vtjUYe8LZruWuNIToYq/ME22igL+2vj2dQ2OOujIZr3MEFvfEaqKoVqpsFKAGsRdBSBjrIvZA==}
    dependencies:
      '@webassemblyjs/wast-printer': 1.9.0
    dev: false

  /@webassemblyjs/helper-fsm@1.9.0:
    resolution: {integrity: sha512-OPRowhGbshCb5PxJ8LocpdX9Kl0uB4XsAjl6jH/dWKlk/mzsANvhwbiULsaiqT5GZGT9qinTICdj6PLuM5gslw==}
    dev: false

  /@webassemblyjs/helper-module-context@1.9.0:
    resolution: {integrity: sha512-MJCW8iGC08tMk2enck1aPW+BE5Cw8/7ph/VGZxwyvGbJwjktKkDK7vy7gAmMDx88D7mhDTCNKAW5tED+gZ0W8g==}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
    dev: false

  /@webassemblyjs/helper-wasm-bytecode@1.9.0:
    resolution: {integrity: sha512-R7FStIzyNcd7xKxCZH5lE0Bqy+hGTwS3LJjuv1ZVxd9O7eHCedSdrId/hMOd20I+v8wDXEn+bjfKDLzTepoaUw==}
    dev: false

  /@webassemblyjs/helper-wasm-section@1.9.0:
    resolution: {integrity: sha512-XnMB8l3ek4tvrKUUku+IVaXNHz2YsJyOOmz+MMkZvh8h1uSJpSen6vYnw3IoQ7WwEuAhL8Efjms1ZWjqh2agvw==}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-buffer': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/wasm-gen': 1.9.0
    dev: false

  /@webassemblyjs/ieee754@1.9.0:
    resolution: {integrity: sha512-dcX8JuYU/gvymzIHc9DgxTzUUTLexWwt8uCTWP3otys596io0L5aW02Gb1RjYpx2+0Jus1h4ZFqjla7umFniTg==}
    dependencies:
      '@xtuc/ieee754': 1.2.0
    dev: false

  /@webassemblyjs/leb128@1.9.0:
    resolution: {integrity: sha512-ENVzM5VwV1ojs9jam6vPys97B/S65YQtv/aanqnU7D8aSoHFX8GyhGg0CMfyKNIHBuAVjy3tlzd5QMMINa7wpw==}
    dependencies:
      '@xtuc/long': 4.2.2
    dev: false

  /@webassemblyjs/utf8@1.9.0:
    resolution: {integrity: sha512-GZbQlWtopBTP0u7cHrEx+73yZKrQoBMpwkGEIqlacljhXCkVM1kMQge/Mf+csMJAjEdSwhOyLAS0AoR3AG5P8w==}
    dev: false

  /@webassemblyjs/wasm-edit@1.9.0:
    resolution: {integrity: sha512-FgHzBm80uwz5M8WKnMTn6j/sVbqilPdQXTWraSjBwFXSYGirpkSWE2R9Qvz9tNiTKQvoKILpCuTjBKzOIm0nxw==}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-buffer': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/helper-wasm-section': 1.9.0
      '@webassemblyjs/wasm-gen': 1.9.0
      '@webassemblyjs/wasm-opt': 1.9.0
      '@webassemblyjs/wasm-parser': 1.9.0
      '@webassemblyjs/wast-printer': 1.9.0
    dev: false

  /@webassemblyjs/wasm-gen@1.9.0:
    resolution: {integrity: sha512-cPE3o44YzOOHvlsb4+E9qSqjc9Qf9Na1OO/BHFy4OI91XDE14MjFN4lTMezzaIWdPqHnsTodGGNP+iRSYfGkjA==}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/ieee754': 1.9.0
      '@webassemblyjs/leb128': 1.9.0
      '@webassemblyjs/utf8': 1.9.0
    dev: false

  /@webassemblyjs/wasm-opt@1.9.0:
    resolution: {integrity: sha512-Qkjgm6Anhm+OMbIL0iokO7meajkzQD71ioelnfPEj6r4eOFuqm4YC3VBPqXjFyyNwowzbMD+hizmprP/Fwkl2A==}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-buffer': 1.9.0
      '@webassemblyjs/wasm-gen': 1.9.0
      '@webassemblyjs/wasm-parser': 1.9.0
    dev: false

  /@webassemblyjs/wasm-parser@1.9.0:
    resolution: {integrity: sha512-9+wkMowR2AmdSWQzsPEjFU7njh8HTO5MqO8vjwEHuM+AMHioNqSBONRdr0NQQ3dVQrzp0s8lTcYqzUdb7YgELA==}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-api-error': 1.9.0
      '@webassemblyjs/helper-wasm-bytecode': 1.9.0
      '@webassemblyjs/ieee754': 1.9.0
      '@webassemblyjs/leb128': 1.9.0
      '@webassemblyjs/utf8': 1.9.0
    dev: false

  /@webassemblyjs/wast-parser@1.9.0:
    resolution: {integrity: sha512-qsqSAP3QQ3LyZjNC/0jBJ/ToSxfYJ8kYyuiGvtn/8MK89VrNEfwj7BPQzJVHi0jGTRK2dGdJ5PRqhtjzoww+bw==}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/floating-point-hex-parser': 1.9.0
      '@webassemblyjs/helper-api-error': 1.9.0
      '@webassemblyjs/helper-code-frame': 1.9.0
      '@webassemblyjs/helper-fsm': 1.9.0
      '@xtuc/long': 4.2.2
    dev: false

  /@webassemblyjs/wast-printer@1.9.0:
    resolution: {integrity: sha512-2J0nE95rHXHyQ24cWjMKJ1tqB/ds8z/cyeOZxJhcb+rW+SQASVjuznUSmdz5GpVJTzU8JkhYut0D3siFDD6wsA==}
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/wast-parser': 1.9.0
      '@xtuc/long': 4.2.2
    dev: false

  /@xtuc/ieee754@1.2.0:
    resolution: {integrity: sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==}
    dev: false

  /@xtuc/long@4.2.2:
    resolution: {integrity: sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==}
    dev: false

  /accepts@1.3.8:
    resolution: {integrity: sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-types: 2.1.35
      negotiator: 0.6.3
    dev: false

  /acorn-walk@8.3.4:
    resolution: {integrity: sha512-ueEepnujpqee2o5aIYnvHU6C0A42MNdsIDeqy5BydrkuC5R1ZuUFnm27EeFJGoEHJQgn3uleRvmTXaJgfXbt4g==}
    engines: {node: '>=0.4.0'}
    dependencies:
      acorn: 8.12.1
    dev: false

  /acorn@6.4.2:
    resolution: {integrity: sha512-XtGIhXwF8YM8bJhGxG5kXgjkEuNGLTkoYqVE+KMR+aspr4KGYmKYg7yUe3KghyQ9yheNwLnjmzh/7+gfDBmHCQ==}
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: false

  /acorn@8.12.1:
    resolution: {integrity: sha512-tcpGyI9zbizT9JbV6oYE477V6mTlXvvi0T0G3SNIYE2apm/G5huBa1+K89VGeovbg+jycCrfhl3ADxErOuO6Jg==}
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: false

  /adler-32@1.2.0:
    resolution: {integrity: sha512-/vUqU/UY4MVeFsg+SsK6c+/05RZXIHZMGJA+PX5JyWI0ZRcBpupnRuPLU/NXXoFwMYCPCoxIfElM2eS+DUXCqQ==}
    engines: {node: '>=0.8'}
    hasBin: true
    dependencies:
      exit-on-epipe: 1.0.1
      printj: 1.1.2
    dev: false

  /adler-32@1.3.1:
    resolution: {integrity: sha512-ynZ4w/nUUv5rrsR8UUGoe1VC9hZj6V5hU9Qw1HlMDJGEJw5S7TfTErWTjMys6M7vr0YWcPqs3qAr4ss0nDfP+A==}
    engines: {node: '>=0.8'}
    dev: false

  /aggregate-error@3.1.0:
    resolution: {integrity: sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA==}
    engines: {node: '>=8'}
    dependencies:
      clean-stack: 2.2.0
      indent-string: 4.0.0
    dev: false

  /ajv-errors@1.0.1(ajv@6.12.6):
    resolution: {integrity: sha512-DCRfO/4nQ+89p/RK43i8Ezd41EqdGIU4ld7nGF8OQ14oc/we5rEntLCUa7+jrn3nn83BosfwZA0wb4pon2o8iQ==}
    peerDependencies:
      ajv: '>=5.0.0'
    dependencies:
      ajv: 6.12.6
    dev: false

  /ajv-formats@2.1.1(ajv@8.17.1):
    resolution: {integrity: sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA==}
    peerDependencies:
      ajv: ^8.0.0
    peerDependenciesMeta:
      ajv:
        optional: true
    dependencies:
      ajv: 8.17.1
    dev: false

  /ajv-keywords@3.5.2(ajv@6.12.6):
    resolution: {integrity: sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==}
    peerDependencies:
      ajv: ^6.9.1
    dependencies:
      ajv: 6.12.6
    dev: false

  /ajv-keywords@5.1.0(ajv@8.17.1):
    resolution: {integrity: sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw==}
    peerDependencies:
      ajv: ^8.8.2
    dependencies:
      ajv: 8.17.1
      fast-deep-equal: 3.1.3
    dev: false

  /ajv@6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1
    dev: false

  /ajv@8.17.1:
    resolution: {integrity: sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g==}
    dependencies:
      fast-deep-equal: 3.1.3
      fast-uri: 3.0.1
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2
    dev: false

  /ansi-align@3.0.1:
    resolution: {integrity: sha512-IOfwwBF5iczOjp/WeY4YxyjqAFMQoZufdQWDd19SEExbVLNXqvpzSJ/M7Za4/sCPmQ0+GRquoA7bGcINcxew6w==}
    dependencies:
      string-width: 4.2.3
    dev: false

  /ansi-escapes@4.3.2:
    resolution: {integrity: sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==}
    engines: {node: '>=8'}
    dependencies:
      type-fest: 0.21.3
    dev: false

  /ansi-html-community@0.0.8:
    resolution: {integrity: sha512-1APHAyr3+PCamwNw3bXCPp4HFLONZt/yIH0sZp0/469KWNTEy+qN5jQ3GVX6DMZ1UXAi34yVwtTeaG/HpBuuzw==}
    engines: {'0': node >= 0.8.0}
    hasBin: true
    dev: false

  /ansi-regex@2.1.1:
    resolution: {integrity: sha512-TIGnTpdo+E3+pCyAluZvtED5p5wCqLdezCyhPZzKPcxvFplEt4i+W7OONCKgeZFT3+y5NZZfOOS/Bdcanm1MYA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}
    dev: false

  /ansi-styles@3.2.1:
    resolution: {integrity: sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==}
    engines: {node: '>=4'}
    dependencies:
      color-convert: 1.9.3
    dev: false

  /ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}
    dependencies:
      color-convert: 2.0.1
    dev: false

  /anymatch@2.0.0:
    resolution: {integrity: sha512-5teOsQWABXHHBFP9y3skS5P3d/WfWXpv3FUpy+LorMrNYaT9pI4oLMQX7jzQ2KklNpGpWHzdCXTDT2Y3XGlZBw==}
    requiresBuild: true
    dependencies:
      micromatch: 3.1.10
      normalize-path: 2.1.1
    transitivePeerDependencies:
      - supports-color
    dev: false
    optional: true

  /anymatch@3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==}
    engines: {node: '>= 8'}
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1
    dev: false

  /aproba@1.2.0:
    resolution: {integrity: sha512-Y9J6ZjXtoYh8RnXVCMOU/ttDmk1aBjunq9vO0ta5x85WDQiQfUF9sIPBITdbiiIVcBo03Hi3jMxigBtsddlXRw==}
    dev: false

  /arg@5.0.2:
    resolution: {integrity: sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==}
    dev: false

  /arr-diff@4.0.0:
    resolution: {integrity: sha512-YVIQ82gZPGBebQV/a8dar4AitzCQs0jjXwMPZllpXMaGjXPYVUawSxQrRsjhjupyVxEvbHgUmIhKVlND+j02kA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /arr-flatten@1.1.0:
    resolution: {integrity: sha512-L3hKV5R/p5o81R7O02IGnwpDmkp6E982XhtbuwSe3O4qOtMMMtodicASA1Cny2U+aCXcNpml+m4dPsvsJ3jatg==}
    engines: {node: '>=0.10.0'}
    dev: false

  /arr-union@3.1.0:
    resolution: {integrity: sha512-sKpyeERZ02v1FeCZT8lrfJq5u6goHCtpTAzPwJYe7c8SPFOboNjNg1vz2L4VTn9T4PQxEx13TbXLmYUcS6Ug7Q==}
    engines: {node: '>=0.10.0'}
    dev: false

  /array-buffer-byte-length@1.0.1:
    resolution: {integrity: sha512-ahC5W1xgou+KTXix4sAO8Ki12Q+jf4i0+tmk3sC+zgcynshkHxzpXdImBehiUYKKKDwvfFiJl1tZt6ewscS1Mg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      is-array-buffer: 3.0.4
    dev: false

  /array-union@2.1.0:
    resolution: {integrity: sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==}
    engines: {node: '>=8'}
    dev: false

  /array-unique@0.3.2:
    resolution: {integrity: sha512-SleRWjh9JUud2wH1hPs9rZBZ33H6T9HOiL0uwGnGx9FpE6wKGyfWugmbkEOIs6qWrZhg0LWeLziLrEwQJhs5mQ==}
    engines: {node: '>=0.10.0'}
    dev: false

  /array.prototype.reduce@1.0.7:
    resolution: {integrity: sha512-mzmiUCVwtiD4lgxYP8g7IYy8El8p2CSMePvIbTS7gchKir/L1fgJrk0yDKmAX6mnRQFKNADYIk8nNlTris5H1Q==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-array-method-boxes-properly: 1.0.0
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      is-string: 1.0.7
    dev: false

  /arraybuffer.prototype.slice@1.0.3:
    resolution: {integrity: sha512-bMxMKAjg13EBSVscxTaYA4mRc5t1UAXa2kXiGTNfZ079HIWXEkKmkgFrh/nJqamaLSrXO5H4WFFkPEaLJWbs3A==}
    engines: {node: '>= 0.4'}
    dependencies:
      array-buffer-byte-length: 1.0.1
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      get-intrinsic: 1.2.4
      is-array-buffer: 3.0.4
      is-shared-array-buffer: 1.0.3
    dev: false

  /asn1.js@4.10.1:
    resolution: {integrity: sha512-p32cOF5q0Zqs9uBiONKYLm6BClCoBCM5O9JfeUSlnQLBTxYdTK+pW+nXflm8UkKd2UYlEbYz5qEi0JuZR9ckSw==}
    dependencies:
      bn.js: 4.12.0
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
    dev: false

  /assert@1.5.1:
    resolution: {integrity: sha512-zzw1uCAgLbsKwBfFc8CX78DDg+xZeBksSO3vwVIDDN5i94eOrPsSSyiVhmsSABFDM/OcpE2aagCat9dnWQLG1A==}
    dependencies:
      object.assign: 4.1.5
      util: 0.10.4
    dev: false

  /assign-symbols@1.0.0:
    resolution: {integrity: sha512-Q+JC7Whu8HhmTdBph/Tq59IoRtoy6KAm5zzPv00WdujX82lbAL8K7WVjne7vdCsAmbF4AYaDOPyO3k0kl8qIrw==}
    engines: {node: '>=0.10.0'}
    dev: false

  /async-each@1.0.6:
    resolution: {integrity: sha512-c646jH1avxr+aVpndVMeAfYw7wAa6idufrlN3LPA4PmKS0QEGp6PIC9nwz0WQkkvBGAMEki3pFdtxaF39J9vvg==}
    requiresBuild: true
    dev: false
    optional: true

  /async-validator@1.8.5:
    resolution: {integrity: sha512-tXBM+1m056MAX0E8TL2iCjg8WvSyXu0Zc8LNtYqrVeyoL3+esHRZ4SieE9fKQyyU09uONjnMEjrNBMqT0mbvmA==}
    dependencies:
      babel-runtime: 6.26.0
    dev: false

  /at-least-node@1.0.0:
    resolution: {integrity: sha512-+q/t7Ekv1EDY2l6Gda6LLiX14rU9TV20Wa3ofeQmwPFZbOMo9DXrLbOjFaaclkXKWidIaopwAObQDqwWtGUjqg==}
    engines: {node: '>= 4.0.0'}
    dev: false

  /atob@2.1.2:
    resolution: {integrity: sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg==}
    engines: {node: '>= 4.5.0'}
    hasBin: true
    dev: false

  /autoprefixer@10.4.20(postcss@8.4.47):
    resolution: {integrity: sha512-XY25y5xSv/wEoqzDyXXME4AFfkZI0P23z6Fs3YgymDnKJkCGOnkL0iTxCa85UTqaSgfcqyf3UA6+c7wUvx/16g==}
    engines: {node: ^10 || ^12 || >=14}
    hasBin: true
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      browserslist: 4.23.3
      caniuse-lite: 1.0.30001662
      fraction.js: 4.3.7
      normalize-range: 0.1.2
      picocolors: 1.1.0
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /available-typed-arrays@1.0.7:
    resolution: {integrity: sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      possible-typed-array-names: 1.0.0
    dev: false

  /axios@0.19.2:
    resolution: {integrity: sha512-fjgm5MvRHLhx+osE2xoekY70AhARk3a6hkN+3Io1jc00jtquGvxYlKlsFUhmUET0V5te6CcZI7lcv2Ym61mjHA==}
    deprecated: Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410
    dependencies:
      follow-redirects: 1.5.10
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-helper-vue-jsx-merge-props@2.0.3:
    resolution: {integrity: sha512-gsLiKK7Qrb7zYJNgiXKpXblxbV5ffSwR0f5whkPAaBAR4fhi6bwRZxX9wBlIc5M/v8CCkXUbXZL4N/nSE97cqg==}
    dev: false

  /babel-loader@8.4.1(@babel/core@7.25.2)(webpack@4.47.0):
    resolution: {integrity: sha512-nXzRChX+Z1GoE6yWavBQg6jDslyFF3SDjl2paADuoQtQW10JqShJt62R6eJQ5m/pjJFDT8xgKIWSP85OY8eXeA==}
    engines: {node: '>= 8.9'}
    peerDependencies:
      '@babel/core': ^7.0.0
      webpack: '>=2'
    dependencies:
      '@babel/core': 7.25.2
      find-cache-dir: 3.3.2
      loader-utils: 2.0.4
      make-dir: 3.1.0
      schema-utils: 2.7.1
      webpack: 4.47.0
    dev: false

  /babel-plugin-polyfill-corejs2@0.4.11(@babel/core@7.25.2):
    resolution: {integrity: sha512-sMEJ27L0gRHShOh5G54uAAPaiCOygY/5ratXuiyb2G46FmlSpc9eFCzYVyDiPxfNbwzA7mYahmjQc5q+CZQ09Q==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/compat-data': 7.25.4
      '@babel/core': 7.25.2
      '@babel/helper-define-polyfill-provider': 0.6.2(@babel/core@7.25.2)
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-plugin-polyfill-corejs3@0.10.6(@babel/core@7.25.2):
    resolution: {integrity: sha512-b37+KR2i/khY5sKmWNVQAnitvquQbNdWy6lJdsr0kmquCKEEUgMKK4SboVM3HtfnZilfjr4MMQ7vY58FVWDtIA==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-define-polyfill-provider': 0.6.2(@babel/core@7.25.2)
      core-js-compat: 3.38.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-plugin-polyfill-regenerator@0.6.2(@babel/core@7.25.2):
    resolution: {integrity: sha512-2R25rQZWP63nGwaAswvDazbPXfrM3HwVoBXK6HcqeKrSrL/JqcC/rDcf95l4r7LXLyxDXc8uQDa064GubtCABg==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/core': 7.25.2
      '@babel/helper-define-polyfill-provider': 0.6.2(@babel/core@7.25.2)
    transitivePeerDependencies:
      - supports-color
    dev: false

  /babel-runtime@6.26.0:
    resolution: {integrity: sha512-ITKNuq2wKlW1fJg9sSW52eepoYgZBggvOAHC0u/CYu/qxQ9EVzThCgR69BnSXLHjy2f7SY5zaQ4yt7H9ZVxY2g==}
    dependencies:
      core-js: 2.6.12
      regenerator-runtime: 0.11.1
    dev: false

  /balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}
    dev: false

  /base64-js@1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==}
    dev: false

  /base@0.11.2:
    resolution: {integrity: sha512-5T6P4xPgpp0YDFvSWwEZ4NoE3aM4QBQXDzmVbraCkFj8zHM+mba8SyqB5DbZWyR7mYHo6Y7BdQo3MoA4m0TeQg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      cache-base: 1.0.1
      class-utils: 0.3.6
      component-emitter: 1.3.1
      define-property: 1.0.0
      isobject: 3.0.1
      mixin-deep: 1.3.2
      pascalcase: 0.1.1
    dev: false

  /big.js@5.2.2:
    resolution: {integrity: sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ==}
    dev: false

  /binary-extensions@1.13.1:
    resolution: {integrity: sha512-Un7MIEDdUC5gNpcGDV97op1Ywk748MpHcFTHoYs6qnj1Z3j7I53VG3nwZhKzoBZmbdRNnb6WRdFlwl7tSDuZGw==}
    engines: {node: '>=0.10.0'}
    requiresBuild: true
    dev: false
    optional: true

  /binary-extensions@2.3.0:
    resolution: {integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==}
    engines: {node: '>=8'}
    dev: false

  /bindings@1.5.0:
    resolution: {integrity: sha512-p2q/t/mhvuOj/UeLlV6566GD/guowlr0hHxClI0W9m7MWYkL1F0hLo+0Aexs9HSPCtR1SXQ0TD3MMKrXZajbiQ==}
    requiresBuild: true
    dependencies:
      file-uri-to-path: 1.0.0
    dev: false
    optional: true

  /bluebird@3.7.2:
    resolution: {integrity: sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg==}
    dev: false

  /bn.js@4.12.0:
    resolution: {integrity: sha512-c98Bf3tPniI+scsdk237ku1Dc3ujXQTSgyiPUDEOe7tRkhrqridvh8klBv0HCEso1OLOYcHuCv/cS6DNxKH+ZA==}
    dev: false

  /bn.js@5.2.1:
    resolution: {integrity: sha512-eXRvHzWyYPBuB4NBy0cmYQjGitUrtqwbvlzP3G6VFnNRbsZQIxQ10PbKKHt8gZ/HW/D/747aDl+QkDqg3KQLMQ==}
    dev: false

  /boolbase@1.0.0:
    resolution: {integrity: sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==}
    dev: false

  /boxen@5.1.2:
    resolution: {integrity: sha512-9gYgQKXx+1nP8mP7CzFyaUARhg7D3n1dF/FnErWmu9l6JvGpNUN278h0aSb+QjoiKSWG+iZ3uHrcqk0qrY9RQQ==}
    engines: {node: '>=10'}
    dependencies:
      ansi-align: 3.0.1
      camelcase: 6.3.0
      chalk: 4.1.2
      cli-boxes: 2.2.1
      string-width: 4.2.3
      type-fest: 0.20.2
      widest-line: 3.1.0
      wrap-ansi: 7.0.0
    dev: false

  /brace-expansion@1.1.11:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==}
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1
    dev: false

  /brace-expansion@2.0.1:
    resolution: {integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==}
    dependencies:
      balanced-match: 1.0.2
    dev: false

  /braces@2.3.2:
    resolution: {integrity: sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w==}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-flatten: 1.1.0
      array-unique: 0.3.2
      extend-shallow: 2.0.1
      fill-range: 4.0.0
      isobject: 3.0.1
      repeat-element: 1.1.4
      snapdragon: 0.8.2
      snapdragon-node: 2.1.1
      split-string: 3.1.0
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /braces@3.0.3:
    resolution: {integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==}
    engines: {node: '>=8'}
    dependencies:
      fill-range: 7.1.1
    dev: false

  /brorand@1.1.0:
    resolution: {integrity: sha512-cKV8tMCEpQs4hK/ik71d6LrPOnpkpGBR0wzxqr68g2m/LB2GxVYQroAjMJZRVM1Y4BCjCKc3vAamxSzOY2RP+w==}
    dev: false

  /browserify-aes@1.2.0:
    resolution: {integrity: sha512-+7CHXqGuspUn/Sl5aO7Ea0xWGAtETPXNSAjHo48JfLdPWcMng33Xe4znFvQweqc/uzk5zSOI3H52CYnjCfb5hA==}
    dependencies:
      buffer-xor: 1.0.3
      cipher-base: 1.0.4
      create-hash: 1.2.0
      evp_bytestokey: 1.0.3
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: false

  /browserify-cipher@1.0.1:
    resolution: {integrity: sha512-sPhkz0ARKbf4rRQt2hTpAHqn47X3llLkUGn+xEJzLjwY8LRs2p0v7ljvI5EyoRO/mexrNunNECisZs+gw2zz1w==}
    dependencies:
      browserify-aes: 1.2.0
      browserify-des: 1.0.2
      evp_bytestokey: 1.0.3
    dev: false

  /browserify-des@1.0.2:
    resolution: {integrity: sha512-BioO1xf3hFwz4kc6iBhI3ieDFompMhrMlnDFC4/0/vd5MokpuAc3R+LYbwTA9A5Yc9pq9UYPqffKpW2ObuwX5A==}
    dependencies:
      cipher-base: 1.0.4
      des.js: 1.1.0
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: false

  /browserify-rsa@4.1.0:
    resolution: {integrity: sha512-AdEER0Hkspgno2aR97SAf6vi0y0k8NuOpGnVH3O99rcA5Q6sh8QxcngtHuJ6uXwnfAXNM4Gn1Gb7/MV1+Ymbog==}
    dependencies:
      bn.js: 5.2.1
      randombytes: 2.1.0
    dev: false

  /browserify-sign@4.2.3:
    resolution: {integrity: sha512-JWCZW6SKhfhjJxO8Tyiiy+XYB7cqd2S5/+WeYHsKdNKFlCBhKbblba1A/HN/90YwtxKc8tCErjffZl++UNmGiw==}
    engines: {node: '>= 0.12'}
    dependencies:
      bn.js: 5.2.1
      browserify-rsa: 4.1.0
      create-hash: 1.2.0
      create-hmac: 1.1.7
      elliptic: 6.5.7
      hash-base: 3.0.4
      inherits: 2.0.4
      parse-asn1: 5.1.7
      readable-stream: 2.3.8
      safe-buffer: 5.2.1
    dev: false

  /browserify-zlib@0.2.0:
    resolution: {integrity: sha512-Z942RysHXmJrhqk88FmKBVq/v5tqmSkDz7p54G/MGyjMnCFFnC79XWNbg+Vta8W6Wb2qtSZTSxIGkJrRpCFEiA==}
    dependencies:
      pako: 1.0.11
    dev: false

  /browserslist@4.23.3:
    resolution: {integrity: sha512-btwCFJVjI4YWDNfau8RhZ+B1Q/VLoUITrm3RlP6y1tYGWIOa+InuYiRGXUBXo8nA1qKmHMyLB/iVQg5TT4eFoA==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true
    dependencies:
      caniuse-lite: 1.0.30001662
      electron-to-chromium: 1.5.26
      node-releases: 2.0.18
      update-browserslist-db: 1.1.0(browserslist@4.23.3)
    dev: false

  /buffer-from@1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==}
    dev: false

  /buffer-json@2.0.0:
    resolution: {integrity: sha512-+jjPFVqyfF1esi9fvfUs3NqM0pH1ziZ36VP4hmA/y/Ssfo/5w5xHKfTw9BwQjoJ1w/oVtpLomqwUHKdefGyuHw==}
    dev: false

  /buffer-xor@1.0.3:
    resolution: {integrity: sha512-571s0T7nZWK6vB67HI5dyUF7wXiNcfaPPPTl6zYCNApANjIvYJTg7hlud/+cJpdAhS7dVzqMLmfhfHR3rAcOjQ==}
    dev: false

  /buffer@4.9.2:
    resolution: {integrity: sha512-xq+q3SRMOxGivLhBNaUdC64hDTQwejJ+H0T/NB1XMtTVEwNTrfFF3gAxiyW0Bu/xWEGhjVKgUcMhCrUy2+uCWg==}
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1
      isarray: 1.0.0
    dev: false

  /builtin-status-codes@3.0.0:
    resolution: {integrity: sha512-HpGFw18DgFWlncDfjTa2rcQ4W88O1mC8e8yZ2AvQY5KDaktSTwo+KRf6nHK6FRI5FyRyb/5T6+TSxfP7QyGsmQ==}
    dev: false

  /bytes@3.0.0:
    resolution: {integrity: sha512-pMhOfFDPiv9t5jjIXkHosWmkSyQbvsgEVNkz0ERHbuLh2T/7j4Mqqpz523Fe8MVY89KC6Sh/QfS2sM+SjgFDcw==}
    engines: {node: '>= 0.8'}
    dev: false

  /cacache@12.0.4:
    resolution: {integrity: sha512-a0tMB40oefvuInr4Cwb3GerbL9xTj1D5yg0T5xrjGCGyfvbxseIXX7BAO/u/hIXdafzOI5JC3wDwHyf24buOAQ==}
    dependencies:
      bluebird: 3.7.2
      chownr: 1.1.4
      figgy-pudding: 3.5.2
      glob: 7.2.3
      graceful-fs: 4.2.11
      infer-owner: 1.0.4
      lru-cache: 5.1.1
      mississippi: 3.0.0
      mkdirp: 0.5.6
      move-concurrently: 1.0.1
      promise-inflight: 1.0.1(bluebird@3.7.2)
      rimraf: 2.7.1
      ssri: 6.0.2
      unique-filename: 1.1.1
      y18n: 4.0.3
    dev: false

  /cacache@15.3.0:
    resolution: {integrity: sha512-VVdYzXEn+cnbXpFgWs5hTT7OScegHVmLhJIR8Ufqk3iFD6A6j5iSX1KuBTfNEv4tdJWE2PzA6IVFtcLC7fN9wQ==}
    engines: {node: '>= 10'}
    dependencies:
      '@npmcli/fs': 1.1.1
      '@npmcli/move-file': 1.1.2
      chownr: 2.0.0
      fs-minipass: 2.1.0
      glob: 7.2.3
      infer-owner: 1.0.4
      lru-cache: 6.0.0
      minipass: 3.3.6
      minipass-collect: 1.0.2
      minipass-flush: 1.0.5
      minipass-pipeline: 1.2.4
      mkdirp: 1.0.4
      p-map: 4.0.0
      promise-inflight: 1.0.1(bluebird@3.7.2)
      rimraf: 3.0.2
      ssri: 8.0.1
      tar: 6.2.1
      unique-filename: 1.1.1
    transitivePeerDependencies:
      - bluebird
    dev: false

  /cache-base@1.0.1:
    resolution: {integrity: sha512-AKcdTnFSWATd5/GCPRxr2ChwIJ85CeyrEyjRHlKxQ56d4XJMGym0uAiKn0xbLOGOl3+yRpOTi484dVCEc5AUzQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      collection-visit: 1.0.0
      component-emitter: 1.3.1
      get-value: 2.0.6
      has-value: 1.0.0
      isobject: 3.0.1
      set-value: 2.0.1
      to-object-path: 0.3.0
      union-value: 1.0.1
      unset-value: 1.0.0
    dev: false

  /cache-loader@4.1.0(webpack@4.47.0):
    resolution: {integrity: sha512-ftOayxve0PwKzBF/GLsZNC9fJBXl8lkZE3TOsjkboHfVHVkL39iUEs1FO07A33mizmci5Dudt38UZrrYXDtbhw==}
    engines: {node: '>= 8.9.0'}
    peerDependencies:
      webpack: ^4.0.0
    dependencies:
      buffer-json: 2.0.0
      find-cache-dir: 3.3.2
      loader-utils: 1.4.2
      mkdirp: 0.5.6
      neo-async: 2.6.2
      schema-utils: 2.7.1
      webpack: 4.47.0
    dev: false

  /call-bind@1.0.7:
    resolution: {integrity: sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-define-property: 1.0.0
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      set-function-length: 1.2.2
    dev: false

  /callsites@3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}
    dev: false

  /camel-case@4.1.2:
    resolution: {integrity: sha512-gxGWBrTT1JuMx6R+o5PTXMmUnhnVzLQ9SNutD4YqKtI6ap897t3tKECYla6gCWEkplXnlNybEkZg9GEGxKFCgw==}
    dependencies:
      pascal-case: 3.1.2
      tslib: 2.7.0
    dev: false

  /camelcase@5.3.1:
    resolution: {integrity: sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==}
    engines: {node: '>=6'}
    dev: false

  /camelcase@6.3.0:
    resolution: {integrity: sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==}
    engines: {node: '>=10'}
    dev: false

  /caniuse-api@3.0.0:
    resolution: {integrity: sha512-bsTwuIg/BZZK/vreVTYYbSWoe2F+71P7K5QGEX+pT250DZbfU1MQ5prOKpPR+LL6uWKK3KMwMCAS74QB3Um1uw==}
    dependencies:
      browserslist: 4.23.3
      caniuse-lite: 1.0.30001662
      lodash.memoize: 4.1.2
      lodash.uniq: 4.5.0
    dev: false

  /caniuse-lite@1.0.30001662:
    resolution: {integrity: sha512-sgMUVwLmGseH8ZIrm1d51UbrhqMCH3jvS7gF/M6byuHOnKyLOBL7W8yz5V02OHwgLGA36o/AFhWzzh4uc5aqTA==}
    dev: false

  /cfb@1.2.2:
    resolution: {integrity: sha512-KfdUZsSOw19/ObEWasvBP/Ac4reZvAGauZhs6S/gqNhXhI7cKwvlH7ulj+dOEYnca4bm4SGo8C1bTAQvnTjgQA==}
    engines: {node: '>=0.8'}
    dependencies:
      adler-32: 1.3.1
      crc-32: 1.2.2
    dev: false

  /chalk@2.4.2:
    resolution: {integrity: sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==}
    engines: {node: '>=4'}
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0
    dev: false

  /chalk@4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0
    dev: false

  /chardet@0.7.0:
    resolution: {integrity: sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA==}
    dev: false

  /cheerio-select@2.1.0:
    resolution: {integrity: sha512-9v9kG0LvzrlcungtnJtpGNxY+fzECQKhK4EGJX2vByejiMX84MFNQw4UxPJl3bFbTMw+Dfs37XaIkCwTZfLh4g==}
    dependencies:
      boolbase: 1.0.0
      css-select: 5.1.0
      css-what: 6.1.0
      domelementtype: 2.3.0
      domhandler: 5.0.3
      domutils: 3.2.2
    dev: false

  /cheerio@1.1.0:
    resolution: {integrity: sha512-+0hMx9eYhJvWbgpKV9hN7jg0JcwydpopZE4hgi+KvQtByZXPp04NiCWU0LzcAbP63abZckIHkTQaXVF52mX3xQ==}
    engines: {node: '>=18.17'}
    dependencies:
      cheerio-select: 2.1.0
      dom-serializer: 2.0.0
      domhandler: 5.0.3
      domutils: 3.2.2
      encoding-sniffer: 0.2.1
      htmlparser2: 10.0.0
      parse5: 7.3.0
      parse5-htmlparser2-tree-adapter: 7.1.0
      parse5-parser-stream: 7.1.2
      undici: 7.11.0
      whatwg-mimetype: 4.0.0
    dev: false

  /chokidar@2.1.8:
    resolution: {integrity: sha512-ZmZUazfOzf0Nve7duiCKD23PFSCs4JPoYyccjUFF3aQkQadqBhfzhjkwBH2mNOG9cTBwhamM37EIsIkZw3nRgg==}
    requiresBuild: true
    dependencies:
      anymatch: 2.0.0
      async-each: 1.0.6
      braces: 2.3.2
      glob-parent: 3.1.0
      inherits: 2.0.4
      is-binary-path: 1.0.1
      is-glob: 4.0.3
      normalize-path: 3.0.0
      path-is-absolute: 1.0.1
      readdirp: 2.2.1
      upath: 1.2.0
    optionalDependencies:
      fsevents: 1.2.13
    transitivePeerDependencies:
      - supports-color
    dev: false
    optional: true

  /chokidar@3.6.0:
    resolution: {integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==}
    engines: {node: '>= 8.10.0'}
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3
    dev: false

  /chownr@1.1.4:
    resolution: {integrity: sha512-jJ0bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg==}
    dev: false

  /chownr@2.0.0:
    resolution: {integrity: sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==}
    engines: {node: '>=10'}
    dev: false

  /chrome-trace-event@1.0.4:
    resolution: {integrity: sha512-rNjApaLzuwaOTjCiT8lSDdGN1APCiqkChLMJxJPWLunPAt5fy8xgU9/jNOchV84wfIxrA0lRQB7oCT8jrn/wrQ==}
    engines: {node: '>=6.0'}
    dev: false

  /ci-info@3.9.0:
    resolution: {integrity: sha512-NIxF55hv4nSqQswkAeiOi1r83xy8JldOFDTWiug55KBu9Jnblncd2U6ViHmYgHf01TPZS77NJBhBMKdWj9HQMQ==}
    engines: {node: '>=8'}
    dev: false

  /cipher-base@1.0.4:
    resolution: {integrity: sha512-Kkht5ye6ZGmwv40uUDZztayT2ThLQGfnj/T71N/XzeZeo3nf8foyW7zGTsPYkEya3m5f3cAypH+qe7YOrM1U2Q==}
    dependencies:
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: false

  /class-utils@0.3.6:
    resolution: {integrity: sha512-qOhPa/Fj7s6TY8H8esGu5QNpMMQxz79h+urzrNYN6mn+9BnxlDGf5QZ+XeCDsxSjPqsSR56XOZOJmpeurnLMeg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-union: 3.1.0
      define-property: 0.2.5
      isobject: 3.0.1
      static-extend: 0.1.2
    dev: false

  /clean-css@4.2.4:
    resolution: {integrity: sha512-EJUDT7nDVFDvaQgAo2G/PJvxmp1o/c6iXLbswsBbUFXi1Nr+AjA2cKmfbKDMjMvzEe75g3P6JkaDDAKk96A85A==}
    engines: {node: '>= 4.0'}
    dependencies:
      source-map: 0.6.1
    dev: false

  /clean-css@5.3.3:
    resolution: {integrity: sha512-D5J+kHaVb/wKSFcyyV75uCn8fiY4sV38XJoe4CUyGQ+mOU/fMVYUdH1hJC+CJQ5uY3EnW27SbJYS4X8BiLrAFg==}
    engines: {node: '>= 10.0'}
    dependencies:
      source-map: 0.6.1
    dev: false

  /clean-stack@2.2.0:
    resolution: {integrity: sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A==}
    engines: {node: '>=6'}
    dev: false

  /cli-boxes@2.2.1:
    resolution: {integrity: sha512-y4coMcylgSCdVinjiDBuR8PCC2bLjyGTwEmPb9NHR/QaNU6EUOXcTY/s6VjGMD6ENSEaeQYHCY0GNGS5jfMwPw==}
    engines: {node: '>=6'}
    dev: false

  /cli-cursor@3.1.0:
    resolution: {integrity: sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==}
    engines: {node: '>=8'}
    dependencies:
      restore-cursor: 3.1.0
    dev: false

  /cli-width@3.0.0:
    resolution: {integrity: sha512-FxqpkPPwu1HjuN93Omfm4h8uIanXofW0RxVEW3k5RKx+mJJYSthzNhp32Kzxxy3YAEZ/Dc/EWN1vZRY0+kOhbw==}
    engines: {node: '>= 10'}
    dev: false

  /clipboard@2.0.11:
    resolution: {integrity: sha512-C+0bbOqkezLIsmWSvlsXS0Q0bmkugu7jcfMIACB+RDEntIzQIkdr148we28AfSloQLRdZlYL/QYyrq05j/3Faw==}
    dependencies:
      good-listener: 1.2.2
      select: 1.1.2
      tiny-emitter: 2.1.0
    dev: false

  /clone@2.1.2:
    resolution: {integrity: sha512-3Pe/CF1Nn94hyhIYpjtiLhdCoEoz0DqQ+988E9gmeEdQZlojxnOb74wctFyuwWQHzqyf9X7C7MG8juUpqBJT8w==}
    engines: {node: '>=0.8'}
    dev: false

  /codepage@1.14.0:
    resolution: {integrity: sha512-iz3zJLhlrg37/gYRWgEPkaFTtzmnEv1h+r7NgZum2lFElYQPi0/5bnmuDfODHxfp0INEfnRqyfyeIJDbb7ahRw==}
    engines: {node: '>=0.8'}
    hasBin: true
    dependencies:
      commander: 2.14.1
      exit-on-epipe: 1.0.1
    dev: false

  /collection-visit@1.0.0:
    resolution: {integrity: sha512-lNkKvzEeMBBjUGHZ+q6z9pSJla0KWAQPvtzhEV9+iGyQYG+pBpl7xKDhxoNSOZH2hhv0v5k0y2yAM4o4SjoSkw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      map-visit: 1.0.0
      object-visit: 1.0.1
    dev: false

  /color-convert@1.9.3:
    resolution: {integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==}
    dependencies:
      color-name: 1.1.3
    dev: false

  /color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}
    dependencies:
      color-name: 1.1.4
    dev: false

  /color-name@1.1.3:
    resolution: {integrity: sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==}
    dev: false

  /color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}
    dev: false

  /color-string@1.9.1:
    resolution: {integrity: sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==}
    dependencies:
      color-name: 1.1.4
      simple-swizzle: 0.2.2
    dev: false

  /color@4.2.3:
    resolution: {integrity: sha512-1rXeuUUiGGrykh+CeBdu5Ie7OJwinCgQY0bc7GCRxy5xVHy+moaqkpL/jqQq0MtQOeYcrqEz4abc5f0KtU7W4A==}
    engines: {node: '>=12.5.0'}
    dependencies:
      color-convert: 2.0.1
      color-string: 1.9.1
    dev: false

  /colord@2.9.3:
    resolution: {integrity: sha512-jeC1axXpnb0/2nn/Y1LPuLdgXBLH7aDcHu4KEKfqw3CUhX7ZpfBSlPKyqXE6btIgEzfWtrX3/tyBCaCvXvMkOw==}
    dev: false

  /colorette@2.0.20:
    resolution: {integrity: sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==}
    dev: false

  /commander@10.0.1:
    resolution: {integrity: sha512-y4Mg2tXshplEbSGzx7amzPwKKOCGuoSRP/CjEdwwk0FOGlUbq6lKuoyDZTNZkmxHdJtp54hdfY/JUrdL7Xfdug==}
    engines: {node: '>=14'}
    dev: false

  /commander@2.14.1:
    resolution: {integrity: sha512-+YR16o3rK53SmWHU3rEM3tPAh2rwb1yPcQX5irVn7mb0gXbwuCCrnkbV5+PBfETdfg1vui07nM6PCG1zndcjQw==}
    dev: false

  /commander@2.17.1:
    resolution: {integrity: sha512-wPMUt6FnH2yzG95SA6mzjQOEKUU3aLaDEmzs1ti+1E9h+CsrZghRlqEM/EJ4KscsQVG8uNN4uVreUeT8+drlgg==}
    dev: false

  /commander@2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==}
    dev: false

  /commander@4.1.1:
    resolution: {integrity: sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==}
    engines: {node: '>= 6'}
    dev: false

  /commander@7.2.0:
    resolution: {integrity: sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw==}
    engines: {node: '>= 10'}
    dev: false

  /commondir@1.0.1:
    resolution: {integrity: sha512-W9pAhw0ja1Edb5GVdIF1mjZw/ASI0AlShXM83UUGe2DVr5TdAPEA1OA8m/g8zWp9x6On7gqufY+FatDbC3MDQg==}
    dev: false

  /component-emitter@1.3.1:
    resolution: {integrity: sha512-T0+barUSQRTUQASh8bx02dl+DhF54GtIDY13Y3m9oWTklKbb3Wv974meRpeZ3lp1JpLVECWWNHC4vaG2XHXouQ==}
    dev: false

  /compressible@2.0.18:
    resolution: {integrity: sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg==}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: 1.53.0
    dev: false

  /compression@1.7.4:
    resolution: {integrity: sha512-jaSIDzP9pZVS4ZfQ+TzvtiWhdpFhE2RDHz8QJkpX9SIpLq88VueF5jJw6t+6CUQcAoA6t+x89MLrWAqpfDE8iQ==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      accepts: 1.3.8
      bytes: 3.0.0
      compressible: 2.0.18
      debug: 2.6.9
      on-headers: 1.0.2
      safe-buffer: 5.1.2
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /concat-map@0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}
    dev: false

  /concat-stream@1.6.2:
    resolution: {integrity: sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw==}
    engines: {'0': node >= 0.8}
    dependencies:
      buffer-from: 1.1.2
      inherits: 2.0.4
      readable-stream: 2.3.8
      typedarray: 0.0.6
    dev: false

  /connect@3.7.0:
    resolution: {integrity: sha512-ZqRXc+tZukToSNmh5C2iWMSoV3X1YUcPbqEM4DkEG5tNQXrQUZCNVGGv3IuicnkMtPfGf3Xtp8WCXs295iQ1pQ==}
    engines: {node: '>= 0.10.0'}
    dependencies:
      debug: 2.6.9
      finalhandler: 1.1.2
      parseurl: 1.3.3
      utils-merge: 1.0.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /consola@3.2.3:
    resolution: {integrity: sha512-I5qxpzLv+sJhTVEoLYNcTW+bThDCPsit0vLNKShZx6rLtpilNpmmeTPaeqJb9ZE9dV3DGaeby6Vuhrw38WjeyQ==}
    engines: {node: ^14.18.0 || >=16.10.0}
    dev: false

  /console-browserify@1.2.0:
    resolution: {integrity: sha512-ZMkYO/LkF17QvCPqM0gxw8yUzigAOZOSWSHg91FH6orS7vcEj5dVZTidN2fQ14yBSdg97RqhSNwLUXInd52OTA==}
    dev: false

  /consolidate@0.15.1(lodash@4.17.21):
    resolution: {integrity: sha512-DW46nrsMJgy9kqAbPt5rKaCr7uFtpo4mSUvLHIUbJEjm0vo+aY5QLwBUq3FK4tRnJr/X0Psc0C4jf/h+HtXSMw==}
    engines: {node: '>= 0.10.0'}
    deprecated: Please upgrade to consolidate v1.0.0+ as it has been modernized with several long-awaited fixes implemented. Maintenance is supported by Forward Email at https://forwardemail.net ; follow/watch https://github.com/ladjs/consolidate for updates and release changelog
    peerDependencies:
      arc-templates: ^0.5.3
      atpl: '>=0.7.6'
      babel-core: ^6.26.3
      bracket-template: ^1.1.5
      coffee-script: ^1.12.7
      dot: ^1.1.3
      dust: ^0.3.0
      dustjs-helpers: ^1.7.4
      dustjs-linkedin: ^2.7.5
      eco: ^1.1.0-rc-3
      ect: ^0.5.9
      ejs: ^3.1.5
      haml-coffee: ^1.14.1
      hamlet: ^0.3.3
      hamljs: ^0.6.2
      handlebars: ^4.7.6
      hogan.js: ^3.0.2
      htmling: ^0.0.8
      jade: ^1.11.0
      jazz: ^0.0.18
      jqtpl: ~1.1.0
      just: ^0.1.8
      liquid-node: ^3.0.1
      liquor: ^0.0.5
      lodash: ^4.17.20
      marko: ^3.14.4
      mote: ^0.2.0
      mustache: ^3.0.0
      nunjucks: ^3.2.2
      plates: ~0.4.11
      pug: ^3.0.0
      qejs: ^3.0.5
      ractive: ^1.3.12
      razor-tmpl: ^1.3.1
      react: ^16.13.1
      react-dom: ^16.13.1
      slm: ^2.0.0
      squirrelly: ^5.1.0
      swig: ^1.4.2
      swig-templates: ^2.0.3
      teacup: ^2.0.0
      templayed: '>=0.2.3'
      then-jade: '*'
      then-pug: '*'
      tinyliquid: ^0.2.34
      toffee: ^0.3.6
      twig: ^1.15.2
      twing: ^5.0.2
      underscore: ^1.11.0
      vash: ^0.13.0
      velocityjs: ^2.0.1
      walrus: ^0.10.1
      whiskers: ^0.4.0
    peerDependenciesMeta:
      arc-templates:
        optional: true
      atpl:
        optional: true
      babel-core:
        optional: true
      bracket-template:
        optional: true
      coffee-script:
        optional: true
      dot:
        optional: true
      dust:
        optional: true
      dustjs-helpers:
        optional: true
      dustjs-linkedin:
        optional: true
      eco:
        optional: true
      ect:
        optional: true
      ejs:
        optional: true
      haml-coffee:
        optional: true
      hamlet:
        optional: true
      hamljs:
        optional: true
      handlebars:
        optional: true
      hogan.js:
        optional: true
      htmling:
        optional: true
      jade:
        optional: true
      jazz:
        optional: true
      jqtpl:
        optional: true
      just:
        optional: true
      liquid-node:
        optional: true
      liquor:
        optional: true
      lodash:
        optional: true
      marko:
        optional: true
      mote:
        optional: true
      mustache:
        optional: true
      nunjucks:
        optional: true
      plates:
        optional: true
      pug:
        optional: true
      qejs:
        optional: true
      ractive:
        optional: true
      razor-tmpl:
        optional: true
      react:
        optional: true
      react-dom:
        optional: true
      slm:
        optional: true
      squirrelly:
        optional: true
      swig:
        optional: true
      swig-templates:
        optional: true
      teacup:
        optional: true
      templayed:
        optional: true
      then-jade:
        optional: true
      then-pug:
        optional: true
      tinyliquid:
        optional: true
      toffee:
        optional: true
      twig:
        optional: true
      twing:
        optional: true
      underscore:
        optional: true
      vash:
        optional: true
      velocityjs:
        optional: true
      walrus:
        optional: true
      whiskers:
        optional: true
    dependencies:
      bluebird: 3.7.2
      lodash: 4.17.21
    dev: false

  /constants-browserify@1.0.0:
    resolution: {integrity: sha512-xFxOwqIzR/e1k1gLiWEophSCMqXcwVHIH7akf7b/vxcUeGunlj3hvZaaqxwHsTgn+IndtkQJgSztIDWeumWJDQ==}
    dev: false

  /convert-source-map@2.0.0:
    resolution: {integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==}
    dev: false

  /cookie-universal-nuxt@2.2.2:
    resolution: {integrity: sha512-Pr6P5UCzl1EAvPh7z7jFkknBw0KTdykm6gFmPHrH4LV9s3flVmAH0ZP/ZqUXcp6b0SKZfizkn+XR1cO+QinGhQ==}
    dependencies:
      '@types/cookie': 0.3.3
      cookie-universal: 2.2.2
    dev: false

  /cookie-universal@2.2.2:
    resolution: {integrity: sha512-nUXF6HH2YKbn8vGcdSzWJhjRkDHbnbekuVu2nsRu00zYsX7o/H3xGJRlPVoM4wX/8cpJYpyi9nDt+boER0Wjug==}
    dependencies:
      '@types/cookie': 0.3.3
      cookie: 0.4.2
    dev: false

  /cookie@0.3.1:
    resolution: {integrity: sha512-+IJOX0OqlHCszo2mBUq+SrEbCj6w7Kpffqx60zYbPTFaO4+yYgRjHwcZNpWvaTylDHaV7PPmBHzSecZiMhtPgw==}
    engines: {node: '>= 0.6'}
    dev: false

  /cookie@0.4.2:
    resolution: {integrity: sha512-aSWTXFzaKWkvHO1Ny/s+ePFpvKsPnjc551iI41v3ny/ow6tBG5Vd+FuqGNhh1LxOmVzOlGUriIlOaokOvhaStA==}
    engines: {node: '>= 0.6'}
    dev: false

  /copy-anything@2.0.6:
    resolution: {integrity: sha512-1j20GZTsvKNkc4BY3NpMOM8tt///wY3FpIzozTOFO2ffuZcV61nojHXVKIy3WM+7ADCy5FVhdZYHYDdgTU0yJw==}
    dependencies:
      is-what: 3.14.1
    dev: false

  /copy-concurrently@1.0.5:
    resolution: {integrity: sha512-f2domd9fsVDFtaFcbaRZuYXwtdmnzqbADSwhSWYxYB/Q8zsdUUFMXVRwXGDMWmbEzAn1kdRrtI1T/KTFOL4X2A==}
    deprecated: This package is no longer supported.
    dependencies:
      aproba: 1.2.0
      fs-write-stream-atomic: 1.0.10
      iferr: 0.1.5
      mkdirp: 0.5.6
      rimraf: 2.7.1
      run-queue: 1.0.3
    dev: false

  /copy-descriptor@0.1.1:
    resolution: {integrity: sha512-XgZ0pFcakEUlbwQEVNg3+QAis1FyTL3Qel9FYy8pSkQqoG3PNoT0bOCQtOXcOkur21r2Eq2kI+IE+gsmAEVlYw==}
    engines: {node: '>=0.10.0'}
    dev: false

  /core-js-compat@3.38.1:
    resolution: {integrity: sha512-JRH6gfXxGmrzF3tZ57lFx97YARxCXPaMzPo6jELZhv88pBH5VXpQ+y0znKGlFnzuaihqhLbefxSJxWJMPtfDzw==}
    dependencies:
      browserslist: 4.23.3
    dev: false

  /core-js@2.6.12:
    resolution: {integrity: sha512-Kb2wC0fvsWfQrgk8HU5lW6U/Lcs8+9aaYcy4ZFc6DDlo4nZ7n70dEgE5rtR0oG6ufKDUnrwfWL1mXR5ljDatrQ==}
    deprecated: core-js@<3.23.3 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Some versions have web compatibility issues. Please, upgrade your dependencies to the actual version of core-js.
    requiresBuild: true
    dev: false

  /core-js@3.38.1:
    resolution: {integrity: sha512-OP35aUorbU3Zvlx7pjsFdu1rGNnD4pgw/CWoYzRY3t2EzoVT7shKHY1dlAy3f41cGIO7ZDPQimhGFTlEYkG/Hw==}
    requiresBuild: true
    dev: false

  /core-util-is@1.0.3:
    resolution: {integrity: sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==}
    dev: false

  /cosmiconfig@7.1.0:
    resolution: {integrity: sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA==}
    engines: {node: '>=10'}
    dependencies:
      '@types/parse-json': 4.0.2
      import-fresh: 3.3.0
      parse-json: 5.2.0
      path-type: 4.0.0
      yaml: 1.10.2
    dev: false

  /crc-32@1.2.2:
    resolution: {integrity: sha512-ROmzCKrTnOwybPcJApAA6WBWij23HVfGVNKqqrZpuyZOHqK2CwHSvpGuyt/UNNvaIjEd8X5IFGp4Mh+Ie1IHJQ==}
    engines: {node: '>=0.8'}
    hasBin: true
    dev: false

  /crc@4.3.2:
    resolution: {integrity: sha512-uGDHf4KLLh2zsHa8D8hIQ1H/HtFQhyHrc0uhHBcoKGol/Xnb+MPYfUMw7cvON6ze/GUESTudKayDcJC5HnJv1A==}
    engines: {node: '>=12'}
    peerDependencies:
      buffer: '>=6.0.3'
    peerDependenciesMeta:
      buffer:
        optional: true
    dev: false

  /create-ecdh@4.0.4:
    resolution: {integrity: sha512-mf+TCx8wWc9VpuxfP2ht0iSISLZnt0JgWlrOKZiNqyUZWnjIaCIVNQArMHnCZKfEYRg6IM7A+NeJoN8gf/Ws0A==}
    dependencies:
      bn.js: 4.12.0
      elliptic: 6.5.7
    dev: false

  /create-hash@1.2.0:
    resolution: {integrity: sha512-z00bCGNHDG8mHAkP7CtT1qVu+bFQUPjYq/4Iv3C3kWjTFV10zIjfSoeqXo9Asws8gwSHDGj/hl2u4OGIjapeCg==}
    dependencies:
      cipher-base: 1.0.4
      inherits: 2.0.4
      md5.js: 1.3.5
      ripemd160: 2.0.2
      sha.js: 2.4.11
    dev: false

  /create-hmac@1.1.7:
    resolution: {integrity: sha512-MJG9liiZ+ogc4TzUwuvbER1JRdgvUFSB5+VR/g5h82fGaIRWMWddtKBHi7/sVhfjQZ6SehlyhvQYrcYkaUIpLg==}
    dependencies:
      cipher-base: 1.0.4
      create-hash: 1.2.0
      inherits: 2.0.4
      ripemd160: 2.0.2
      safe-buffer: 5.2.1
      sha.js: 2.4.11
    dev: false

  /create-require@1.1.1:
    resolution: {integrity: sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ==}
    dev: false

  /cross-spawn@7.0.3:
    resolution: {integrity: sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==}
    engines: {node: '>= 8'}
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2
    dev: false

  /crypto-browserify@3.12.0:
    resolution: {integrity: sha512-fz4spIh+znjO2VjL+IdhEpRJ3YN6sMzITSBijk6FK2UvTqruSQW+/cCZTSNsMiZNvUeq0CqurF+dAbyiGOY6Wg==}
    dependencies:
      browserify-cipher: 1.0.1
      browserify-sign: 4.2.3
      create-ecdh: 4.0.4
      create-hash: 1.2.0
      create-hmac: 1.1.7
      diffie-hellman: 5.0.3
      inherits: 2.0.4
      pbkdf2: 3.1.2
      public-encrypt: 4.0.3
      randombytes: 2.1.0
      randomfill: 1.0.4
    dev: false

  /css-blank-pseudo@6.0.2(postcss@8.4.47):
    resolution: {integrity: sha512-J/6m+lsqpKPqWHOifAFtKFeGLOzw3jR92rxQcwRUfA/eTuZzKfKlxOmYDx2+tqOPQAueNvBiY8WhAeHu5qNmTg==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.47
      postcss-selector-parser: 6.1.2
    dev: false

  /css-declaration-sorter@6.4.1(postcss@8.4.47):
    resolution: {integrity: sha512-rtdthzxKuyq6IzqX6jEcIzQF/YqccluefyCYheovBOLhFT/drQA9zj/UbRAa9J7C0o6EG6u3E6g+vKkay7/k3g==}
    engines: {node: ^10 || ^12 || >=14}
    peerDependencies:
      postcss: ^8.0.9
    dependencies:
      postcss: 8.4.47
    dev: false

  /css-declaration-sorter@7.2.0(postcss@8.4.47):
    resolution: {integrity: sha512-h70rUM+3PNFuaBDTLe8wF/cdWu+dOZmb7pJt8Z2sedYbAcQVQV/tEchueg3GWxwqS0cxtbxmaHEdkNACqcvsow==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.0.9
    dependencies:
      postcss: 8.4.47
    dev: false

  /css-has-pseudo@6.0.5(postcss@8.4.47):
    resolution: {integrity: sha512-ZTv6RlvJJZKp32jPYnAJVhowDCrRrHUTAxsYSuUPBEDJjzws6neMnzkRblxtgmv1RgcV5dhH2gn7E3wA9Wt6lw==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/selector-specificity': 3.1.1(postcss-selector-parser@6.1.2)
      postcss: 8.4.47
      postcss-selector-parser: 6.1.2
      postcss-value-parser: 4.2.0
    dev: false

  /css-loader@5.2.7(webpack@4.47.0):
    resolution: {integrity: sha512-Q7mOvpBNBG7YrVGMxRxcBJZFL75o+cH2abNASdibkj/fffYD8qWbInZrD0S9ccI6vZclF3DsHE7njGlLtaHbhg==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^4.27.0 || ^5.0.0
    dependencies:
      icss-utils: 5.1.0(postcss@8.4.47)
      loader-utils: 2.0.4
      postcss: 8.4.47
      postcss-modules-extract-imports: 3.1.0(postcss@8.4.47)
      postcss-modules-local-by-default: 4.0.5(postcss@8.4.47)
      postcss-modules-scope: 3.2.0(postcss@8.4.47)
      postcss-modules-values: 4.0.0(postcss@8.4.47)
      postcss-value-parser: 4.2.0
      schema-utils: 3.3.0
      semver: 7.6.3
      webpack: 4.47.0
    dev: false

  /css-prefers-color-scheme@9.0.1(postcss@8.4.47):
    resolution: {integrity: sha512-iFit06ochwCKPRiWagbTa1OAWCvWWVdEnIFd8BaRrgO8YrrNh4RAWUQTFcYX5tdFZgFl1DJ3iiULchZyEbnF4g==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.47
    dev: false

  /css-select@4.3.0:
    resolution: {integrity: sha512-wPpOYtnsVontu2mODhA19JrqWxNsfdatRKd64kmpRbQgh1KtItko5sTnEpPdpSaJszTOhEMlF/RPz28qj4HqhQ==}
    dependencies:
      boolbase: 1.0.0
      css-what: 6.1.0
      domhandler: 4.3.1
      domutils: 2.8.0
      nth-check: 2.1.1
    dev: false

  /css-select@5.1.0:
    resolution: {integrity: sha512-nwoRF1rvRRnnCqqY7updORDsuqKzqYJ28+oSMaJMMgOauh3fvwHqMS7EZpIPqK8GL+g9mKxF1vP/ZjSeNjEVHg==}
    dependencies:
      boolbase: 1.0.0
      css-what: 6.1.0
      domhandler: 5.0.3
      domutils: 3.1.0
      nth-check: 2.1.1
    dev: false

  /css-tree@1.1.3:
    resolution: {integrity: sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q==}
    engines: {node: '>=8.0.0'}
    dependencies:
      mdn-data: 2.0.14
      source-map: 0.6.1
    dev: false

  /css-tree@2.2.1:
    resolution: {integrity: sha512-OA0mILzGc1kCOCSJerOeqDxDQ4HOh+G8NbOJFOTgOCzpw7fCBubk0fEyxp8AgOL/jvLgYA/uV0cMbe43ElF1JA==}
    engines: {node: ^10 || ^12.20.0 || ^14.13.0 || >=15.0.0, npm: '>=7.0.0'}
    dependencies:
      mdn-data: 2.0.28
      source-map-js: 1.2.1
    dev: false

  /css-tree@2.3.1:
    resolution: {integrity: sha512-6Fv1DV/TYw//QF5IzQdqsNDjx/wc8TrMBZsqjL9eW01tWb7R7k/mq+/VXfJCl7SoD5emsJop9cOByJZfs8hYIw==}
    engines: {node: ^10 || ^12.20.0 || ^14.13.0 || >=15.0.0}
    dependencies:
      mdn-data: 2.0.30
      source-map-js: 1.2.1
    dev: false

  /css-what@6.1.0:
    resolution: {integrity: sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw==}
    engines: {node: '>= 6'}
    dev: false

  /cssdb@8.1.1:
    resolution: {integrity: sha512-kRbSRgZoxtZNl5snb3nOzBkFOt5AwnephcUTIEFc2DebKG9PN50/cHarlwOooTxYQ/gxsnKs3BxykhNLmfvyLg==}
    dev: false

  /cssesc@3.0.0:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==}
    engines: {node: '>=4'}
    hasBin: true
    dev: false

  /cssnano-preset-default@5.2.14(postcss@8.4.47):
    resolution: {integrity: sha512-t0SFesj/ZV2OTylqQVOrFgEh5uanxbO6ZAdeCrNsUQ6fVuXwYTxJPNAGvGTxHbD68ldIJNec7PyYZDBrfDQ+6A==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      css-declaration-sorter: 6.4.1(postcss@8.4.47)
      cssnano-utils: 3.1.0(postcss@8.4.47)
      postcss: 8.4.47
      postcss-calc: 8.2.4(postcss@8.4.47)
      postcss-colormin: 5.3.1(postcss@8.4.47)
      postcss-convert-values: 5.1.3(postcss@8.4.47)
      postcss-discard-comments: 5.1.2(postcss@8.4.47)
      postcss-discard-duplicates: 5.1.0(postcss@8.4.47)
      postcss-discard-empty: 5.1.1(postcss@8.4.47)
      postcss-discard-overridden: 5.1.0(postcss@8.4.47)
      postcss-merge-longhand: 5.1.7(postcss@8.4.47)
      postcss-merge-rules: 5.1.4(postcss@8.4.47)
      postcss-minify-font-values: 5.1.0(postcss@8.4.47)
      postcss-minify-gradients: 5.1.1(postcss@8.4.47)
      postcss-minify-params: 5.1.4(postcss@8.4.47)
      postcss-minify-selectors: 5.2.1(postcss@8.4.47)
      postcss-normalize-charset: 5.1.0(postcss@8.4.47)
      postcss-normalize-display-values: 5.1.0(postcss@8.4.47)
      postcss-normalize-positions: 5.1.1(postcss@8.4.47)
      postcss-normalize-repeat-style: 5.1.1(postcss@8.4.47)
      postcss-normalize-string: 5.1.0(postcss@8.4.47)
      postcss-normalize-timing-functions: 5.1.0(postcss@8.4.47)
      postcss-normalize-unicode: 5.1.1(postcss@8.4.47)
      postcss-normalize-url: 5.1.0(postcss@8.4.47)
      postcss-normalize-whitespace: 5.1.1(postcss@8.4.47)
      postcss-ordered-values: 5.1.3(postcss@8.4.47)
      postcss-reduce-initial: 5.1.2(postcss@8.4.47)
      postcss-reduce-transforms: 5.1.0(postcss@8.4.47)
      postcss-svgo: 5.1.0(postcss@8.4.47)
      postcss-unique-selectors: 5.1.1(postcss@8.4.47)
    dev: false

  /cssnano-preset-default@7.0.6(postcss@8.4.47):
    resolution: {integrity: sha512-ZzrgYupYxEvdGGuqL+JKOY70s7+saoNlHSCK/OGn1vB2pQK8KSET8jvenzItcY+kA7NoWvfbb/YhlzuzNKjOhQ==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      browserslist: 4.23.3
      css-declaration-sorter: 7.2.0(postcss@8.4.47)
      cssnano-utils: 5.0.0(postcss@8.4.47)
      postcss: 8.4.47
      postcss-calc: 10.0.2(postcss@8.4.47)
      postcss-colormin: 7.0.2(postcss@8.4.47)
      postcss-convert-values: 7.0.4(postcss@8.4.47)
      postcss-discard-comments: 7.0.3(postcss@8.4.47)
      postcss-discard-duplicates: 7.0.1(postcss@8.4.47)
      postcss-discard-empty: 7.0.0(postcss@8.4.47)
      postcss-discard-overridden: 7.0.0(postcss@8.4.47)
      postcss-merge-longhand: 7.0.4(postcss@8.4.47)
      postcss-merge-rules: 7.0.4(postcss@8.4.47)
      postcss-minify-font-values: 7.0.0(postcss@8.4.47)
      postcss-minify-gradients: 7.0.0(postcss@8.4.47)
      postcss-minify-params: 7.0.2(postcss@8.4.47)
      postcss-minify-selectors: 7.0.4(postcss@8.4.47)
      postcss-normalize-charset: 7.0.0(postcss@8.4.47)
      postcss-normalize-display-values: 7.0.0(postcss@8.4.47)
      postcss-normalize-positions: 7.0.0(postcss@8.4.47)
      postcss-normalize-repeat-style: 7.0.0(postcss@8.4.47)
      postcss-normalize-string: 7.0.0(postcss@8.4.47)
      postcss-normalize-timing-functions: 7.0.0(postcss@8.4.47)
      postcss-normalize-unicode: 7.0.2(postcss@8.4.47)
      postcss-normalize-url: 7.0.0(postcss@8.4.47)
      postcss-normalize-whitespace: 7.0.0(postcss@8.4.47)
      postcss-ordered-values: 7.0.1(postcss@8.4.47)
      postcss-reduce-initial: 7.0.2(postcss@8.4.47)
      postcss-reduce-transforms: 7.0.0(postcss@8.4.47)
      postcss-svgo: 7.0.1(postcss@8.4.47)
      postcss-unique-selectors: 7.0.3(postcss@8.4.47)
    dev: false

  /cssnano-utils@3.1.0(postcss@8.4.47):
    resolution: {integrity: sha512-JQNR19/YZhz4psLX/rQ9M83e3z2Wf/HdJbryzte4a3NSuafyp9w/I4U+hx5C2S9g41qlstH7DEWnZaaj83OuEA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.47
    dev: false

  /cssnano-utils@5.0.0(postcss@8.4.47):
    resolution: {integrity: sha512-Uij0Xdxc24L6SirFr25MlwC2rCFX6scyUmuKpzI+JQ7cyqDEwD42fJ0xfB3yLfOnRDU5LKGgjQ9FA6LYh76GWQ==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.47
    dev: false

  /cssnano@5.1.15(postcss@8.4.47):
    resolution: {integrity: sha512-j+BKgDcLDQA+eDifLx0EO4XSA56b7uut3BQFH+wbSaSTuGLuiyTa/wbRYthUXX8LC9mLg+WWKe8h+qJuwTAbHw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      cssnano-preset-default: 5.2.14(postcss@8.4.47)
      lilconfig: 2.1.0
      postcss: 8.4.47
      yaml: 1.10.2
    dev: false

  /cssnano@7.0.6(postcss@8.4.47):
    resolution: {integrity: sha512-54woqx8SCbp8HwvNZYn68ZFAepuouZW4lTwiMVnBErM3VkO7/Sd4oTOt3Zz3bPx3kxQ36aISppyXj2Md4lg8bw==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      cssnano-preset-default: 7.0.6(postcss@8.4.47)
      lilconfig: 3.1.2
      postcss: 8.4.47
    dev: false

  /csso@4.2.0:
    resolution: {integrity: sha512-wvlcdIbf6pwKEk7vHj8/Bkc0B4ylXZruLvOgs9doS5eOsOpuodOV2zJChSpkp+pRpYQLQMeF04nr3Z68Sta9jA==}
    engines: {node: '>=8.0.0'}
    dependencies:
      css-tree: 1.1.3
    dev: false

  /csso@5.0.5:
    resolution: {integrity: sha512-0LrrStPOdJj+SPCCrGhzryycLjwcgUSHBtxNA8aIDxf0GLsRh1cKYhB00Gd1lDOS4yGH69+SNn13+TWbVHETFQ==}
    engines: {node: ^10 || ^12.20.0 || ^14.13.0 || >=15.0.0, npm: '>=7.0.0'}
    dependencies:
      css-tree: 2.2.1
    dev: false

  /csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}
    dev: false

  /cuint@0.2.2:
    resolution: {integrity: sha512-d4ZVpCW31eWwCMe1YT3ur7mUDnTXbgwyzaL320DrcRT45rfjYxkt5QWLrmOJ+/UEAI2+fQgKe/fCjR8l4TpRgw==}
    dev: false

  /cyclist@1.0.2:
    resolution: {integrity: sha512-0sVXIohTfLqVIW3kb/0n6IiWF3Ifj5nm2XaSrLq2DI6fKIGa2fYAZdk917rUneaeLVpYfFcyXE2ft0fe3remsA==}
    dev: false

  /data-view-buffer@1.0.1:
    resolution: {integrity: sha512-0lht7OugA5x3iJLOWFhWK/5ehONdprk0ISXqVFn/NFrDu+cuc8iADFrGQz5BnRK7LLU3JmkbXSxaqX+/mXYtUA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-data-view: 1.0.1
    dev: false

  /data-view-byte-length@1.0.1:
    resolution: {integrity: sha512-4J7wRJD3ABAzr8wP+OcIcqq2dlUKp4DVflx++hs5h5ZKydWMI6/D/fAot+yh6g2tHh8fLFTvNOaVN357NvSrOQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-data-view: 1.0.1
    dev: false

  /data-view-byte-offset@1.0.0:
    resolution: {integrity: sha512-t/Ygsytq+R995EJ5PZlD4Cu56sWa8InXySaViRzw9apusqsOO2bQP+SbYzAhR0pFKoB+43lYy8rWban9JSuXnA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-data-view: 1.0.1
    dev: false

  /de-indent@1.0.2:
    resolution: {integrity: sha512-e/1zu3xH5MQryN2zdVaF0OrdNLUbvWxzMbi+iNA6Bky7l1RoP8a2fIbRocyHclXt/arDrrR6lL3TqFD9pMQTsg==}
    dev: false

  /debounce@1.2.1:
    resolution: {integrity: sha512-XRRe6Glud4rd/ZGQfiV1ruXSfbvfJedlV9Y6zOlP+2K04vBYiJEte6stfFkCP03aMnY5tsipamumUjL14fofug==}
    dev: false

  /debug@2.6.9:
    resolution: {integrity: sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.0.0
    dev: false

  /debug@3.1.0:
    resolution: {integrity: sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.0.0
    dev: false

  /debug@4.3.7:
    resolution: {integrity: sha512-Er2nc/H7RrMXZBFCEim6TCmMk02Z8vLC2Rbi1KEBggpo0fS6l0S1nnapwmIi3yW/+GOJap1Krg4w0Hg80oCqgQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.3
    dev: false

  /decimal.js@10.4.3:
    resolution: {integrity: sha512-VBBaLc1MgL5XpzgIP7ny5Z6Nx3UrRkIViUkPUdtl9aya5amy3De1gsUUSB1g3+3sExYNjCAsAznmukyxCb1GRA==}
    dev: false

  /decode-uri-component@0.2.2:
    resolution: {integrity: sha512-FqUYQ+8o158GyGTrMFJms9qh3CqTKvAqgqsTnkLI8sKu0028orqBhxNMFkFen0zGyg6epACD32pjVk58ngIErQ==}
    engines: {node: '>=0.10'}
    dev: false

  /deepmerge@1.5.2:
    resolution: {integrity: sha512-95k0GDqvBjZavkuvzx/YqVLv/6YYa17fz6ILMSf7neqQITCPbnfEnQvEgMPNjH4kgobe7+WIL0yJEHku+H3qtQ==}
    engines: {node: '>=0.10.0'}
    dev: false

  /deepmerge@4.3.1:
    resolution: {integrity: sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==}
    engines: {node: '>=0.10.0'}
    dev: false

  /define-data-property@1.1.4:
    resolution: {integrity: sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-define-property: 1.0.0
      es-errors: 1.3.0
      gopd: 1.0.1
    dev: false

  /define-properties@1.2.1:
    resolution: {integrity: sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-data-property: 1.1.4
      has-property-descriptors: 1.0.2
      object-keys: 1.1.1
    dev: false

  /define-property@0.2.5:
    resolution: {integrity: sha512-Rr7ADjQZenceVOAKop6ALkkRAmH1A4Gx9hV/7ZujPUN2rkATqFO0JZLZInbAjpZYoJ1gUx8MRMQVkYemcbMSTA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-descriptor: 0.1.7
    dev: false

  /define-property@1.0.0:
    resolution: {integrity: sha512-cZTYKFWspt9jZsMscWo8sc/5lbPC9Q0N5nBLgb+Yd915iL3udB1uFgS3B8YCx66UVHq018DAVFoee7x+gxggeA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-descriptor: 1.0.3
    dev: false

  /define-property@2.0.2:
    resolution: {integrity: sha512-jwK2UV4cnPpbcG7+VRARKTZPUWowwXA8bzH5NP6ud0oeAxyYPuGZUAC7hMugpCdz4BeSZl2Dl9k66CHJ/46ZYQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-descriptor: 1.0.3
      isobject: 3.0.1
    dev: false

  /defu@5.0.1:
    resolution: {integrity: sha512-EPS1carKg+dkEVy3qNTqIdp2qV7mUP08nIsupfwQpz++slCVRw7qbQyWvSTig+kFPwz2XXp5/kIIkH+CwrJKkQ==}
    dev: false

  /defu@6.1.4:
    resolution: {integrity: sha512-mEQCMmwJu317oSz8CwdIOdwf3xMif1ttiM8LTufzc3g6kR+9Pe236twL8j3IYT1F7GfRgGcW6MWxzZjLIkuHIg==}
    dev: false

  /delegate@3.2.0:
    resolution: {integrity: sha512-IofjkYBZaZivn0V8nnsMJGBr4jVLxHDheKSW88PyxS5QC4Vo9ZbZVvhzlSxY87fVq3STR6r+4cGepyHkcWOQSw==}
    dev: false

  /depd@2.0.0:
    resolution: {integrity: sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==}
    engines: {node: '>= 0.8'}
    dev: false

  /des.js@1.1.0:
    resolution: {integrity: sha512-r17GxjhUCjSRy8aiJpr8/UadFIzMzJGexI3Nmz4ADi9LYSFx4gTBp80+NaX/YsXWWLhpZ7v/v/ubEc/bCNfKwg==}
    dependencies:
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
    dev: false

  /destr@2.0.3:
    resolution: {integrity: sha512-2N3BOUU4gYMpTP24s5rF5iP7BDr7uNTCs4ozw3kf/eKfvWSIu93GEBi5m427YoyJoeOzQ5smuu4nNAPGb8idSQ==}
    dev: false

  /destroy@1.2.0:
    resolution: {integrity: sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}
    dev: false

  /detect-indent@5.0.0:
    resolution: {integrity: sha512-rlpvsxUtM0PQvy9iZe640/IWwWYyBsTApREbA1pHOpmOUIl9MkP/U4z7vTtg4Oaojvqhxt7sdufnT0EzGaR31g==}
    engines: {node: '>=4'}
    dev: false

  /devalue@2.0.1:
    resolution: {integrity: sha512-I2TiqT5iWBEyB8GRfTDP0hiLZ0YeDJZ+upDxjBfOC2lebO5LezQMv7QvIUTzdb64jQyAKLf1AHADtGN+jw6v8Q==}
    dev: false

  /diffie-hellman@5.0.3:
    resolution: {integrity: sha512-kqag/Nl+f3GwyK25fhUMYj81BUOrZ9IuJsjIcDE5icNM9FJHAVm3VcUDxdLPoQtTuUylWm6ZIknYJwwaPxsUzg==}
    dependencies:
      bn.js: 4.12.0
      miller-rabin: 4.0.1
      randombytes: 2.1.0
    dev: false

  /dir-glob@3.0.1:
    resolution: {integrity: sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==}
    engines: {node: '>=8'}
    dependencies:
      path-type: 4.0.0
    dev: false

  /dom-converter@0.2.0:
    resolution: {integrity: sha512-gd3ypIPfOMr9h5jIKq8E3sHOTCjeirnl0WK5ZdS1AW0Odt0b1PaWaHdJ4Qk4klv+YB9aJBS7mESXjFoDQPu6DA==}
    dependencies:
      utila: 0.4.0
    dev: false

  /dom-serializer@1.4.1:
    resolution: {integrity: sha512-VHwB3KfrcOOkelEG2ZOfxqLZdfkil8PtJi4P8N2MMXucZq2yLp75ClViUlOVwyoHEDjYU433Aq+5zWP61+RGag==}
    dependencies:
      domelementtype: 2.3.0
      domhandler: 4.3.1
      entities: 2.2.0
    dev: false

  /dom-serializer@2.0.0:
    resolution: {integrity: sha512-wIkAryiqt/nV5EQKqQpo3SToSOV9J0DnbJqwK7Wv/Trc92zIAYZ4FlMu+JPFW1DfGFt81ZTCGgDEabffXeLyJg==}
    dependencies:
      domelementtype: 2.3.0
      domhandler: 5.0.3
      entities: 4.5.0
    dev: false

  /domain-browser@1.2.0:
    resolution: {integrity: sha512-jnjyiM6eRyZl2H+W8Q/zLMA481hzi0eszAaBUzIVnmYVDBbnLxVNnfu1HgEBvCbL+71FrxMl3E6lpKH7Ge3OXA==}
    engines: {node: '>=0.4', npm: '>=1.2'}
    dev: false

  /domelementtype@2.3.0:
    resolution: {integrity: sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==}
    dev: false

  /domhandler@4.3.1:
    resolution: {integrity: sha512-GrwoxYN+uWlzO8uhUXRl0P+kHE4GtVPfYzVLcUxPL7KNdHKj66vvlhiweIHqYYXWlw+T8iLMp42Lm67ghw4WMQ==}
    engines: {node: '>= 4'}
    dependencies:
      domelementtype: 2.3.0
    dev: false

  /domhandler@5.0.3:
    resolution: {integrity: sha512-cgwlv/1iFQiFnU96XXgROh8xTeetsnJiDsTc7TYCLFd9+/WNkIqPTxiM/8pSd8VIrhXGTf1Ny1q1hquVqDJB5w==}
    engines: {node: '>= 4'}
    dependencies:
      domelementtype: 2.3.0
    dev: false

  /domutils@2.8.0:
    resolution: {integrity: sha512-w96Cjofp72M5IIhpjgobBimYEfoPjx1Vx0BSX9P30WBdZW2WIKU0T1Bd0kz2eNZ9ikjKgHbEyKx8BB6H1L3h3A==}
    dependencies:
      dom-serializer: 1.4.1
      domelementtype: 2.3.0
      domhandler: 4.3.1
    dev: false

  /domutils@3.1.0:
    resolution: {integrity: sha512-H78uMmQtI2AhgDJjWeQmHwJJ2bLPD3GMmO7Zja/ZZh84wkm+4ut+IUnUdRa8uCGX88DiVx1j6FRe1XfxEgjEZA==}
    dependencies:
      dom-serializer: 2.0.0
      domelementtype: 2.3.0
      domhandler: 5.0.3
    dev: false

  /domutils@3.2.2:
    resolution: {integrity: sha512-6kZKyUajlDuqlHKVX1w7gyslj9MPIXzIFiz/rGu35uC1wMi+kMhQwGhl4lt9unC9Vb9INnY9Z3/ZA3+FhASLaw==}
    dependencies:
      dom-serializer: 2.0.0
      domelementtype: 2.3.0
      domhandler: 5.0.3
    dev: false

  /dot-case@3.0.4:
    resolution: {integrity: sha512-Kv5nKlh6yRrdrGvxeJ2e5y2eRUpkUosIW4A2AS38zwSz27zu7ufDwQPi5Jhs3XAlGNetl3bmnGhQsMtkKJnj3w==}
    dependencies:
      no-case: 3.0.4
      tslib: 2.7.0
    dev: false

  /dotenv@16.4.5:
    resolution: {integrity: sha512-ZmdL2rui+eB2YwhsWzjInR8LldtZHGDoQ1ugH85ppHKwpUHL7j7rN0Ti9NCnGiQbhaZ11FpR+7ao1dNsmduNUg==}
    engines: {node: '>=12'}
    dev: false

  /dotenv@9.0.2:
    resolution: {integrity: sha512-I9OvvrHp4pIARv4+x9iuewrWycX6CcZtoAu1XrzPxc5UygMJXJZYmBsynku8IkrJwgypE5DGNjDPmPRhDCptUg==}
    engines: {node: '>=10'}
    dev: false

  /duplexer@0.1.2:
    resolution: {integrity: sha512-jtD6YG370ZCIi/9GTaJKQxWTZD045+4R4hTk/x1UyoqadyJ9x9CgSi1RlVDQF8U2sxLLSnFkCaMihqljHIWgMg==}
    dev: false

  /duplexify@3.7.1:
    resolution: {integrity: sha512-07z8uv2wMyS51kKhD1KsdXJg5WQ6t93RneqRxUHnskXVtlYYkLqM0gqStQZ3pj073g687jPCHrqNfCzawLYh5g==}
    dependencies:
      end-of-stream: 1.4.4
      inherits: 2.0.4
      readable-stream: 2.3.8
      stream-shift: 1.0.3
    dev: false

  /ee-first@1.1.1:
    resolution: {integrity: sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==}
    dev: false

  /electron-to-chromium@1.5.26:
    resolution: {integrity: sha512-Z+OMe9M/V6Ep9n/52+b7lkvYEps26z4Yz3vjWL1V61W0q+VLF1pOHhMY17sa4roz4AWmULSI8E6SAojZA5L0YQ==}
    dev: false

  /element-ui@2.15.14(vue@2.7.16):
    resolution: {integrity: sha512-2v9fHL0ZGINotOlRIAJD5YuVB8V7WKxrE9Qy7dXhRipa035+kF7WuU/z+tEmLVPBcJ0zt8mOu1DKpWcVzBK8IA==}
    peerDependencies:
      vue: ^2.5.17
    dependencies:
      async-validator: 1.8.5
      babel-helper-vue-jsx-merge-props: 2.0.3
      deepmerge: 1.5.2
      normalize-wheel: 1.0.1
      resize-observer-polyfill: 1.5.1
      throttle-debounce: 1.1.0
      vue: 2.7.16
    dev: false

  /elliptic@6.5.7:
    resolution: {integrity: sha512-ESVCtTwiA+XhY3wyh24QqRGBoP3rEdDUl3EDUUo9tft074fi19IrdpH7hLCMMP3CIj7jb3W96rn8lt/BqIlt5Q==}
    dependencies:
      bn.js: 4.12.0
      brorand: 1.1.0
      hash.js: 1.1.7
      hmac-drbg: 1.0.1
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
      minimalistic-crypto-utils: 1.0.1
    dev: false

  /emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}
    dev: false

  /emojis-list@3.0.0:
    resolution: {integrity: sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q==}
    engines: {node: '>= 4'}
    dev: false

  /encodeurl@1.0.2:
    resolution: {integrity: sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==}
    engines: {node: '>= 0.8'}
    dev: false

  /encodeurl@2.0.0:
    resolution: {integrity: sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg==}
    engines: {node: '>= 0.8'}
    dev: false

  /encoding-sniffer@0.2.1:
    resolution: {integrity: sha512-5gvq20T6vfpekVtqrYQsSCFZ1wEg5+wW0/QaZMWkFr6BqD3NfKs0rLCx4rrVlSWJeZb5NBJgVLswK/w2MWU+Gw==}
    dependencies:
      iconv-lite: 0.6.3
      whatwg-encoding: 3.1.1
    dev: false

  /end-of-stream@1.4.4:
    resolution: {integrity: sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==}
    dependencies:
      once: 1.4.0
    dev: false

  /enhanced-resolve@4.5.0:
    resolution: {integrity: sha512-Nv9m36S/vxpsI+Hc4/ZGRs0n9mXqSWGGq49zxb/cJfPAQMbUtttJAlNPS4AQzaBdw/pKskw5bMbekT/Y7W/Wlg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      graceful-fs: 4.2.11
      memory-fs: 0.5.0
      tapable: 1.1.3
    dev: false

  /entities@2.2.0:
    resolution: {integrity: sha512-p92if5Nz619I0w+akJrLZH0MX0Pb5DX39XOwQTtXSdQQOaYH03S1uIQp4mhOZtAXrxq4ViO67YTiLBo2638o9A==}
    dev: false

  /entities@4.5.0:
    resolution: {integrity: sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==}
    engines: {node: '>=0.12'}
    dev: false

  /entities@6.0.1:
    resolution: {integrity: sha512-aN97NXWF6AWBTahfVOIrB/NShkzi5H7F9r1s9mD3cDj4Ko5f2qhhVoYMibXF7GlLveb/D2ioWay8lxI97Ven3g==}
    engines: {node: '>=0.12'}
    dev: false

  /errno@0.1.8:
    resolution: {integrity: sha512-dJ6oBr5SQ1VSd9qkk7ByRgb/1SH4JZjCHSW/mr63/QcXO9zLVxvJ6Oy13nio03rxpSnVDDjFor75SjVeZWPW/A==}
    hasBin: true
    dependencies:
      prr: 1.0.1
    dev: false

  /error-ex@1.3.2:
    resolution: {integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==}
    dependencies:
      is-arrayish: 0.2.1
    dev: false

  /error-stack-parser@2.1.4:
    resolution: {integrity: sha512-Sk5V6wVazPhq5MhpO+AUxJn5x7XSXGl1R93Vn7i+zS15KDVxQijejNCrz8340/2bgLBjR9GtEG8ZVKONDjcqGQ==}
    dependencies:
      stackframe: 1.3.4
    dev: false

  /es-abstract@1.23.3:
    resolution: {integrity: sha512-e+HfNH61Bj1X9/jLc5v1owaLYuHdeHHSQlkhCBiTK8rBvKaULl/beGMxwrMXjpYrv4pz22BlY570vVePA2ho4A==}
    engines: {node: '>= 0.4'}
    dependencies:
      array-buffer-byte-length: 1.0.1
      arraybuffer.prototype.slice: 1.0.3
      available-typed-arrays: 1.0.7
      call-bind: 1.0.7
      data-view-buffer: 1.0.1
      data-view-byte-length: 1.0.1
      data-view-byte-offset: 1.0.0
      es-define-property: 1.0.0
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      es-set-tostringtag: 2.0.3
      es-to-primitive: 1.2.1
      function.prototype.name: 1.1.6
      get-intrinsic: 1.2.4
      get-symbol-description: 1.0.2
      globalthis: 1.0.4
      gopd: 1.0.1
      has-property-descriptors: 1.0.2
      has-proto: 1.0.3
      has-symbols: 1.0.3
      hasown: 2.0.2
      internal-slot: 1.0.7
      is-array-buffer: 3.0.4
      is-callable: 1.2.7
      is-data-view: 1.0.1
      is-negative-zero: 2.0.3
      is-regex: 1.1.4
      is-shared-array-buffer: 1.0.3
      is-string: 1.0.7
      is-typed-array: 1.1.13
      is-weakref: 1.0.2
      object-inspect: 1.13.2
      object-keys: 1.1.1
      object.assign: 4.1.5
      regexp.prototype.flags: 1.5.2
      safe-array-concat: 1.1.2
      safe-regex-test: 1.0.3
      string.prototype.trim: 1.2.9
      string.prototype.trimend: 1.0.8
      string.prototype.trimstart: 1.0.8
      typed-array-buffer: 1.0.2
      typed-array-byte-length: 1.0.1
      typed-array-byte-offset: 1.0.2
      typed-array-length: 1.0.6
      unbox-primitive: 1.0.2
      which-typed-array: 1.1.15
    dev: false

  /es-array-method-boxes-properly@1.0.0:
    resolution: {integrity: sha512-wd6JXUmyHmt8T5a2xreUwKcGPq6f1f+WwIJkijUqiGcJz1qqnZgP6XIK+QyIWU5lT7imeNxUll48bziG+TSYcA==}
    dev: false

  /es-define-property@1.0.0:
    resolution: {integrity: sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      get-intrinsic: 1.2.4
    dev: false

  /es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==}
    engines: {node: '>= 0.4'}
    dev: false

  /es-object-atoms@1.0.0:
    resolution: {integrity: sha512-MZ4iQ6JwHOBQjahnjwaC1ZtIBH+2ohjamzAO3oaHcXYup7qxjF2fixyH+Q71voWHeOkI2q/TnJao/KfXYIZWbw==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
    dev: false

  /es-set-tostringtag@2.0.3:
    resolution: {integrity: sha512-3T8uNMC3OQTHkFUsFq8r/BwAXLHvU/9O9mE0fBc/MY5iq/8H7ncvO947LmYA6ldWw9Uh8Yhf25zu6n7nML5QWQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      get-intrinsic: 1.2.4
      has-tostringtag: 1.0.2
      hasown: 2.0.2
    dev: false

  /es-to-primitive@1.2.1:
    resolution: {integrity: sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA==}
    engines: {node: '>= 0.4'}
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.0.5
      is-symbol: 1.0.4
    dev: false

  /escalade@3.2.0:
    resolution: {integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==}
    engines: {node: '>=6'}
    dev: false

  /escape-html@1.0.3:
    resolution: {integrity: sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==}
    dev: false

  /escape-string-regexp@1.0.5:
    resolution: {integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==}
    engines: {node: '>=0.8.0'}
    dev: false

  /escape-string-regexp@4.0.0:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==}
    engines: {node: '>=10'}
    dev: false

  /eslint-scope@4.0.3:
    resolution: {integrity: sha512-p7VutNr1O/QrxysMo3E45FjYDTeXBy0iTltPFNSqKAIfjDSXC+4dj+qfyuD8bfAXrW/y6lW3O76VaYNPKfpKrg==}
    engines: {node: '>=4.0.0'}
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0
    dev: false

  /esrecurse@4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==}
    engines: {node: '>=4.0'}
    dependencies:
      estraverse: 5.3.0
    dev: false

  /estraverse@4.3.0:
    resolution: {integrity: sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==}
    engines: {node: '>=4.0'}
    dev: false

  /estraverse@5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==}
    engines: {node: '>=4.0'}
    dev: false

  /esutils@2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}
    dev: false

  /etag@1.8.1:
    resolution: {integrity: sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==}
    engines: {node: '>= 0.6'}
    dev: false

  /eventemitter3@4.0.7:
    resolution: {integrity: sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==}
    dev: false

  /events@3.3.0:
    resolution: {integrity: sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==}
    engines: {node: '>=0.8.x'}
    dev: false

  /eventsource-polyfill@0.9.6:
    resolution: {integrity: sha512-LyMFp2oPDGhum2lMvkjqKZEwWd2/AoXyt8aoyftTBMWwPHNgU+2tdxhTHPluDxoz+z4gNj0uHAPR9nqevATMbg==}
    dev: false

  /evp_bytestokey@1.0.3:
    resolution: {integrity: sha512-/f2Go4TognH/KvCISP7OUsHn85hT9nUkxxA9BEWxFn+Oj9o8ZNLm/40hdlgSLyuOimsrTKLUMEorQexp/aPQeA==}
    dependencies:
      md5.js: 1.3.5
      safe-buffer: 5.2.1
    dev: false

  /execa@5.1.1:
    resolution: {integrity: sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==}
    engines: {node: '>=10'}
    dependencies:
      cross-spawn: 7.0.3
      get-stream: 6.0.1
      human-signals: 2.1.0
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0
    dev: false

  /exit-on-epipe@1.0.1:
    resolution: {integrity: sha512-h2z5mrROTxce56S+pnvAV890uu7ls7f1kEvVGJbw1OlFH3/mlJ5bkXu0KRyW94v37zzHPiUd55iLn3DA7TjWpw==}
    engines: {node: '>=0.8'}
    dev: false

  /exit@0.1.2:
    resolution: {integrity: sha512-Zk/eNKV2zbjpKzrsQ+n1G6poVbErQxJ0LBOJXaKZ1EViLzH+hrLu9cdXI4zw9dBQJslwBEpbQ2P1oS7nDxs6jQ==}
    engines: {node: '>= 0.8.0'}
    dev: false

  /expand-brackets@2.1.4:
    resolution: {integrity: sha512-w/ozOKR9Obk3qoWeY/WDi6MFta9AoMR+zud60mdnbniMcBxRuFJyDt2LdX/14A1UABeqk+Uk+LDfUpvoGKppZA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      debug: 2.6.9
      define-property: 0.2.5
      extend-shallow: 2.0.1
      posix-character-classes: 0.1.1
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /extend-shallow@2.0.1:
    resolution: {integrity: sha512-zCnTtlxNoAiDc3gqY2aYAWFx7XWWiasuF2K8Me5WbN8otHKTUKBwjPtNpRs/rbUZm7KxWAaNj7P1a/p52GbVug==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extendable: 0.1.1
    dev: false

  /extend-shallow@3.0.2:
    resolution: {integrity: sha512-BwY5b5Ql4+qZoefgMj2NUmx+tehVTH/Kf4k1ZEtOHNFcm2wSxMRo992l6X3TIgni2eZVTZ85xMOjF31fwZAj6Q==}
    engines: {node: '>=0.10.0'}
    dependencies:
      assign-symbols: 1.0.0
      is-extendable: 1.0.1
    dev: false

  /external-editor@3.1.0:
    resolution: {integrity: sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew==}
    engines: {node: '>=4'}
    dependencies:
      chardet: 0.7.0
      iconv-lite: 0.4.24
      tmp: 0.0.33
    dev: false

  /extglob@2.0.4:
    resolution: {integrity: sha512-Nmb6QXkELsuBr24CJSkilo6UHHgbekK5UiZgfE6UHD3Eb27YC6oD+bhcT+tJ6cl8dmsgdQxnWlcry8ksBIBLpw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      array-unique: 0.3.2
      define-property: 1.0.0
      expand-brackets: 2.1.4
      extend-shallow: 2.0.1
      fragment-cache: 0.2.1
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /extract-css-chunks-webpack-plugin@4.10.0(webpack@4.47.0):
    resolution: {integrity: sha512-D/wb/Tbexq8XMBl4uhthto25WBaHI9P8vucDdzwPtLTyVi4Rdw/aiRLSL2rHaF6jZfPAjThWXepFU9PXsdtIbA==}
    engines: {node: '>= 6.9.0'}
    peerDependencies:
      webpack: ^4.4.0 || ^5.0.0
    dependencies:
      loader-utils: 2.0.4
      normalize-url: 1.9.1
      schema-utils: 1.0.0
      webpack: 4.47.0
      webpack-sources: 1.4.3
    dev: false

  /fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}
    dev: false

  /fast-glob@3.3.2:
    resolution: {integrity: sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow==}
    engines: {node: '>=8.6.0'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8
    dev: false

  /fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}
    dev: false

  /fast-uri@3.0.1:
    resolution: {integrity: sha512-MWipKbbYiYI0UC7cl8m/i/IWTqfC8YXsqjzybjddLsFjStroQzsHXkc73JutMvBiXmOvapk+axIl79ig5t55Bw==}
    dev: false

  /fastq@1.17.1:
    resolution: {integrity: sha512-sRVD3lWVIXWg6By68ZN7vho9a1pQcN/WBFaAAsDDFzlJjvoGx0P8z7V1t72grFJfJhu3YPZBuu25f7Kaw2jN1w==}
    dependencies:
      reusify: 1.0.4
    dev: false

  /fflate@0.3.11:
    resolution: {integrity: sha512-Rr5QlUeGN1mbOHlaqcSYMKVpPbgLy0AWT/W0EHxA6NGI12yO1jpoui2zBBvU2G824ltM6Ut8BFgfHSBGfkmS0A==}
    dev: false

  /figgy-pudding@3.5.2:
    resolution: {integrity: sha512-0btnI/H8f2pavGMN8w40mlSKOfTK2SVJmBfBeVIj3kNw0swwgzyRq0d5TJVOwodFmtvpPeWPN/MCcfuWF0Ezbw==}
    deprecated: This module is no longer supported.
    dev: false

  /figures@3.2.0:
    resolution: {integrity: sha512-yaduQFRKLXYOGgEn6AZau90j3ggSOyiqXU0F9JZfeXYhNa+Jk4X+s45A2zg5jns87GAFa34BBm2kXw4XpNcbdg==}
    engines: {node: '>=8'}
    dependencies:
      escape-string-regexp: 1.0.5
    dev: false

  /file-loader@6.2.0(webpack@4.47.0):
    resolution: {integrity: sha512-qo3glqyTa61Ytg4u73GultjHGjdRyig3tG6lPtyX/jOEJvHif9uB0/OCI2Kif6ctF3caQTW2G5gym21oAsI4pw==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      loader-utils: 2.0.4
      schema-utils: 3.3.0
      webpack: 4.47.0
    dev: false

  /file-saver@2.0.5:
    resolution: {integrity: sha512-P9bmyZ3h/PRG+Nzga+rbdI4OEpNDzAVyy74uVO9ATgzLK6VtAsYybF/+TOCvrc0MO793d6+42lLyZTw7/ArVzA==}
    dev: false

  /file-uri-to-path@1.0.0:
    resolution: {integrity: sha512-0Zt+s3L7Vf1biwWZ29aARiVYLx7iMGnEUl9x33fbB/j3jR81u/O2LbqK+Bm1CDSNDKVtJ/YjwY7TUd5SkeLQLw==}
    requiresBuild: true
    dev: false
    optional: true

  /fill-range@4.0.0:
    resolution: {integrity: sha512-VcpLTWqWDiTerugjj8e3+esbg+skS3M9e54UuR3iCeIDMXCLTsAH8hTSzDQU/X6/6t3eYkOKoZSef2PlU6U1XQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 2.0.1
      is-number: 3.0.0
      repeat-string: 1.6.1
      to-regex-range: 2.1.1
    dev: false

  /fill-range@7.1.1:
    resolution: {integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==}
    engines: {node: '>=8'}
    dependencies:
      to-regex-range: 5.0.1
    dev: false

  /finalhandler@1.1.2:
    resolution: {integrity: sha512-aAWcW57uxVNrQZqFXjITpW3sIUQmHGG3qSb9mUah9MgMC4NeWhNOlNjXEYq3HjRAvL6arUviZGGJsBg6z0zsWA==}
    engines: {node: '>= 0.8'}
    dependencies:
      debug: 2.6.9
      encodeurl: 1.0.2
      escape-html: 1.0.3
      on-finished: 2.3.0
      parseurl: 1.3.3
      statuses: 1.5.0
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /find-cache-dir@2.1.0:
    resolution: {integrity: sha512-Tq6PixE0w/VMFfCgbONnkiQIVol/JJL7nRMi20fqzA4NRs9AfeqMGeRdPi3wIhYkxjeBaWh2rxwapn5Tu3IqOQ==}
    engines: {node: '>=6'}
    dependencies:
      commondir: 1.0.1
      make-dir: 2.1.0
      pkg-dir: 3.0.0
    dev: false

  /find-cache-dir@3.3.2:
    resolution: {integrity: sha512-wXZV5emFEjrridIgED11OoUKLxiYjAcqot/NJdAkOhlJ+vGzwhOAfcG5OX1jP+S0PcjEn8bdMJv+g2jwQ3Onig==}
    engines: {node: '>=8'}
    dependencies:
      commondir: 1.0.1
      make-dir: 3.1.0
      pkg-dir: 4.2.0
    dev: false

  /find-up@3.0.0:
    resolution: {integrity: sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg==}
    engines: {node: '>=6'}
    dependencies:
      locate-path: 3.0.0
    dev: false

  /find-up@4.1.0:
    resolution: {integrity: sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==}
    engines: {node: '>=8'}
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0
    dev: false

  /flush-write-stream@1.1.1:
    resolution: {integrity: sha512-3Z4XhFZ3992uIq0XOqb9AreonueSYphE6oYbpt5+3u06JWklbsPkNv3ZKkP9Bz/r+1MWCaMoSQ28P85+1Yc77w==}
    dependencies:
      inherits: 2.0.4
      readable-stream: 2.3.8
    dev: false

  /focus-lock@0.5.4:
    resolution: {integrity: sha512-A9ngdb0NyI6UygBQ0eD+p8SpLWTkdEDn67I3EGUUcDUfxH694mLA/xBWwhWhoj/2YLtsv2EoQdAx9UOKs8d/ZQ==}
    dev: false

  /follow-redirects@1.15.9:
    resolution: {integrity: sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true
    dev: false

  /follow-redirects@1.5.10:
    resolution: {integrity: sha512-0V5l4Cizzvqt5D44aTXbFZz+FtyXV1vrDN6qrelxtfYQKW0KO0W2T/hkE8xvGa/540LkZlkaUjO4ailYTFtHVQ==}
    engines: {node: '>=4.0'}
    dependencies:
      debug: 3.1.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /for-each@0.3.3:
    resolution: {integrity: sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==}
    dependencies:
      is-callable: 1.2.7
    dev: false

  /for-in@1.0.2:
    resolution: {integrity: sha512-7EwmXrOjyL+ChxMhmG5lnW9MPt1aIeZEwKhQzoBUdTV0N3zuwWDZYVJatDvZ2OyzPUvdIAZDsCetk3coyMfcnQ==}
    engines: {node: '>=0.10.0'}
    dev: false

  /frac@1.1.2:
    resolution: {integrity: sha512-w/XBfkibaTl3YDqASwfDUqkna4Z2p9cFSr1aHDt0WoMTECnRfBOv2WArlZILlqgWlmdIlALXGpM2AOhEk5W3IA==}
    engines: {node: '>=0.8'}
    dev: false

  /fraction.js@4.3.7:
    resolution: {integrity: sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==}
    dev: false

  /fragment-cache@0.2.1:
    resolution: {integrity: sha512-GMBAbW9antB8iZRHLoGw0b3HANt57diZYFO/HL1JGIC1MjKrdmhxvrJbupnVvpys0zsz7yBApXdQyfepKly2kA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      map-cache: 0.2.2
    dev: false

  /fresh@0.5.2:
    resolution: {integrity: sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==}
    engines: {node: '>= 0.6'}
    dev: false

  /from2@2.3.0:
    resolution: {integrity: sha512-OMcX/4IC/uqEPVgGeyfN22LJk6AZrMkRZHxcHBMBvHScDGgwTm2GT2Wkgtocyd3JfZffjj2kYUDXXII0Fk9W0g==}
    dependencies:
      inherits: 2.0.4
      readable-stream: 2.3.8
    dev: false

  /fs-extra@11.2.0:
    resolution: {integrity: sha512-PmDi3uwK5nFuXh7XDTlVnS17xJS7vW36is2+w3xcv8SVxiB4NyATf4ctkVY5bkSjX0Y4nbvZCq1/EjtEyr9ktw==}
    engines: {node: '>=14.14'}
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.1
    dev: false

  /fs-extra@8.1.0:
    resolution: {integrity: sha512-yhlQgA6mnOJUKOsRUFsgJdQCvkKhcz8tlZG5HBQfReYZy46OwLcY+Zia0mtdHsOo9y/hP+CxMN0TU9QxoOtG4g==}
    engines: {node: '>=6 <7 || >=8'}
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 4.0.0
      universalify: 0.1.2
    dev: false

  /fs-memo@1.2.0:
    resolution: {integrity: sha512-YEexkCpL4j03jn5SxaMHqcO6IuWuqm8JFUYhyCep7Ao89JIYmB8xoKhK7zXXJ9cCaNXpyNH5L3QtAmoxjoHW2w==}
    dev: false

  /fs-minipass@2.1.0:
    resolution: {integrity: sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.6
    dev: false

  /fs-monkey@1.0.6:
    resolution: {integrity: sha512-b1FMfwetIKymC0eioW7mTywihSQE4oLzQn1dB6rZB5fx/3NpNEdAWeCSMB+60/AeT0TCXsxzAlcYVEFCTAksWg==}
    dev: false

  /fs-write-stream-atomic@1.0.10:
    resolution: {integrity: sha512-gehEzmPn2nAwr39eay+x3X34Ra+M2QlVUTLhkXPjWdeO8RF9kszk116avgBJM3ZyNHgHXBNx+VmPaFC36k0PzA==}
    deprecated: This package is no longer supported.
    dependencies:
      graceful-fs: 4.2.11
      iferr: 0.1.5
      imurmurhash: 0.1.4
      readable-stream: 2.3.8
    dev: false

  /fs.realpath@1.0.0:
    resolution: {integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==}
    dev: false

  /fsevents@1.2.13:
    resolution: {integrity: sha512-oWb1Z6mkHIskLzEJ/XWX0srkpkTQ7vaopMQkyaEIoq0fmtFVxOthb8cCxeT+p3ynTdkk/RZwbgG4brR5BeWECw==}
    engines: {node: '>= 4.0'}
    os: [darwin]
    deprecated: Upgrade to fsevents v2 to mitigate potential security issues
    requiresBuild: true
    dependencies:
      bindings: 1.5.0
      nan: 2.20.0
    dev: false
    optional: true

  /fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    dev: false
    optional: true

  /function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}
    dev: false

  /function.prototype.name@1.1.6:
    resolution: {integrity: sha512-Z5kx79swU5P27WEayXM1tBi5Ze/lbIyiNgU3qyXUOf9b2rgXYyF9Dy9Cx+IQv/Lc8WCG6L82zwUPpSS9hGehIg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      functions-have-names: 1.2.3
    dev: false

  /functions-have-names@1.2.3:
    resolution: {integrity: sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==}
    dev: false

  /gensync@1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}
    dev: false

  /get-intrinsic@1.2.4:
    resolution: {integrity: sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2
      has-proto: 1.0.3
      has-symbols: 1.0.3
      hasown: 2.0.2
    dev: false

  /get-port-please@2.6.1:
    resolution: {integrity: sha512-4PDSrL6+cuMM1xs6w36ZIkaKzzE0xzfVBCfebHIJ3FE8iB9oic/ECwPw3iNiD4h1AoJ5XLLBhEviFAVrZsDC5A==}
    dependencies:
      fs-memo: 1.2.0
    dev: false

  /get-stream@6.0.1:
    resolution: {integrity: sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==}
    engines: {node: '>=10'}
    dev: false

  /get-symbol-description@1.0.2:
    resolution: {integrity: sha512-g0QYk1dZBxGwk+Ngc+ltRH2IBp2f7zBkBMBJZCDerh6EhlhSR6+9irMCuT/09zD6qkarHUSn529sK/yL4S27mg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      get-intrinsic: 1.2.4
    dev: false

  /get-value@2.0.6:
    resolution: {integrity: sha512-Ln0UQDlxH1BapMu3GPtf7CuYNwRZf2gwCuPqbyG6pB8WfmFpzqcy4xtAaAMUhnNqjMKTiCPZG2oMT3YSx8U2NA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /get-value@3.0.1:
    resolution: {integrity: sha512-mKZj9JLQrwMBtj5wxi6MH8Z5eSKaERpAwjg43dPtlGI1ZVEgH/qC7T8/6R2OBSUA+zzHBZgICsVJaEIV2tKTDA==}
    engines: {node: '>=6.0'}
    dependencies:
      isobject: 3.0.1
    dev: false

  /git-config-path@2.0.0:
    resolution: {integrity: sha512-qc8h1KIQbJpp+241id3GuAtkdyJ+IK+LIVtkiFTRKRrmddDzs3SI9CvP1QYmWBFvm1I/PWRwj//of8bgAc0ltA==}
    engines: {node: '>=4'}
    dev: false

  /git-up@7.0.0:
    resolution: {integrity: sha512-ONdIrbBCFusq1Oy0sC71F5azx8bVkvtZtMJAsv+a6lz5YAmbNnLD6HAB4gptHZVLPR8S2/kVN6Gab7lryq5+lQ==}
    dependencies:
      is-ssh: 1.4.0
      parse-url: 8.1.0
    dev: false

  /git-url-parse@13.1.1:
    resolution: {integrity: sha512-PCFJyeSSdtnbfhSNRw9Wk96dDCNx+sogTe4YNXeXSJxt7xz5hvXekuRn9JX7m+Mf4OscCu8h+mtAl3+h5Fo8lQ==}
    dependencies:
      git-up: 7.0.0
    dev: false

  /glob-parent@3.1.0:
    resolution: {integrity: sha512-E8Ak/2+dZY6fnzlR7+ueWvhsH1SjHr4jjss4YS/h4py44jY9MhK/VFdaZJAWDz6BbL21KeteKxFSFpq8OS5gVA==}
    requiresBuild: true
    dependencies:
      is-glob: 3.1.0
      path-dirname: 1.0.2
    dev: false
    optional: true

  /glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}
    dependencies:
      is-glob: 4.0.3
    dev: false

  /glob-to-regexp@0.4.1:
    resolution: {integrity: sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==}
    dev: false

  /glob@7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==}
    deprecated: Glob versions prior to v9 are no longer supported
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1
    dev: false

  /glob@8.1.0:
    resolution: {integrity: sha512-r8hpEjiQEYlF2QU0df3dS+nxxSIreXQS1qRhMJM0Q5NDdR386C7jb7Hwwod8Fgiuex+k0GFjgft18yvxm5XoCQ==}
    engines: {node: '>=12'}
    deprecated: Glob versions prior to v9 are no longer supported
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 5.1.6
      once: 1.4.0
    dev: false

  /globals@11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==}
    engines: {node: '>=4'}
    dev: false

  /globalthis@1.0.4:
    resolution: {integrity: sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-properties: 1.2.1
      gopd: 1.0.1
    dev: false

  /globby@11.1.0:
    resolution: {integrity: sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==}
    engines: {node: '>=10'}
    dependencies:
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.3.2
      ignore: 5.3.2
      merge2: 1.4.1
      slash: 3.0.0
    dev: false

  /good-listener@1.2.2:
    resolution: {integrity: sha512-goW1b+d9q/HIwbVYZzZ6SsTr4IgE+WA44A0GmPIQstuOrgsFcT7VEJ48nmr9GaRtNu0XTKacFLGnBPAM6Afouw==}
    dependencies:
      delegate: 3.2.0
    dev: false

  /gopd@1.0.1:
    resolution: {integrity: sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==}
    dependencies:
      get-intrinsic: 1.2.4
    dev: false

  /graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}
    dev: false

  /gzip-size@6.0.0:
    resolution: {integrity: sha512-ax7ZYomf6jqPTQ4+XCpUGyXKHk5WweS+e05MBO4/y3WJ5RkmPXNKvX+bx1behVILVwr6JSQvZAku021CHPXG3Q==}
    engines: {node: '>=10'}
    dependencies:
      duplexer: 0.1.2
    dev: false

  /hard-source-webpack-plugin@0.13.1(webpack@4.47.0):
    resolution: {integrity: sha512-r9zf5Wq7IqJHdVAQsZ4OP+dcUSvoHqDMxJlIzaE2J0TZWn3UjMMrHqwDHR8Jr/pzPfG7XxSe36E7Y8QGNdtuAw==}
    engines: {node: '>=8.0.0'}
    peerDependencies:
      webpack: '*'
    dependencies:
      chalk: 2.4.2
      find-cache-dir: 2.1.0
      graceful-fs: 4.2.11
      lodash: 4.17.21
      mkdirp: 0.5.6
      node-object-hash: 1.4.2
      parse-json: 4.0.0
      pkg-dir: 3.0.0
      rimraf: 2.7.1
      semver: 5.7.2
      tapable: 1.1.3
      webpack: 4.47.0
      webpack-sources: 1.4.3
      write-json-file: 2.3.0
    dev: false

  /has-bigints@1.0.2:
    resolution: {integrity: sha512-tSvCKtBr9lkF0Ex0aQiP9N+OpV4zi2r/Nee5VkRDbaqv35RLYMzbwQfFSZZH0kR+Rd6302UJZ2p/bJCEoR3VoQ==}
    dev: false

  /has-flag@3.0.0:
    resolution: {integrity: sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==}
    engines: {node: '>=4'}
    dev: false

  /has-flag@4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}
    dev: false

  /has-property-descriptors@1.0.2:
    resolution: {integrity: sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==}
    dependencies:
      es-define-property: 1.0.0
    dev: false

  /has-proto@1.0.3:
    resolution: {integrity: sha512-SJ1amZAJUiZS+PhsVLf5tGydlaVB8EdFpaSO4gmiUKUOxk8qzn5AIy4ZeJUmh22znIdk/uMAUT2pl3FxzVUH+Q==}
    engines: {node: '>= 0.4'}
    dev: false

  /has-symbols@1.0.3:
    resolution: {integrity: sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==}
    engines: {node: '>= 0.4'}
    dev: false

  /has-tostringtag@1.0.2:
    resolution: {integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-symbols: 1.0.3
    dev: false

  /has-value@0.3.1:
    resolution: {integrity: sha512-gpG936j8/MzaeID5Yif+577c17TxaDmhuyVgSwtnL/q8UUTySg8Mecb+8Cf1otgLoD7DDH75axp86ER7LFsf3Q==}
    engines: {node: '>=0.10.0'}
    dependencies:
      get-value: 2.0.6
      has-values: 0.1.4
      isobject: 2.1.0
    dev: false

  /has-value@1.0.0:
    resolution: {integrity: sha512-IBXk4GTsLYdQ7Rvt+GRBrFSVEkmuOUy4re0Xjd9kJSUQpnTrWR4/y9RpfexN9vkAPMFuQoeWKwqzPozRTlasGw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      get-value: 2.0.6
      has-values: 1.0.0
      isobject: 3.0.1
    dev: false

  /has-values@0.1.4:
    resolution: {integrity: sha512-J8S0cEdWuQbqD9//tlZxiMuMNmxB8PlEwvYwuxsTmR1G5RXUePEX/SJn7aD0GMLieuZYSwNH0cQuJGwnYunXRQ==}
    engines: {node: '>=0.10.0'}
    dev: false

  /has-values@1.0.0:
    resolution: {integrity: sha512-ODYZC64uqzmtfGMEAX/FvZiRyWLpAC3vYnNunURUnkGVTS+mI0smVsWaPydRBsE3g+ok7h960jChO8mFcWlHaQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-number: 3.0.0
      kind-of: 4.0.0
    dev: false

  /hash-base@3.0.4:
    resolution: {integrity: sha512-EeeoJKjTyt868liAlVmcv2ZsUfGHlE3Q+BICOXcZiwN3osr5Q/zFGYmTJpoIzuaSTAwndFy+GqhEwlU4L3j4Ow==}
    engines: {node: '>=4'}
    dependencies:
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: false

  /hash-base@3.1.0:
    resolution: {integrity: sha512-1nmYp/rhMDiE7AYkDw+lLwlAzz0AntGIe51F3RfFfEqyQ3feY2eI/NcwC6umIQVOASPMsWJLJScWKSSvzL9IVA==}
    engines: {node: '>=4'}
    dependencies:
      inherits: 2.0.4
      readable-stream: 3.6.2
      safe-buffer: 5.2.1
    dev: false

  /hash-sum@1.0.2:
    resolution: {integrity: sha512-fUs4B4L+mlt8/XAtSOGMUO1TXmAelItBPtJG7CyHJfYTdDjwisntGO2JQz7oUsatOY9o68+57eziUVNw/mRHmA==}
    dev: false

  /hash-sum@2.0.0:
    resolution: {integrity: sha512-WdZTbAByD+pHfl/g9QSsBIIwy8IT+EsPiKDs0KNX+zSHhdDLFKdZu0BQHljvO+0QI/BasbMSUa8wYNCZTvhslg==}
    dev: false

  /hash.js@1.1.7:
    resolution: {integrity: sha512-taOaskGt4z4SOANNseOviYDvjEJinIkRgmp7LbKP2YTTmVxWBl87s/uzK9r+44BclBSp2X7K1hqeNfz9JbBeXA==}
    dependencies:
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
    dev: false

  /hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      function-bind: 1.1.2
    dev: false

  /he@1.2.0:
    resolution: {integrity: sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==}
    hasBin: true
    dev: false

  /highcharts-vue@1.4.3(highcharts@8.2.2)(vue@2.7.16):
    resolution: {integrity: sha512-qIy9EFuLIgOw+reNkTvu0pvMBcSE0BAPtONAqZoEl0qaAIpGxiyXqhRNPDufWieSC0YlybpCznxE8EK19Kcg+A==}
    peerDependencies:
      highcharts: '>=5.0.0'
      vue: '>=1.0.0'
    dependencies:
      highcharts: 8.2.2
      vue: 2.7.16
    dev: false

  /highcharts@8.2.2:
    resolution: {integrity: sha512-F63TXO7RxsvTcpO/KOubQZWualYpCMyCTuKtoWbt7KCsfQ3Kl7Fr6HEyyJdjkYl+XlnmnKlSRi9d3HjLK9Q0wg==}
    dev: false

  /hmac-drbg@1.0.1:
    resolution: {integrity: sha512-Tti3gMqLdZfhOQY1Mzf/AanLiqh1WTiJgEj26ZuYQ9fbkLomzGchCws4FyrSd4VkpBfiNhaE1On+lOz894jvXg==}
    dependencies:
      hash.js: 1.1.7
      minimalistic-assert: 1.0.1
      minimalistic-crypto-utils: 1.0.1
    dev: false

  /hookable@4.4.1:
    resolution: {integrity: sha512-KWjZM8C7IVT2qne5HTXjM6R6VnRfjfRlf/oCnHd+yFxoHO1DzOl6B9LzV/VqGQK/IrFewq+EG+ePVrE9Tpc3fg==}
    dev: false

  /html-entities@2.5.2:
    resolution: {integrity: sha512-K//PSRMQk4FZ78Kyau+mZurHn3FH0Vwr+H36eE0rPbeYkRRi9YxceYPhuN60UwWorxyKHhqoAJl2OFKa4BVtaA==}
    dev: false

  /html-escaper@2.0.2:
    resolution: {integrity: sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==}
    dev: false

  /html-minifier-terser@5.1.1:
    resolution: {integrity: sha512-ZPr5MNObqnV/T9akshPKbVgyOqLmy+Bxo7juKCfTfnjNniTAMdy4hz21YQqoofMBJD2kdREaqPPdThoR78Tgxg==}
    engines: {node: '>=6'}
    hasBin: true
    dependencies:
      camel-case: 4.1.2
      clean-css: 4.2.4
      commander: 4.1.1
      he: 1.2.0
      param-case: 3.0.4
      relateurl: 0.2.7
      terser: 4.8.1
    dev: false

  /html-minifier-terser@7.2.0:
    resolution: {integrity: sha512-tXgn3QfqPIpGl9o+K5tpcj3/MN4SfLtsx2GWwBC3SSd0tXQGyF3gsSqad8loJgKZGM3ZxbYDd5yhiBIdWpmvLA==}
    engines: {node: ^14.13.1 || >=16.0.0}
    hasBin: true
    dependencies:
      camel-case: 4.1.2
      clean-css: 5.3.3
      commander: 10.0.1
      entities: 4.5.0
      param-case: 3.0.4
      relateurl: 0.2.7
      terser: 5.33.0
    dev: false

  /html-tags@2.0.0:
    resolution: {integrity: sha512-+Il6N8cCo2wB/Vd3gqy/8TZhTD3QvcVeQLCnZiGkGCH3JP28IgGAY41giccp2W4R3jfyJPAP318FQTa1yU7K7g==}
    engines: {node: '>=4'}
    dev: false

  /html-webpack-plugin@4.5.2(webpack@4.47.0):
    resolution: {integrity: sha512-q5oYdzjKUIPQVjOosjgvCHQOv9Ett9CYYHlgvJeXG0qQvdSojnBq4vAdQBwn1+yGveAwHCoe/rMR86ozX3+c2A==}
    engines: {node: '>=6.9'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      '@types/html-minifier-terser': 5.1.2
      '@types/tapable': 1.0.12
      '@types/webpack': 4.41.39
      html-minifier-terser: 5.1.1
      loader-utils: 1.4.2
      lodash: 4.17.21
      pretty-error: 2.1.2
      tapable: 1.1.3
      util.promisify: 1.0.0
      webpack: 4.47.0
    dev: false

  /htmlparser2@10.0.0:
    resolution: {integrity: sha512-TwAZM+zE5Tq3lrEHvOlvwgj1XLWQCtaaibSN11Q+gGBAS7Y1uZSWwXXRe4iF6OXnaq1riyQAPFOBtYc77Mxq0g==}
    dependencies:
      domelementtype: 2.3.0
      domhandler: 5.0.3
      domutils: 3.2.2
      entities: 6.0.1
    dev: false

  /htmlparser2@6.1.0:
    resolution: {integrity: sha512-gyyPk6rgonLFEDGoeRgQNaEUvdJ4ktTmmUh/h2t7s+M8oPpIPxgNACWa+6ESR57kXstwqPiCut0V8NRpcwgU7A==}
    dependencies:
      domelementtype: 2.3.0
      domhandler: 4.3.1
      domutils: 2.8.0
      entities: 2.2.0
    dev: false

  /http-errors@2.0.0:
    resolution: {integrity: sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==}
    engines: {node: '>= 0.8'}
    dependencies:
      depd: 2.0.0
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 2.0.1
      toidentifier: 1.0.1
    dev: false

  /http-proxy-middleware@1.3.1:
    resolution: {integrity: sha512-13eVVDYS4z79w7f1+NPllJtOQFx/FdUW4btIvVRMaRlUY9VGstAbo5MOhLEuUgZFRHn3x50ufn25zkj/boZnEg==}
    engines: {node: '>=8.0.0'}
    dependencies:
      '@types/http-proxy': 1.17.15
      http-proxy: 1.18.1
      is-glob: 4.0.3
      is-plain-obj: 3.0.0
      micromatch: 4.0.8
    transitivePeerDependencies:
      - debug
    dev: false

  /http-proxy@1.18.1:
    resolution: {integrity: sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ==}
    engines: {node: '>=8.0.0'}
    dependencies:
      eventemitter3: 4.0.7
      follow-redirects: 1.15.9
      requires-port: 1.0.0
    transitivePeerDependencies:
      - debug
    dev: false

  /https-browserify@1.0.0:
    resolution: {integrity: sha512-J+FkSdyD+0mA0N+81tMotaRMfSL9SGi+xpD3T6YApKsc3bGSXJlfXri3VyFOeYkfLRQisDk1W+jIFFKBeUBbBg==}
    dev: false

  /human-signals@2.1.0:
    resolution: {integrity: sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==}
    engines: {node: '>=10.17.0'}
    dev: false

  /hyperdyperid@1.2.0:
    resolution: {integrity: sha512-Y93lCzHYgGWdrJ66yIktxiaGULYc6oGiABxhcO5AufBeOyoIdZF7bIfLaOrbM0iGIOXQQgxxRrFEnb+Y6w1n4A==}
    engines: {node: '>=10.18'}
    dev: false

  /iconv-lite@0.4.24:
    resolution: {integrity: sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      safer-buffer: 2.1.2
    dev: false

  /iconv-lite@0.6.3:
    resolution: {integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      safer-buffer: 2.1.2
    dev: false

  /icss-utils@5.1.0(postcss@8.4.47):
    resolution: {integrity: sha512-soFhflCVWLfRNOPU3iv5Z9VUdT44xFRbzjLsEzSr5AQmgqPMTHdU3PMT1Cf1ssx8fLNJDA1juftYl+PUcv3MqA==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      postcss: 8.4.47
    dev: false

  /ieee754@1.2.1:
    resolution: {integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==}
    dev: false

  /iferr@0.1.5:
    resolution: {integrity: sha512-DUNFN5j7Tln0D+TxzloUjKB+CtVu6myn0JEFak6dG18mNt9YkQ6lzGCdafwofISZ1lLF3xRHJ98VKy9ynkcFaA==}
    dev: false

  /ignore@5.3.2:
    resolution: {integrity: sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==}
    engines: {node: '>= 4'}
    dev: false

  /image-size@0.5.5:
    resolution: {integrity: sha512-6TDAlDPZxUFCv+fuOkIoXT/V/f3Qbq8e37p+YOiYrUv3v9cc3/6x78VdfPgFVaB9dZYeLUfKgHRebpkm/oP2VQ==}
    engines: {node: '>=0.10.0'}
    hasBin: true
    requiresBuild: true
    dev: false
    optional: true

  /import-fresh@3.3.0:
    resolution: {integrity: sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==}
    engines: {node: '>=6'}
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0
    dev: false

  /imurmurhash@0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==}
    engines: {node: '>=0.8.19'}
    dev: false

  /indent-string@4.0.0:
    resolution: {integrity: sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==}
    engines: {node: '>=8'}
    dev: false

  /infer-owner@1.0.4:
    resolution: {integrity: sha512-IClj+Xz94+d7irH5qRyfJonOdfTzuDaifE6ZPWfx0N0+/ATZCbuTPq2prFl526urkQd90WyUKIh1DfBQ2hMz9A==}
    dev: false

  /inflight@1.0.6:
    resolution: {integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==}
    deprecated: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2
    dev: false

  /inherits@2.0.3:
    resolution: {integrity: sha512-x00IRNXNy63jwGkJmzPigoySHbaqpNuzKbBOmzK+g2OdZpQ9w+sxCN+VSB3ja7IAge2OP2qpfxTjeNcyjmW1uw==}
    dev: false

  /inherits@2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}
    dev: false

  /ini@1.3.8:
    resolution: {integrity: sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==}
    dev: false

  /inquirer@7.3.3:
    resolution: {integrity: sha512-JG3eIAj5V9CwcGvuOmoo6LB9kbAYT8HXffUl6memuszlwDC/qvFAJw49XJ5NROSFNPxp3iQg1GqkFhaY/CR0IA==}
    engines: {node: '>=8.0.0'}
    dependencies:
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      cli-cursor: 3.1.0
      cli-width: 3.0.0
      external-editor: 3.1.0
      figures: 3.2.0
      lodash: 4.17.21
      mute-stream: 0.0.8
      run-async: 2.4.1
      rxjs: 6.6.7
      string-width: 4.2.3
      strip-ansi: 6.0.1
      through: 2.3.8
    dev: false

  /internal-slot@1.0.7:
    resolution: {integrity: sha512-NGnrKwXzSms2qUUih/ILZ5JBqNTSa1+ZmP6flaIp6KmSElgE9qdndzS3cqjrDovwFdmwsGsLdeFgB6suw+1e9g==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      hasown: 2.0.2
      side-channel: 1.0.6
    dev: false

  /ip@2.0.1:
    resolution: {integrity: sha512-lJUL9imLTNi1ZfXT+DU6rBBdbiKGBuay9B6xGSPVjUeQwaH1RIGqef8RZkUtHioLmSNpPR5M4HVKJGm1j8FWVQ==}
    dev: false

  /is-accessor-descriptor@1.0.1:
    resolution: {integrity: sha512-YBUanLI8Yoihw923YeFUS5fs0fF2f5TSFTNiYAAzhhDscDa3lEqYuz1pDOEP5KvX94I9ey3vsqjJcLVFVU+3QA==}
    engines: {node: '>= 0.10'}
    dependencies:
      hasown: 2.0.2
    dev: false

  /is-array-buffer@3.0.4:
    resolution: {integrity: sha512-wcjaerHw0ydZwfhiKbXJWLDY8A7yV7KhjQOpb83hGgGfId/aQa4TOvwyzn2PuswW2gPCYEL/nEAiSVpdOj1lXw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      get-intrinsic: 1.2.4
    dev: false

  /is-arrayish@0.2.1:
    resolution: {integrity: sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==}
    dev: false

  /is-arrayish@0.3.2:
    resolution: {integrity: sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==}
    dev: false

  /is-bigint@1.0.4:
    resolution: {integrity: sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg==}
    dependencies:
      has-bigints: 1.0.2
    dev: false

  /is-binary-path@1.0.1:
    resolution: {integrity: sha512-9fRVlXc0uCxEDj1nQzaWONSpbTfx0FmJfzHF7pwlI8DkWGoHBBea4Pg5Ky0ojwwxQmnSifgbKkI06Qv0Ljgj+Q==}
    engines: {node: '>=0.10.0'}
    requiresBuild: true
    dependencies:
      binary-extensions: 1.13.1
    dev: false
    optional: true

  /is-binary-path@2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}
    dependencies:
      binary-extensions: 2.3.0
    dev: false

  /is-boolean-object@1.1.2:
    resolution: {integrity: sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      has-tostringtag: 1.0.2
    dev: false

  /is-buffer@1.1.6:
    resolution: {integrity: sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==}
    dev: false

  /is-callable@1.2.7:
    resolution: {integrity: sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==}
    engines: {node: '>= 0.4'}
    dev: false

  /is-core-module@2.15.1:
    resolution: {integrity: sha512-z0vtXSwucUJtANQWldhbtbt7BnL0vxiFjIdDLAatwhDYty2bad6s+rijD6Ri4YuYJubLzIJLUidCh09e1djEVQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      hasown: 2.0.2
    dev: false

  /is-data-descriptor@1.0.1:
    resolution: {integrity: sha512-bc4NlCDiCr28U4aEsQ3Qs2491gVq4V8G7MQyws968ImqjKuYtTJXrl7Vq7jsN7Ly/C3xj5KWFrY7sHNeDkAzXw==}
    engines: {node: '>= 0.4'}
    dependencies:
      hasown: 2.0.2
    dev: false

  /is-data-view@1.0.1:
    resolution: {integrity: sha512-AHkaJrsUVW6wq6JS8y3JnM/GJF/9cf+k20+iDzlSaJrinEo5+7vRiteOSwBhHRiAyQATN1AmY4hwzxJKPmYf+w==}
    engines: {node: '>= 0.4'}
    dependencies:
      is-typed-array: 1.1.13
    dev: false

  /is-date-object@1.0.5:
    resolution: {integrity: sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-tostringtag: 1.0.2
    dev: false

  /is-descriptor@0.1.7:
    resolution: {integrity: sha512-C3grZTvObeN1xud4cRWl366OMXZTj0+HGyk4hvfpx4ZHt1Pb60ANSXqCK7pdOTeUQpRzECBSTphqvD7U+l22Eg==}
    engines: {node: '>= 0.4'}
    dependencies:
      is-accessor-descriptor: 1.0.1
      is-data-descriptor: 1.0.1
    dev: false

  /is-descriptor@1.0.3:
    resolution: {integrity: sha512-JCNNGbwWZEVaSPtS45mdtrneRWJFp07LLmykxeFV5F6oBvNF8vHSfJuJgoT472pSfk+Mf8VnlrspaFBHWM8JAw==}
    engines: {node: '>= 0.4'}
    dependencies:
      is-accessor-descriptor: 1.0.1
      is-data-descriptor: 1.0.1
    dev: false

  /is-extendable@0.1.1:
    resolution: {integrity: sha512-5BMULNob1vgFX6EjQw5izWDxrecWK9AM72rugNr0TFldMOi0fj6Jk+zeKIt0xGj4cEfQIJth4w3OKWOJ4f+AFw==}
    engines: {node: '>=0.10.0'}
    dev: false

  /is-extendable@1.0.1:
    resolution: {integrity: sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-plain-object: 2.0.4
    dev: false

  /is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}
    dev: false

  /is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}
    dev: false

  /is-glob@3.1.0:
    resolution: {integrity: sha512-UFpDDrPgM6qpnFNI+rh/p3bUaq9hKLZN8bMUWzxmcnZVS3omf4IPK+BrewlnWjO1WmUsMYuSjKh4UJuV4+Lqmw==}
    engines: {node: '>=0.10.0'}
    requiresBuild: true
    dependencies:
      is-extglob: 2.1.1
    dev: false
    optional: true

  /is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: 2.1.1
    dev: false

  /is-negative-zero@2.0.3:
    resolution: {integrity: sha512-5KoIu2Ngpyek75jXodFvnafB6DJgr3u8uuK0LEZJjrU19DrMD3EVERaR8sjz8CCGgpZvxPl9SuE1GMVPFHx1mw==}
    engines: {node: '>= 0.4'}
    dev: false

  /is-number-object@1.0.7:
    resolution: {integrity: sha512-k1U0IRzLMo7ZlYIfzRu23Oh6MiIFasgpb9X76eqfFZAqwH44UI4KTBvBYIZ1dSL9ZzChTB9ShHfLkR4pdW5krQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-tostringtag: 1.0.2
    dev: false

  /is-number@3.0.0:
    resolution: {integrity: sha512-4cboCqIpliH+mAvFNegjZQ4kgKc3ZUhQVr3HvWbSh5q3WH2v82ct+T2Y1hdU5Gdtorx/cLifQjqCbL7bpznLTg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2
    dev: false

  /is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}
    dev: false

  /is-plain-obj@1.1.0:
    resolution: {integrity: sha512-yvkRyxmFKEOQ4pNXCmJG5AEQNlXJS5LaONXo5/cLdTZdWvsZ1ioJEonLGAosKlMWE8lwUy/bJzMjcw8az73+Fg==}
    engines: {node: '>=0.10.0'}
    dev: false

  /is-plain-obj@3.0.0:
    resolution: {integrity: sha512-gwsOE28k+23GP1B6vFl1oVh/WOzmawBrKwo5Ev6wMKzPkaXaCDIQKzLnvsA42DRlbVTWorkgTKIviAKCWkfUwA==}
    engines: {node: '>=10'}
    dev: false

  /is-plain-object@2.0.4:
    resolution: {integrity: sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==}
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: 3.0.1
    dev: false

  /is-regex@1.1.4:
    resolution: {integrity: sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      has-tostringtag: 1.0.2
    dev: false

  /is-shared-array-buffer@1.0.3:
    resolution: {integrity: sha512-nA2hv5XIhLR3uVzDDfCIknerhx8XUKnstuOERPNNIinXG7v9u+ohXF67vxm4TPTEPU6lm61ZkwP3c9PCB97rhg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
    dev: false

  /is-ssh@1.4.0:
    resolution: {integrity: sha512-x7+VxdxOdlV3CYpjvRLBv5Lo9OJerlYanjwFrPR9fuGPjCiNiCzFgAWpiLAohSbsnH4ZAys3SBh+hq5rJosxUQ==}
    dependencies:
      protocols: 2.0.1
    dev: false

  /is-stream@2.0.1:
    resolution: {integrity: sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==}
    engines: {node: '>=8'}
    dev: false

  /is-string@1.0.7:
    resolution: {integrity: sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-tostringtag: 1.0.2
    dev: false

  /is-symbol@1.0.4:
    resolution: {integrity: sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-symbols: 1.0.3
    dev: false

  /is-typed-array@1.1.13:
    resolution: {integrity: sha512-uZ25/bUAlUY5fR4OKT4rZQEBrzQWYV9ZJYGGsUmEJ6thodVJ1HX64ePQ6Z0qPWP+m+Uq6e9UugrE38jeYsDSMw==}
    engines: {node: '>= 0.4'}
    dependencies:
      which-typed-array: 1.1.15
    dev: false

  /is-weakref@1.0.2:
    resolution: {integrity: sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ==}
    dependencies:
      call-bind: 1.0.7
    dev: false

  /is-what@3.14.1:
    resolution: {integrity: sha512-sNxgpk9793nzSs7bA6JQJGeIuRBQhAaNGG77kzYQgMkrID+lS6SlK07K5LaptscDlSaIgH+GPFzf+d75FVxozA==}
    dev: false

  /is-what@4.1.16:
    resolution: {integrity: sha512-ZhMwEosbFJkA0YhFnNDgTM4ZxDRsS6HqTo7qsZM08fehyRYIYa0yHu5R6mgo1n/8MgaPBXiPimPD77baVFYg+A==}
    engines: {node: '>=12.13'}
    dev: false

  /is-windows@1.0.2:
    resolution: {integrity: sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /is-wsl@1.1.0:
    resolution: {integrity: sha512-gfygJYZ2gLTDlmbWMI0CE2MwnFzSN/2SZfkMlItC4K/JBlsWVDB0bO6XhqcY13YXE7iMcAJnzTCJjPiTeJJ0Mw==}
    engines: {node: '>=4'}
    dev: false

  /isarray@1.0.0:
    resolution: {integrity: sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==}
    dev: false

  /isarray@2.0.5:
    resolution: {integrity: sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==}
    dev: false

  /isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}
    dev: false

  /isobject@2.1.0:
    resolution: {integrity: sha512-+OUdGJlgjOBZDfxnDjYYG6zp487z0JGNQq3cYQYg5f5hKR+syHMsaztzGeml/4kGG55CSpKSpWTY+jYGgsHLgA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      isarray: 1.0.0
    dev: false

  /isobject@3.0.1:
    resolution: {integrity: sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg==}
    engines: {node: '>=0.10.0'}
    dev: false

  /isobject@4.0.0:
    resolution: {integrity: sha512-S/2fF5wH8SJA/kmwr6HYhK/RI/OkhD84k8ntalo0iJjZikgq1XFvR5M8NPT1x5F7fBwCG3qHfnzeP/Vh/ZxCUA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /jest-worker@26.6.2:
    resolution: {integrity: sha512-KWYVV1c4i+jbMpaBC+U++4Va0cp8OisU185o73T1vo99hqi7w8tSJfUXYswwqqrjzwxa6KpRK54WhPvwf5w6PQ==}
    engines: {node: '>= 10.13.0'}
    dependencies:
      '@types/node': 22.5.5
      merge-stream: 2.0.0
      supports-color: 7.2.0
    dev: false

  /jiti@1.21.6:
    resolution: {integrity: sha512-2yTgeWTWzMWkHu6Jp9NKgePDaYHbntiwvYuuJLbbN9vl7DC9DvXKOB2BC3ZZ92D3cvV/aflH0osDfwpHepQ53w==}
    hasBin: true
    dev: false

  /jquery@3.7.1:
    resolution: {integrity: sha512-m4avr8yL8kmFN8psrbFFFmB/If14iN5o9nw/NgnnM+kybDJpRsAynV2BsfpTYrTRysYUdADVD7CkUUizgkpLfg==}
    dev: false

  /js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}
    dev: false

  /jsesc@0.5.0:
    resolution: {integrity: sha512-uZz5UnB7u4T9LvwmFqXii7pZSouaRPorGs5who1Ip7VO0wxanFvBL7GkM6dTHlgX+jhBApRetaWpnDabOeTcnA==}
    hasBin: true
    dev: false

  /jsesc@2.5.2:
    resolution: {integrity: sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==}
    engines: {node: '>=4'}
    hasBin: true
    dev: false

  /json-parse-better-errors@1.0.2:
    resolution: {integrity: sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw==}
    dev: false

  /json-parse-even-better-errors@2.3.1:
    resolution: {integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==}
    dev: false

  /json-schema-traverse@0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}
    dev: false

  /json-schema-traverse@1.0.0:
    resolution: {integrity: sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==}
    dev: false

  /json5@1.0.2:
    resolution: {integrity: sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==}
    hasBin: true
    dependencies:
      minimist: 1.2.8
    dev: false

  /json5@2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==}
    engines: {node: '>=6'}
    hasBin: true
    dev: false

  /jsonfile@4.0.0:
    resolution: {integrity: sha512-m6F1R3z8jjlf2imQHS2Qez5sjKWQzbuuhuJ/FKYFRZvPE3PuHcSMVZzfsLhGVOkfd20obL5SWEBew5ShlquNxg==}
    optionalDependencies:
      graceful-fs: 4.2.11
    dev: false

  /jsonfile@6.1.0:
    resolution: {integrity: sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==}
    dependencies:
      universalify: 2.0.1
    optionalDependencies:
      graceful-fs: 4.2.11
    dev: false

  /kind-of@3.2.2:
    resolution: {integrity: sha512-NOW9QQXMoZGg/oqnVNoNTTIFEIid1627WCffUBJEdMxYApq7mNE7CpzucIPc+ZQg25Phej7IJSmX3hO+oblOtQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-buffer: 1.1.6
    dev: false

  /kind-of@4.0.0:
    resolution: {integrity: sha512-24XsCxmEbRwEDbz/qz3stgin8TTzZ1ESR56OMCN0ujYg+vRutNSiOj9bHH9u85DKgXguraugV5sFuvbD4FW/hw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-buffer: 1.1.6
    dev: false

  /kind-of@6.0.3:
    resolution: {integrity: sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==}
    engines: {node: '>=0.10.0'}
    dev: false

  /klona@2.0.6:
    resolution: {integrity: sha512-dhG34DXATL5hSxJbIexCft8FChFXtmskoZYnoPWjXQuebWYCNkVeV3KkGegCK9CP1oswI/vQibS2GY7Em/sJJA==}
    engines: {node: '>= 8'}
    dev: false

  /last-call-webpack-plugin@3.0.0:
    resolution: {integrity: sha512-7KI2l2GIZa9p2spzPIVZBYyNKkN+e/SQPpnjlTiPhdbDW3F86tdKKELxKpzJ5sgU19wQWsACULZmpTPYHeWO5w==}
    dependencies:
      lodash: 4.17.21
      webpack-sources: 1.4.3
    dev: false

  /launch-editor-middleware@2.9.1:
    resolution: {integrity: sha512-4wF6AtPtaIENiZdH/a+3yW8Xni7uxzTEDd1z+gH00hUWBCSmQknFohznMd9BWhLk8MXObeB5ir69GbIr9qFW1w==}
    dependencies:
      launch-editor: 2.9.1
    dev: false

  /launch-editor@2.9.1:
    resolution: {integrity: sha512-Gcnl4Bd+hRO9P9icCP/RVVT2o8SFlPXofuCxvA2SaZuH45whSvf5p8x5oih5ftLiVhEI4sp5xDY+R+b3zJBh5w==}
    dependencies:
      picocolors: 1.1.0
      shell-quote: 1.8.1
    dev: false

  /less-loader@6.2.0(webpack@4.47.0):
    resolution: {integrity: sha512-Cl5h95/Pz/PWub/tCBgT1oNMFeH1WTD33piG80jn5jr12T4XbxZcjThwNXDQ7AG649WEynuIzO4b0+2Tn9Qolg==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      clone: 2.1.2
      less: 3.13.1
      loader-utils: 2.0.4
      schema-utils: 2.7.1
      webpack: 4.47.0
    dev: false

  /less@3.13.1:
    resolution: {integrity: sha512-SwA1aQXGUvp+P5XdZslUOhhLnClSLIjWvJhmd+Vgib5BFIr9lMNlQwmwUNOjXThF/A0x+MCYYPeWEfeWiLRnTw==}
    engines: {node: '>=6'}
    hasBin: true
    dependencies:
      copy-anything: 2.0.6
      tslib: 1.14.1
    optionalDependencies:
      errno: 0.1.8
      graceful-fs: 4.2.11
      image-size: 0.5.5
      make-dir: 2.1.0
      mime: 1.6.0
      native-request: 1.1.2
      source-map: 0.6.1
    dev: false

  /lilconfig@2.1.0:
    resolution: {integrity: sha512-utWOt/GHzuUxnLKxB6dk81RoOeoNeHgbrXiuGk4yyF5qlRz+iIVWu56E2fqGHFrXz0QNUhLB/8nKqvRH66JKGQ==}
    engines: {node: '>=10'}
    dev: false

  /lilconfig@3.1.2:
    resolution: {integrity: sha512-eop+wDAvpItUys0FWkHIKeC9ybYrTGbU41U5K7+bttZZeohvnY7M9dZ5kB21GNWiFT2q1OoPTvncPCgSOVO5ow==}
    engines: {node: '>=14'}
    dev: false

  /lines-and-columns@1.2.4:
    resolution: {integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==}
    dev: false

  /lit-element@3.3.3:
    resolution: {integrity: sha512-XbeRxmTHubXENkV4h8RIPyr8lXc+Ff28rkcQzw3G6up2xg5E8Zu1IgOWIwBLEQsu3cOVFqdYwiVi0hv0SlpqUA==}
    dependencies:
      '@lit-labs/ssr-dom-shim': 1.2.1
      '@lit/reactive-element': 1.6.3
      lit-html: 2.8.0
    dev: false

  /lit-html@2.8.0:
    resolution: {integrity: sha512-o9t+MQM3P4y7M7yNzqAyjp7z+mQGa4NS4CxiyLqFPyFWyc4O+nodLrkrxSaCTrla6M5YOLaT3RpbbqjszB5g3Q==}
    dependencies:
      '@types/trusted-types': 2.0.7
    dev: false

  /lit@2.8.0:
    resolution: {integrity: sha512-4Sc3OFX9QHOJaHbmTMk28SYgVxLN3ePDjg7hofEft2zWlehFL3LiAuapWc4U/kYwMYJSh2hTCPZ6/LIC7ii0MA==}
    dependencies:
      '@lit/reactive-element': 1.6.3
      lit-element: 3.3.3
      lit-html: 2.8.0
    dev: false

  /loader-runner@2.4.0:
    resolution: {integrity: sha512-Jsmr89RcXGIwivFY21FcRrisYZfvLMTWx5kOLc+JTxtpBOG6xML0vzbc6SEQG2FO9/4Fc3wW4LVcB5DmGflaRw==}
    engines: {node: '>=4.3.0 <5.0.0 || >=5.10'}
    dev: false

  /loader-runner@4.3.0:
    resolution: {integrity: sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg==}
    engines: {node: '>=6.11.5'}
    dev: false

  /loader-utils@1.4.2:
    resolution: {integrity: sha512-I5d00Pd/jwMD2QCduo657+YM/6L3KZu++pmX9VFncxaxvHcru9jx1lBaFft+r4Mt2jK0Yhp41XlRAihzPxHNCg==}
    engines: {node: '>=4.0.0'}
    dependencies:
      big.js: 5.2.2
      emojis-list: 3.0.0
      json5: 1.0.2
    dev: false

  /loader-utils@2.0.4:
    resolution: {integrity: sha512-xXqpXoINfFhgua9xiqD8fPFHgkoq1mmmpE92WlDbm9rNRd/EbRb+Gqf908T2DMfuHjjJlksiK2RbHVOdD/MqSw==}
    engines: {node: '>=8.9.0'}
    dependencies:
      big.js: 5.2.2
      emojis-list: 3.0.0
      json5: 2.2.3
    dev: false

  /locate-path@3.0.0:
    resolution: {integrity: sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A==}
    engines: {node: '>=6'}
    dependencies:
      p-locate: 3.0.0
      path-exists: 3.0.0
    dev: false

  /locate-path@5.0.0:
    resolution: {integrity: sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==}
    engines: {node: '>=8'}
    dependencies:
      p-locate: 4.1.0
    dev: false

  /lodash._reinterpolate@3.0.0:
    resolution: {integrity: sha512-xYHt68QRoYGjeeM/XOE1uJtvXQAgvszfBhjV4yvsQH0u2i9I6cI6c6/eG4Hh3UAOVn0y/xAXwmTzEay49Q//HA==}
    dev: false

  /lodash.debounce@4.0.8:
    resolution: {integrity: sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==}
    dev: false

  /lodash.kebabcase@4.1.1:
    resolution: {integrity: sha512-N8XRTIMMqqDgSy4VLKPnJ/+hpGZN+PHQiJnSenYqPaVV/NCqEogTnAdZLQiGKhxX+JCs8waWq2t1XHWKOmlY8g==}
    dev: false

  /lodash.memoize@4.1.2:
    resolution: {integrity: sha512-t7j+NzmgnQzTAYXcsHYLgimltOV1MXHtlOWf6GjL9Kj8GK5FInw5JotxvbOs+IvV1/Dzo04/fCGfLVs7aXb4Ag==}
    dev: false

  /lodash.template@4.5.0:
    resolution: {integrity: sha512-84vYFxIkmidUiFxidA/KjjH9pAycqW+h980j7Fuz5qxRtO9pgB7MDFTdys1N7A5mcucRiDyEq4fusljItR1T/A==}
    dependencies:
      lodash._reinterpolate: 3.0.0
      lodash.templatesettings: 4.2.0
    dev: false

  /lodash.templatesettings@4.2.0:
    resolution: {integrity: sha512-stgLz+i3Aa9mZgnjr/O+v9ruKZsPsndy7qPZOchbqk2cnTU1ZaldKK+v7m54WoKIyxiuMZTKT2H81F8BeAc3ZQ==}
    dependencies:
      lodash._reinterpolate: 3.0.0
    dev: false

  /lodash.uniq@4.5.0:
    resolution: {integrity: sha512-xfBaXQd9ryd9dlSDvnvI0lvxfLJlYAZzXomUYzLKtUeOQvOP5piqAWuGtrhWeqaXK9hhoM/iyJc5AV+XfsX3HQ==}
    dev: false

  /lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}
    dev: false

  /lottie-web@5.12.2:
    resolution: {integrity: sha512-uvhvYPC8kGPjXT3MyKMrL3JitEAmDMp30lVkuq/590Mw9ok6pWcFCwXJveo0t5uqYw1UREQHofD+jVpdjBv8wg==}
    dev: false

  /lower-case@2.0.2:
    resolution: {integrity: sha512-7fm3l3NAF9WfN6W3JOmf5drwpVqX78JtoGJ3A6W0a6ZnldM41w2fV5D490psKFTpMds8TJse/eHLFFsNHHjHgg==}
    dependencies:
      tslib: 2.7.0
    dev: false

  /lru-cache@4.1.5:
    resolution: {integrity: sha512-sWZlbEP2OsHNkXrMl5GYk/jKk70MBng6UU4YI/qGDYbgf6YbP4EvmqISbXCoJiRKs+1bSpFHVgQxvJ17F2li5g==}
    dependencies:
      pseudomap: 1.0.2
      yallist: 2.1.2
    dev: false

  /lru-cache@5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==}
    dependencies:
      yallist: 3.1.1
    dev: false

  /lru-cache@6.0.0:
    resolution: {integrity: sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==}
    engines: {node: '>=10'}
    dependencies:
      yallist: 4.0.0
    dev: false

  /make-dir@1.3.0:
    resolution: {integrity: sha512-2w31R7SJtieJJnQtGc7RVL2StM2vGYVfqUOvUDxH6bC6aJTxPxTF0GnIgCyu7tjockiUWAYQRbxa7vKn34s5sQ==}
    engines: {node: '>=4'}
    dependencies:
      pify: 3.0.0
    dev: false

  /make-dir@2.1.0:
    resolution: {integrity: sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==}
    engines: {node: '>=6'}
    dependencies:
      pify: 4.0.1
      semver: 5.7.2
    dev: false

  /make-dir@3.1.0:
    resolution: {integrity: sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==}
    engines: {node: '>=8'}
    dependencies:
      semver: 6.3.1
    dev: false

  /map-cache@0.2.2:
    resolution: {integrity: sha512-8y/eV9QQZCiyn1SprXSrCmqJN0yNRATe+PO8ztwqrvrbdRLA3eYJF0yaR0YayLWkMbsQSKWS9N2gPcGEc4UsZg==}
    engines: {node: '>=0.10.0'}
    dev: false

  /map-visit@1.0.0:
    resolution: {integrity: sha512-4y7uGv8bd2WdM9vpQsiQNo41Ln1NvhvDRuVt0k2JZQ+ezN2uaQes7lZeZ+QQUHOLQAtDaBJ+7wCbi+ab/KFs+w==}
    engines: {node: '>=0.10.0'}
    dependencies:
      object-visit: 1.0.1
    dev: false

  /markdown-table@2.0.0:
    resolution: {integrity: sha512-Ezda85ToJUBhM6WGaG6veasyym+Tbs3cMAw/ZhOPqXiYsr0jgocBV3j3nx+4lk47plLlIqjwuTm/ywVI+zjJ/A==}
    dependencies:
      repeat-string: 1.6.1
    dev: false

  /md5.js@1.3.5:
    resolution: {integrity: sha512-xitP+WxNPcTTOgnTJcrhM0xvdPepipPSf3I8EIpGKeFLjt3PlJLIDG3u8EX53ZIubkb+5U2+3rELYpEhHhzdkg==}
    dependencies:
      hash-base: 3.1.0
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: false

  /mdn-data@2.0.14:
    resolution: {integrity: sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow==}
    dev: false

  /mdn-data@2.0.28:
    resolution: {integrity: sha512-aylIc7Z9y4yzHYAJNuESG3hfhC+0Ibp/MAMiaOZgNv4pmEdFyfZhhhny4MNiAfWdBQ1RQ2mfDWmM1x8SvGyp8g==}
    dev: false

  /mdn-data@2.0.30:
    resolution: {integrity: sha512-GaqWWShW4kv/G9IEucWScBx9G1/vsFZZJUO+tD26M8J8z3Kw5RDQjaoZe03YAClgeS/SWPOcb4nkFBTEi5DUEA==}
    dev: false

  /memfs@3.5.3:
    resolution: {integrity: sha512-UERzLsxzllchadvbPs5aolHh65ISpKpM+ccLbOJ8/vvpBKmAWf+la7dXFy7Mr0ySHbdHrFv5kGFCUHHe6GFEmw==}
    engines: {node: '>= 4.0.0'}
    dependencies:
      fs-monkey: 1.0.6
    dev: false

  /memfs@4.12.0:
    resolution: {integrity: sha512-74wDsex5tQDSClVkeK1vtxqYCAgCoXxx+K4NSHzgU/muYVYByFqa+0RnrPO9NM6naWm1+G9JmZ0p6QHhXmeYfA==}
    engines: {node: '>= 4.0.0'}
    dependencies:
      '@jsonjoy.com/json-pack': 1.1.0(tslib@2.7.0)
      '@jsonjoy.com/util': 1.3.0(tslib@2.7.0)
      tree-dump: 1.0.2(tslib@2.7.0)
      tslib: 2.7.0
    dev: false

  /memory-fs@0.4.1:
    resolution: {integrity: sha512-cda4JKCxReDXFXRqOHPQscuIYg1PvxbE2S2GP45rnwfEK+vZaXC8C1OFvdHIbgw0DLzowXGVoxLaAmlgRy14GQ==}
    dependencies:
      errno: 0.1.8
      readable-stream: 2.3.8
    dev: false

  /memory-fs@0.5.0:
    resolution: {integrity: sha512-jA0rdU5KoQMC0e6ppoNRtpp6vjFq6+NY7r8hywnC7V+1Xj/MtHwGIbB1QaK/dunyjWteJzmkpd7ooeWg10T7GA==}
    engines: {node: '>=4.3.0 <5.0.0 || >=5.10'}
    dependencies:
      errno: 0.1.8
      readable-stream: 2.3.8
    dev: false

  /merge-source-map@1.1.0:
    resolution: {integrity: sha512-Qkcp7P2ygktpMPh2mCQZaf3jhN6D3Z/qVZHSdWvQ+2Ef5HgRAPBO57A77+ENm0CPx2+1Ce/MYKi3ymqdfuqibw==}
    dependencies:
      source-map: 0.6.1
    dev: false

  /merge-stream@2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==}
    dev: false

  /merge2@1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    engines: {node: '>= 8'}
    dev: false

  /micromatch@3.1.10:
    resolution: {integrity: sha512-MWikgl9n9M3w+bpsY3He8L+w9eF9338xRl8IAO5viDizwSzziFEyUzo2xrrloB64ADbTf8uA8vRqqttDTOmccg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-diff: 4.0.0
      array-unique: 0.3.2
      braces: 2.3.2
      define-property: 2.0.2
      extend-shallow: 3.0.2
      extglob: 2.0.4
      fragment-cache: 0.2.1
      kind-of: 6.0.3
      nanomatch: 1.2.13
      object.pick: 1.3.0
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /micromatch@4.0.8:
    resolution: {integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==}
    engines: {node: '>=8.6'}
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1
    dev: false

  /miller-rabin@4.0.1:
    resolution: {integrity: sha512-115fLhvZVqWwHPbClyntxEVfVDfl9DLLTuJvq3g2O/Oxi8AiNouAHvDSzHS0viUJc+V5vm3eq91Xwqn9dp4jRA==}
    hasBin: true
    dependencies:
      bn.js: 4.12.0
      brorand: 1.1.0
    dev: false

  /mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}
    dev: false

  /mime-db@1.53.0:
    resolution: {integrity: sha512-oHlN/w+3MQ3rba9rqFr6V/ypF10LSkdwUysQL7GkXoTgIWeV+tcXGA852TBxH+gsh8UWoyhR1hKcoMJTuWflpg==}
    engines: {node: '>= 0.6'}
    dev: false

  /mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: 1.52.0
    dev: false

  /mime@1.6.0:
    resolution: {integrity: sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==}
    engines: {node: '>=4'}
    hasBin: true
    dev: false

  /mime@2.5.2:
    resolution: {integrity: sha512-tqkh47FzKeCPD2PUiPB6pkbMzsCasjxAfC62/Wap5qrUWcb+sFasXUC5I3gYM5iBM8v/Qpn4UK0x+j0iHyFPDg==}
    engines: {node: '>=4.0.0'}
    hasBin: true
    dev: false

  /mimic-fn@2.1.0:
    resolution: {integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==}
    engines: {node: '>=6'}
    dev: false

  /minimalistic-assert@1.0.1:
    resolution: {integrity: sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A==}
    dev: false

  /minimalistic-crypto-utils@1.0.1:
    resolution: {integrity: sha512-JIYlbt6g8i5jKfJ3xz7rF0LXmv2TkDxBLUkiBeZ7bAx4GnnNMr8xFpGnOxn6GhTEHx3SjRrZEoU+j04prX1ktg==}
    dev: false

  /minimatch@3.0.8:
    resolution: {integrity: sha512-6FsRAQsxQ61mw+qP1ZzbL9Bc78x2p5OqNgNpnoAFLTrX8n5Kxph0CsnhmKKNXTWjXqU5L0pGPR7hYk+XWZr60Q==}
    dependencies:
      brace-expansion: 1.1.11
    dev: false

  /minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}
    dependencies:
      brace-expansion: 1.1.11
    dev: false

  /minimatch@5.1.6:
    resolution: {integrity: sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g==}
    engines: {node: '>=10'}
    dependencies:
      brace-expansion: 2.0.1
    dev: false

  /minimist@1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==}
    dev: false

  /minipass-collect@1.0.2:
    resolution: {integrity: sha512-6T6lH0H8OG9kITm/Jm6tdooIbogG9e0tLgpY6mphXSm/A9u8Nq1ryBG+Qspiub9LjWlBPsPS3tWQ/Botq4FdxA==}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.6
    dev: false

  /minipass-flush@1.0.5:
    resolution: {integrity: sha512-JmQSYYpPUqX5Jyn1mXaRwOda1uQ8HP5KAT/oDSLCzt1BYRhQU0/hDtsB1ufZfEEzMZ9aAVmsBw8+FWsIXlClWw==}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.6
    dev: false

  /minipass-pipeline@1.2.4:
    resolution: {integrity: sha512-xuIq7cIOt09RPRJ19gdi4b+RiNvDFYe5JH+ggNvBqGqpQXcru3PcRmOZuHBKWK1Txf9+cQ+HMVN4d6z46LZP7A==}
    engines: {node: '>=8'}
    dependencies:
      minipass: 3.3.6
    dev: false

  /minipass@3.3.6:
    resolution: {integrity: sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==}
    engines: {node: '>=8'}
    dependencies:
      yallist: 4.0.0
    dev: false

  /minipass@5.0.0:
    resolution: {integrity: sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ==}
    engines: {node: '>=8'}
    dev: false

  /minizlib@2.1.2:
    resolution: {integrity: sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.6
      yallist: 4.0.0
    dev: false

  /mississippi@3.0.0:
    resolution: {integrity: sha512-x471SsVjUtBRtcvd4BzKE9kFC+/2TeWgKCgw0bZcw1b9l2X3QX5vCWgF+KaZaYm87Ss//rHnWryupDrgLvmSkA==}
    engines: {node: '>=4.0.0'}
    dependencies:
      concat-stream: 1.6.2
      duplexify: 3.7.1
      end-of-stream: 1.4.4
      flush-write-stream: 1.1.1
      from2: 2.3.0
      parallel-transform: 1.2.0
      pump: 3.0.2
      pumpify: 1.5.1
      stream-each: 1.2.3
      through2: 2.0.5
    dev: false

  /mixin-deep@1.3.2:
    resolution: {integrity: sha512-WRoDn//mXBiJ1H40rqa3vH0toePwSsGb45iInWlTySa+Uu4k3tYUSxa2v1KqAiLtvlrSzaExqS1gtk96A9zvEA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      for-in: 1.0.2
      is-extendable: 1.0.1
    dev: false

  /mkdirp@0.5.6:
    resolution: {integrity: sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==}
    hasBin: true
    dependencies:
      minimist: 1.2.8
    dev: false

  /mkdirp@1.0.4:
    resolution: {integrity: sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==}
    engines: {node: '>=10'}
    hasBin: true
    dev: false

  /moment@2.30.1:
    resolution: {integrity: sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==}
    dev: false

  /move-concurrently@1.0.1:
    resolution: {integrity: sha512-hdrFxZOycD/g6A6SoI2bB5NA/5NEqD0569+S47WZhPvm46sD50ZHdYaFmnua5lndde9rCHGjmfK7Z8BuCt/PcQ==}
    deprecated: This package is no longer supported.
    dependencies:
      aproba: 1.2.0
      copy-concurrently: 1.0.5
      fs-write-stream-atomic: 1.0.10
      mkdirp: 0.5.6
      rimraf: 2.7.1
      run-queue: 1.0.3
    dev: false

  /mrmime@2.0.0:
    resolution: {integrity: sha512-eu38+hdgojoyq63s+yTpN4XMBdt5l8HhMhc4VKLO9KM5caLIBvUm4thi7fFaxyTmCKeNnXZ5pAlBwCUnhA09uw==}
    engines: {node: '>=10'}
    dev: false

  /ms@2.0.0:
    resolution: {integrity: sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==}
    dev: false

  /ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}
    dev: false

  /mustache@2.3.2:
    resolution: {integrity: sha512-KpMNwdQsYz3O/SBS1qJ/o3sqUJ5wSb8gb0pul8CO0S56b9Y2ALm8zCfsjPXsqGFfoNBkDwZuZIAjhsZI03gYVQ==}
    engines: {npm: '>=1.4.0'}
    hasBin: true
    dev: false

  /mute-stream@0.0.8:
    resolution: {integrity: sha512-nnbWWOkoWyUsTjKrhgD0dcz22mdkSnpYqbEjIm2nhwhuxlSkpywJmBo8h0ZqJdkp73mb90SssHkN4rsRaBAfAA==}
    dev: false

  /nan@2.20.0:
    resolution: {integrity: sha512-bk3gXBZDGILuuo/6sKtr0DQmSThYHLtNCdSdXk9YkxD/jK6X2vmCyyXBBxyqZ4XcnzTyYEAThfX3DCEnLf6igw==}
    requiresBuild: true
    dev: false
    optional: true

  /nanoid@3.3.7:
    resolution: {integrity: sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true
    dev: false

  /nanomatch@1.2.13:
    resolution: {integrity: sha512-fpoe2T0RbHwBTBUOftAfBPaDEi06ufaUai0mE6Yn1kacc3SnTErfb/h+X94VXzI64rKFHYImXSvdwGGCmwOqCA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-diff: 4.0.0
      array-unique: 0.3.2
      define-property: 2.0.2
      extend-shallow: 3.0.2
      fragment-cache: 0.2.1
      is-windows: 1.0.2
      kind-of: 6.0.3
      object.pick: 1.3.0
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /native-request@1.1.2:
    resolution: {integrity: sha512-/etjwrK0J4Ebbcnt35VMWnfiUX/B04uwGJxyJInagxDqf2z5drSt/lsOvEMWGYunz1kaLZAFrV4NDAbOoDKvAQ==}
    requiresBuild: true
    dev: false
    optional: true

  /negotiator@0.6.3:
    resolution: {integrity: sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==}
    engines: {node: '>= 0.6'}
    dev: false

  /neo-async@2.6.2:
    resolution: {integrity: sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==}
    dev: false

  /no-case@3.0.4:
    resolution: {integrity: sha512-fgAN3jGAh+RoxUGZHTSOLJIqUc2wmoBwGR4tbpNAKmmovFoWq0OdRkb0VkldReO2a2iBT/OEulG9XSUc10r3zg==}
    dependencies:
      lower-case: 2.0.2
      tslib: 2.7.0
    dev: false

  /node-fetch-native@1.6.4:
    resolution: {integrity: sha512-IhOigYzAKHd244OC0JIMIUrjzctirCmPkaIfhDeGcEETWof5zKYUW7e7MYvChGWh/4CJeXEgsRyGzuF334rOOQ==}
    dev: false

  /node-fetch@2.7.0:
    resolution: {integrity: sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==}
    engines: {node: 4.x || >=6.0.0}
    peerDependencies:
      encoding: ^0.1.0
    peerDependenciesMeta:
      encoding:
        optional: true
    dependencies:
      whatwg-url: 5.0.0
    dev: false

  /node-html-parser@6.1.13:
    resolution: {integrity: sha512-qIsTMOY4C/dAa5Q5vsobRpOOvPfC4pB61UVW2uSwZNUp0QU/jCekTal1vMmbO0DgdHeLUJpv/ARmDqErVxA3Sg==}
    dependencies:
      css-select: 5.1.0
      he: 1.2.0
    dev: false

  /node-libs-browser@2.2.1:
    resolution: {integrity: sha512-h/zcD8H9kaDZ9ALUWwlBUDo6TKF8a7qBSCSEGfjTVIYeqsioSKaAX+BN7NgiMGp6iSIXZ3PxgCu8KS3b71YK5Q==}
    dependencies:
      assert: 1.5.1
      browserify-zlib: 0.2.0
      buffer: 4.9.2
      console-browserify: 1.2.0
      constants-browserify: 1.0.0
      crypto-browserify: 3.12.0
      domain-browser: 1.2.0
      events: 3.3.0
      https-browserify: 1.0.0
      os-browserify: 0.3.0
      path-browserify: 0.0.1
      process: 0.11.10
      punycode: 1.4.1
      querystring-es3: 0.2.1
      readable-stream: 2.3.8
      stream-browserify: 2.0.2
      stream-http: 2.8.3
      string_decoder: 1.3.0
      timers-browserify: 2.0.12
      tty-browserify: 0.0.0
      url: 0.11.4
      util: 0.11.1
      vm-browserify: 1.1.2
    dev: false

  /node-object-hash@1.4.2:
    resolution: {integrity: sha512-UdS4swXs85fCGWWf6t6DMGgpN/vnlKeSGEQ7hJcrs7PBFoxoKLmibc3QRb7fwiYsjdL7PX8iI/TMSlZ90dgHhQ==}
    engines: {node: '>=0.10.0'}
    dev: false

  /node-releases@2.0.18:
    resolution: {integrity: sha512-d9VeXT4SJ7ZeOqGX6R5EM022wpL+eWPooLI+5UpWn2jCT1aosUQEhQP214x33Wkwx3JQMvIm+tIoVOdodFS40g==}
    dev: false

  /node-res@5.0.1:
    resolution: {integrity: sha512-YOleO9c7MAqoHC+Ccu2vzvV1fL6Ku49gShq3PIMKWHRgrMSih3XcwL05NbLBi6oU2J471gTBfdpVVxwT6Pfhxg==}
    dependencies:
      destroy: 1.2.0
      etag: 1.8.1
      mime-types: 2.1.35
      on-finished: 2.4.1
      vary: 1.1.2
    dev: false

  /normalize-path@2.1.1:
    resolution: {integrity: sha512-3pKJwH184Xo/lnH6oyP1q2pMd7HcypqqmRs91/6/i2CGtWwIKGCkOOMTm/zXbgTEWHw1uNpNi/igc3ePOYHb6w==}
    engines: {node: '>=0.10.0'}
    requiresBuild: true
    dependencies:
      remove-trailing-separator: 1.1.0
    dev: false
    optional: true

  /normalize-path@3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /normalize-range@0.1.2:
    resolution: {integrity: sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /normalize-url@1.9.1:
    resolution: {integrity: sha512-A48My/mtCklowHBlI8Fq2jFWK4tX4lJ5E6ytFsSOq1fzpvT0SQSgKhSg7lN5c2uYFOrUAOQp6zhhJnpp1eMloQ==}
    engines: {node: '>=4'}
    dependencies:
      object-assign: 4.1.1
      prepend-http: 1.0.4
      query-string: 4.3.4
      sort-keys: 1.1.2
    dev: false

  /normalize-url@6.1.0:
    resolution: {integrity: sha512-DlL+XwOy3NxAQ8xuC0okPgK46iuVNAK01YN7RueYBqqFeGsBjV9XmCAzAdgt+667bCl5kPh9EqKKDwnaPG1I7A==}
    engines: {node: '>=10'}
    dev: false

  /normalize-wheel@1.0.1:
    resolution: {integrity: sha512-1OnlAPZ3zgrk8B91HyRj+eVv+kS5u+Z0SCsak6Xil/kmgEia50ga7zfkumayonZrImffAxPU/5WcyGhzetHNPA==}
    dev: false

  /npm-run-path@4.0.1:
    resolution: {integrity: sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==}
    engines: {node: '>=8'}
    dependencies:
      path-key: 3.1.1
    dev: false

  /nth-check@2.1.1:
    resolution: {integrity: sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==}
    dependencies:
      boolbase: 1.0.0
    dev: false

  /nuxt@2.18.1(consola@3.2.3)(vue@2.7.16):
    resolution: {integrity: sha512-SZFOLDKgCfLu23BrQE0YYNWeoi/h+fw07TNDNDzRfbmMvQlStgTBG7lqeELytXdQnaPKWjWAYo12K7pPPRZb9Q==}
    deprecated: Nuxt 2 has reached EOL and is no longer actively maintained. See https://nuxt.com/blog/nuxt2-eol for more details.
    hasBin: true
    requiresBuild: true
    dependencies:
      '@nuxt/babel-preset-app': 2.18.1(vue@2.7.16)
      '@nuxt/builder': 2.18.1(vue@2.7.16)
      '@nuxt/cli': 2.18.1
      '@nuxt/components': 2.2.1(consola@3.2.3)
      '@nuxt/config': 2.18.1
      '@nuxt/core': 2.18.1
      '@nuxt/generator': 2.18.1
      '@nuxt/loading-screen': 2.0.4
      '@nuxt/opencollective': 0.4.0
      '@nuxt/server': 2.18.1
      '@nuxt/telemetry': 1.5.0
      '@nuxt/utils': 2.18.1
      '@nuxt/vue-app': 2.18.1
      '@nuxt/vue-renderer': 2.18.1
      '@nuxt/webpack': 2.18.1(vue@2.7.16)
    transitivePeerDependencies:
      - '@vue/compiler-sfc'
      - arc-templates
      - atpl
      - babel-core
      - bluebird
      - bracket-template
      - buffer
      - bufferutil
      - coffee-script
      - consola
      - dot
      - dust
      - dustjs-helpers
      - dustjs-linkedin
      - eco
      - ect
      - ejs
      - encoding
      - haml-coffee
      - hamlet
      - hamljs
      - handlebars
      - hogan.js
      - htmling
      - jade
      - jazz
      - jqtpl
      - just
      - liquid-node
      - liquor
      - marko
      - mote
      - mustache
      - nunjucks
      - plates
      - prettier
      - pug
      - qejs
      - ractive
      - razor-tmpl
      - react
      - react-dom
      - slm
      - squirrelly
      - supports-color
      - swig
      - swig-templates
      - teacup
      - templayed
      - then-jade
      - then-pug
      - tinyliquid
      - toffee
      - twig
      - twing
      - typescript
      - underscore
      - utf-8-validate
      - vash
      - velocityjs
      - vue
      - walrus
      - webpack-cli
      - webpack-command
      - whiskers
    dev: false

  /object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}
    dev: false

  /object-copy@0.1.0:
    resolution: {integrity: sha512-79LYn6VAb63zgtmAteVOWo9Vdj71ZVBy3Pbse+VqxDpEP83XuujMrGqHIwAXJ5I/aM0zU7dIyIAhifVTPrNItQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      copy-descriptor: 0.1.1
      define-property: 0.2.5
      kind-of: 3.2.2
    dev: false

  /object-inspect@1.13.2:
    resolution: {integrity: sha512-IRZSRuzJiynemAXPYtPe5BoI/RESNYR7TYm50MC5Mqbd3Jmw5y790sErYw3V6SryFJD64b74qQQs9wn5Bg/k3g==}
    engines: {node: '>= 0.4'}
    dev: false

  /object-keys@1.1.1:
    resolution: {integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==}
    engines: {node: '>= 0.4'}
    dev: false

  /object-visit@1.0.1:
    resolution: {integrity: sha512-GBaMwwAVK9qbQN3Scdo0OyvgPW7l3lnaVMj84uTOZlswkX0KpF6fyDBJhtTthf7pymztoN36/KEr1DyhF96zEA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: 3.0.1
    dev: false

  /object.assign@4.1.5:
    resolution: {integrity: sha512-byy+U7gp+FVwmyzKPYhW2h5l3crpmGsxl7X2s8y43IgxvG4g3QZ6CffDtsNQy1WsmZpQbO+ybo0AlW7TY6DcBQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      has-symbols: 1.0.3
      object-keys: 1.1.1
    dev: false

  /object.getownpropertydescriptors@2.1.8:
    resolution: {integrity: sha512-qkHIGe4q0lSYMv0XI4SsBTJz3WaURhLvd0lKSgtVuOsJ2krg4SgMw3PIRQFMp07yi++UR3se2mkcLqsBNpBb/A==}
    engines: {node: '>= 0.8'}
    dependencies:
      array.prototype.reduce: 1.0.7
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-object-atoms: 1.0.0
      gopd: 1.0.1
      safe-array-concat: 1.1.2
    dev: false

  /object.pick@1.3.0:
    resolution: {integrity: sha512-tqa/UMy/CCoYmj+H5qc07qvSL9dqcs/WZENZ1JbtWBlATP+iVOe778gE6MSijnyCnORzDuX6hU+LA4SZ09YjFQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: 3.0.1
    dev: false

  /on-finished@2.3.0:
    resolution: {integrity: sha512-ikqdkGAAyf/X/gPhXGvfgAytDZtDbr+bkNUJ0N9h5MI/dmdgCs3l6hoHrcUv41sRKew3jIwrp4qQDXiK99Utww==}
    engines: {node: '>= 0.8'}
    dependencies:
      ee-first: 1.1.1
    dev: false

  /on-finished@2.4.1:
    resolution: {integrity: sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==}
    engines: {node: '>= 0.8'}
    dependencies:
      ee-first: 1.1.1
    dev: false

  /on-headers@1.0.2:
    resolution: {integrity: sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==}
    engines: {node: '>= 0.8'}
    dev: false

  /once@1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==}
    dependencies:
      wrappy: 1.0.2
    dev: false

  /onetime@5.1.2:
    resolution: {integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==}
    engines: {node: '>=6'}
    dependencies:
      mimic-fn: 2.1.0
    dev: false

  /opener@1.5.2:
    resolution: {integrity: sha512-ur5UIdyw5Y7yEj9wLzhqXiy6GZ3Mwx0yGI+5sMn2r0N0v3cKJvUmFH5yPP+WXh9e0xfyzyJX95D8l088DNFj7A==}
    hasBin: true
    dev: false

  /optimize-css-assets-webpack-plugin@6.0.1(webpack@4.47.0):
    resolution: {integrity: sha512-BshV2UZPfggZLdUfN3zFBbG4sl/DynUI+YCB6fRRDWaqO2OiWN8GPcp4Y0/fEV6B3k9Hzyk3czve3V/8B/SzKQ==}
    peerDependencies:
      webpack: ^4.0.0
    dependencies:
      cssnano: 5.1.15(postcss@8.4.47)
      last-call-webpack-plugin: 3.0.0
      postcss: 8.4.47
      webpack: 4.47.0
    dev: false

  /os-browserify@0.3.0:
    resolution: {integrity: sha512-gjcpUc3clBf9+210TRaDWbf+rZZZEshZ+DlXMRCeAjp0xhTrnQsKHypIy1J3d5hKdUzj69t708EHtU8P6bUn0A==}
    dev: false

  /os-tmpdir@1.0.2:
    resolution: {integrity: sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g==}
    engines: {node: '>=0.10.0'}
    dev: false

  /p-limit@2.3.0:
    resolution: {integrity: sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==}
    engines: {node: '>=6'}
    dependencies:
      p-try: 2.2.0
    dev: false

  /p-limit@3.1.0:
    resolution: {integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==}
    engines: {node: '>=10'}
    dependencies:
      yocto-queue: 0.1.0
    dev: false

  /p-locate@3.0.0:
    resolution: {integrity: sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ==}
    engines: {node: '>=6'}
    dependencies:
      p-limit: 2.3.0
    dev: false

  /p-locate@4.1.0:
    resolution: {integrity: sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==}
    engines: {node: '>=8'}
    dependencies:
      p-limit: 2.3.0
    dev: false

  /p-map@4.0.0:
    resolution: {integrity: sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ==}
    engines: {node: '>=10'}
    dependencies:
      aggregate-error: 3.1.0
    dev: false

  /p-try@2.2.0:
    resolution: {integrity: sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==}
    engines: {node: '>=6'}
    dev: false

  /pako@1.0.11:
    resolution: {integrity: sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw==}
    dev: false

  /pako@2.1.0:
    resolution: {integrity: sha512-w+eufiZ1WuJYgPXbV/PO3NCMEc3xqylkKHzp8bxp1uW4qaSNQUkwmLLEc3kKsfz8lpV1F8Ht3U1Cm+9Srog2ug==}
    dev: false

  /papaparse@5.4.1:
    resolution: {integrity: sha512-HipMsgJkZu8br23pW15uvo6sib6wne/4woLZPlFf3rpDyMe9ywEXUsuD7+6K9PRkJlVT51j/sCOYDKGGS3ZJrw==}
    dev: false

  /parallel-transform@1.2.0:
    resolution: {integrity: sha512-P2vSmIu38uIlvdcU7fDkyrxj33gTUy/ABO5ZUbGowxNCopBq/OoD42bP4UmMrJoPyk4Uqf0mu3mtWBhHCZD8yg==}
    dependencies:
      cyclist: 1.0.2
      inherits: 2.0.4
      readable-stream: 2.3.8
    dev: false

  /param-case@3.0.4:
    resolution: {integrity: sha512-RXlj7zCYokReqWpOPH9oYivUzLYZ5vAPIfEmCTNViosC78F8F0H9y7T7gG2M39ymgutxF5gcFEsyZQSph9Bp3A==}
    dependencies:
      dot-case: 3.0.4
      tslib: 2.7.0
    dev: false

  /parent-module@1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}
    dependencies:
      callsites: 3.1.0
    dev: false

  /parse-asn1@5.1.7:
    resolution: {integrity: sha512-CTM5kuWR3sx9IFamcl5ErfPl6ea/N8IYwiJ+vpeB2g+1iknv7zBl5uPwbMbRVznRVbrNY6lGuDoE5b30grmbqg==}
    engines: {node: '>= 0.10'}
    dependencies:
      asn1.js: 4.10.1
      browserify-aes: 1.2.0
      evp_bytestokey: 1.0.3
      hash-base: 3.0.4
      pbkdf2: 3.1.2
      safe-buffer: 5.2.1
    dev: false

  /parse-git-config@3.0.0:
    resolution: {integrity: sha512-wXoQGL1D+2COYWCD35/xbiKma1Z15xvZL8cI25wvxzled58V51SJM04Urt/uznS900iQor7QO04SgdfT/XlbuA==}
    engines: {node: '>=8'}
    dependencies:
      git-config-path: 2.0.0
      ini: 1.3.8
    dev: false

  /parse-json@4.0.0:
    resolution: {integrity: sha512-aOIos8bujGN93/8Ox/jPLh7RwVnPEysynVFE+fQZyg6jKELEHwzgKdLRFHUgXJL6kylijVSBC4BvN9OmsB48Rw==}
    engines: {node: '>=4'}
    dependencies:
      error-ex: 1.3.2
      json-parse-better-errors: 1.0.2
    dev: false

  /parse-json@5.2.0:
    resolution: {integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==}
    engines: {node: '>=8'}
    dependencies:
      '@babel/code-frame': 7.24.7
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4
    dev: false

  /parse-path@7.0.0:
    resolution: {integrity: sha512-Euf9GG8WT9CdqwuWJGdf3RkUcTBArppHABkO7Lm8IzRQp0e2r/kkFnmhu4TSK30Wcu5rVAZLmfPKSBBi9tWFog==}
    dependencies:
      protocols: 2.0.1
    dev: false

  /parse-url@8.1.0:
    resolution: {integrity: sha512-xDvOoLU5XRrcOZvnI6b8zA6n9O9ejNk/GExuz1yBuWUGn9KA97GI6HTs6u02wKara1CeVmZhH+0TZFdWScR89w==}
    dependencies:
      parse-path: 7.0.0
    dev: false

  /parse5-htmlparser2-tree-adapter@7.1.0:
    resolution: {integrity: sha512-ruw5xyKs6lrpo9x9rCZqZZnIUntICjQAd0Wsmp396Ul9lN/h+ifgVV1x1gZHi8euej6wTfpqX8j+BFQxF0NS/g==}
    dependencies:
      domhandler: 5.0.3
      parse5: 7.3.0
    dev: false

  /parse5-parser-stream@7.1.2:
    resolution: {integrity: sha512-JyeQc9iwFLn5TbvvqACIF/VXG6abODeB3Fwmv/TGdLk2LfbWkaySGY72at4+Ty7EkPZj854u4CrICqNk2qIbow==}
    dependencies:
      parse5: 7.3.0
    dev: false

  /parse5@7.3.0:
    resolution: {integrity: sha512-IInvU7fabl34qmi9gY8XOVxhYyMyuH2xUNpb2q8/Y+7552KlejkRvqvD19nMoUW/uQGGbqNpA6Tufu5FL5BZgw==}
    dependencies:
      entities: 6.0.1
    dev: false

  /parseurl@1.3.3:
    resolution: {integrity: sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==}
    engines: {node: '>= 0.8'}
    dev: false

  /pascal-case@3.1.2:
    resolution: {integrity: sha512-uWlGT3YSnK9x3BQJaOdcZwrnV6hPpd8jFH1/ucpiLRPh/2zCVJKS19E4GvYHvaCcACn3foXZ0cLB9Wrx1KGe5g==}
    dependencies:
      no-case: 3.0.4
      tslib: 2.7.0
    dev: false

  /pascalcase@0.1.1:
    resolution: {integrity: sha512-XHXfu/yOQRy9vYOtUDVMN60OEJjW013GoObG1o+xwQTpB9eYJX/BjXMsdW13ZDPruFhYYn0AG22w0xgQMwl3Nw==}
    engines: {node: '>=0.10.0'}
    dev: false

  /path-browserify@0.0.1:
    resolution: {integrity: sha512-BapA40NHICOS+USX9SN4tyhq+A2RrN/Ws5F0Z5aMHDp98Fl86lX8Oti8B7uN93L4Ifv4fHOEA+pQw87gmMO/lQ==}
    dev: false

  /path-dirname@1.0.2:
    resolution: {integrity: sha512-ALzNPpyNq9AqXMBjeymIjFDAkAFH06mHJH/cSBHAgU0s4vfpBn6b2nf8tiRLvagKD8RbTpq2FKTBg7cl9l3c7Q==}
    requiresBuild: true
    dev: false
    optional: true

  /path-exists@3.0.0:
    resolution: {integrity: sha512-bpC7GYwiDYQ4wYLe+FA8lhRjhQCMcQGuSgGGqDkg/QerRWw9CmGRT0iSOVRSZJ29NMLZgIzqaljJ63oaL4NIJQ==}
    engines: {node: '>=4'}
    dev: false

  /path-exists@4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}
    dev: false

  /path-is-absolute@1.0.1:
    resolution: {integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==}
    engines: {node: '>=0.10.0'}
    dev: false

  /path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}
    dev: false

  /path-parse@1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}
    dev: false

  /path-to-regexp@6.3.0:
    resolution: {integrity: sha512-Yhpw4T9C6hPpgPeA28us07OJeqZ5EzQTkbfwuhsUg0c237RomFoETJgmp2sa3F/41gfLE6G5cqcYwznmeEeOlQ==}
    dev: false

  /path-type@4.0.0:
    resolution: {integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==}
    engines: {node: '>=8'}
    dev: false

  /pbkdf2@3.1.2:
    resolution: {integrity: sha512-iuh7L6jA7JEGu2WxDwtQP1ddOpaJNC4KlDEFfdQajSGgGPNi4OyDc2R7QnbY2bR9QjBVGwgvTdNJZoE7RaxUMA==}
    engines: {node: '>=0.12'}
    dependencies:
      create-hash: 1.2.0
      create-hmac: 1.1.7
      ripemd160: 2.0.2
      safe-buffer: 5.2.1
      sha.js: 2.4.11
    dev: false

  /picocolors@0.2.1:
    resolution: {integrity: sha512-cMlDqaLEqfSaW8Z7N5Jw+lyIW869EzT73/F5lhtY9cLGoVxSXznfgfXMO0Z5K0o0Q2TkTXq+0KFsdnSe3jDViA==}
    dev: false

  /picocolors@1.1.0:
    resolution: {integrity: sha512-TQ92mBOW0l3LeMeyLV6mzy/kWr8lkd/hp3mTg7wYK7zJhuBStmGMBG0BdeDZS/dZx1IukaX6Bk11zcln25o1Aw==}
    dev: false

  /picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}
    dev: false

  /pify@2.3.0:
    resolution: {integrity: sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==}
    engines: {node: '>=0.10.0'}
    dev: false

  /pify@3.0.0:
    resolution: {integrity: sha512-C3FsVNH1udSEX48gGX1xfvwTWfsYWj5U+8/uK15BGzIGrKoUpghX8hWZwa/OFnakBiiVNmBvemTJR5mcy7iPcg==}
    engines: {node: '>=4'}
    dev: false

  /pify@4.0.1:
    resolution: {integrity: sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==}
    engines: {node: '>=6'}
    requiresBuild: true
    dev: false

  /pify@5.0.0:
    resolution: {integrity: sha512-eW/gHNMlxdSP6dmG6uJip6FXN0EQBwm2clYYd8Wul42Cwu/DK8HEftzsapcNdYe2MfLiIwZqsDk2RDEsTE79hA==}
    engines: {node: '>=10'}
    dev: false

  /pkg-dir@3.0.0:
    resolution: {integrity: sha512-/E57AYkoeQ25qkxMj5PBOVgF8Kiu/h7cYS30Z5+R7WaiCCBfLq58ZI/dSeaEKb9WVJV5n/03QwrN3IeWIFllvw==}
    engines: {node: '>=6'}
    dependencies:
      find-up: 3.0.0
    dev: false

  /pkg-dir@4.2.0:
    resolution: {integrity: sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==}
    engines: {node: '>=8'}
    dependencies:
      find-up: 4.1.0
    dev: false

  /pnp-webpack-plugin@1.7.0:
    resolution: {integrity: sha512-2Rb3vm+EXble/sMXNSu6eoBx8e79gKqhNq9F5ZWW6ERNCTE/Q0wQNne5541tE5vKjfM8hpNCYL+LGc1YTfI0dg==}
    engines: {node: '>=6'}
    dependencies:
      ts-pnp: 1.2.0
    transitivePeerDependencies:
      - typescript
    dev: false

  /posix-character-classes@0.1.1:
    resolution: {integrity: sha512-xTgYBc3fuo7Yt7JbiuFxSYGToMoz8fLoE6TC9Wx1P/u+LfeThMOAqmuyECnlBaaJb+u1m9hHiXUEtwW4OzfUJg==}
    engines: {node: '>=0.10.0'}
    dev: false

  /possible-typed-array-names@1.0.0:
    resolution: {integrity: sha512-d7Uw+eZoloe0EHDIYoe+bQ5WXnGMOpmiZFTuMWCwpjzzkL2nTjcKiAk4hh8TjnGye2TwWOk3UXucZ+3rbmBa8Q==}
    engines: {node: '>= 0.4'}
    dev: false

  /postcss-attribute-case-insensitive@6.0.3(postcss@8.4.47):
    resolution: {integrity: sha512-KHkmCILThWBRtg+Jn1owTnHPnFit4OkqS+eKiGEOPIGke54DCeYGJ6r0Fx/HjfE9M9kznApCLcU0DvnPchazMQ==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.47
      postcss-selector-parser: 6.1.2
    dev: false

  /postcss-calc@10.0.2(postcss@8.4.47):
    resolution: {integrity: sha512-DT/Wwm6fCKgpYVI7ZEWuPJ4az8hiEHtCUeYjZXqU7Ou4QqYh1Df2yCQ7Ca6N7xqKPFkxN3fhf+u9KSoOCJNAjg==}
    engines: {node: ^18.12 || ^20.9 || >=22.0}
    peerDependencies:
      postcss: ^8.4.38
    dependencies:
      postcss: 8.4.47
      postcss-selector-parser: 6.1.2
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-calc@8.2.4(postcss@8.4.47):
    resolution: {integrity: sha512-SmWMSJmB8MRnnULldx0lQIyhSNvuDl9HfrZkaqqE/WHAhToYsAvDq+yAsA/kIyINDszOp3Rh0GFoNuH5Ypsm3Q==}
    peerDependencies:
      postcss: ^8.2.2
    dependencies:
      postcss: 8.4.47
      postcss-selector-parser: 6.1.2
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-clamp@4.1.0(postcss@8.4.47):
    resolution: {integrity: sha512-ry4b1Llo/9zz+PKC+030KUnPITTJAHeOwjfAyyB60eT0AorGLdzp52s31OsPRHRf8NchkgFoG2y6fCfn1IV1Ow==}
    engines: {node: '>=7.6.0'}
    peerDependencies:
      postcss: ^8.4.6
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-color-functional-notation@6.0.14(postcss@8.4.47):
    resolution: {integrity: sha512-dNUX+UH4dAozZ8uMHZ3CtCNYw8fyFAmqqdcyxMr7PEdM9jLXV19YscoYO0F25KqZYhmtWKQ+4tKrIZQrwzwg7A==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/css-color-parser': 2.0.5(@csstools/css-parser-algorithms@2.7.1)(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-parser-algorithms': 2.7.1(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-tokenizer': 2.4.1
      '@csstools/postcss-progressive-custom-properties': 3.3.0(postcss@8.4.47)
      '@csstools/utilities': 1.0.0(postcss@8.4.47)
      postcss: 8.4.47
    dev: false

  /postcss-color-hex-alpha@9.0.4(postcss@8.4.47):
    resolution: {integrity: sha512-XQZm4q4fNFqVCYMGPiBjcqDhuG7Ey2xrl99AnDJMyr5eDASsAGalndVgHZF8i97VFNy1GQeZc4q2ydagGmhelQ==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/utilities': 1.0.0(postcss@8.4.47)
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-color-rebeccapurple@9.0.3(postcss@8.4.47):
    resolution: {integrity: sha512-ruBqzEFDYHrcVq3FnW3XHgwRqVMrtEPLBtD7K2YmsLKVc2jbkxzzNEctJKsPCpDZ+LeMHLKRDoSShVefGc+CkQ==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/utilities': 1.0.0(postcss@8.4.47)
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-colormin@5.3.1(postcss@8.4.47):
    resolution: {integrity: sha512-UsWQG0AqTFQmpBegeLLc1+c3jIqBNB0zlDGRWR+dQ3pRKJL1oeMzyqmH3o2PIfn9MBdNrVPWhDbT769LxCTLJQ==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: 4.23.3
      caniuse-api: 3.0.0
      colord: 2.9.3
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-colormin@7.0.2(postcss@8.4.47):
    resolution: {integrity: sha512-YntRXNngcvEvDbEjTdRWGU606eZvB5prmHG4BF0yLmVpamXbpsRJzevyy6MZVyuecgzI2AWAlvFi8DAeCqwpvA==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      browserslist: 4.23.3
      caniuse-api: 3.0.0
      colord: 2.9.3
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-convert-values@5.1.3(postcss@8.4.47):
    resolution: {integrity: sha512-82pC1xkJZtcJEfiLw6UXnXVXScgtBrjlO5CBmuDQc+dlb88ZYheFsjTn40+zBVi3DkfF7iezO0nJUPLcJK3pvA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: 4.23.3
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-convert-values@7.0.4(postcss@8.4.47):
    resolution: {integrity: sha512-e2LSXPqEHVW6aoGbjV9RsSSNDO3A0rZLCBxN24zvxF25WknMPpX8Dm9UxxThyEbaytzggRuZxaGXqaOhxQ514Q==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      browserslist: 4.23.3
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-custom-media@10.0.8(postcss@8.4.47):
    resolution: {integrity: sha512-V1KgPcmvlGdxTel4/CyQtBJEFhMVpEmRGFrnVtgfGIHj5PJX9vO36eFBxKBeJn+aCDTed70cc+98Mz3J/uVdGQ==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/cascade-layer-name-parser': 1.0.13(@csstools/css-parser-algorithms@2.7.1)(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-parser-algorithms': 2.7.1(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-tokenizer': 2.4.1
      '@csstools/media-query-list-parser': 2.1.13(@csstools/css-parser-algorithms@2.7.1)(@csstools/css-tokenizer@2.4.1)
      postcss: 8.4.47
    dev: false

  /postcss-custom-properties@13.3.12(postcss@8.4.47):
    resolution: {integrity: sha512-oPn/OVqONB2ZLNqN185LDyaVByELAA/u3l2CS2TS16x2j2XsmV4kd8U49+TMxmUsEU9d8fB/I10E6U7kB0L1BA==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/cascade-layer-name-parser': 1.0.13(@csstools/css-parser-algorithms@2.7.1)(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-parser-algorithms': 2.7.1(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-tokenizer': 2.4.1
      '@csstools/utilities': 1.0.0(postcss@8.4.47)
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-custom-selectors@7.1.12(postcss@8.4.47):
    resolution: {integrity: sha512-ctIoprBMJwByYMGjXG0F7IT2iMF2hnamQ+aWZETyBM0aAlyaYdVZTeUkk8RB+9h9wP+NdN3f01lfvKl2ZSqC0g==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/cascade-layer-name-parser': 1.0.13(@csstools/css-parser-algorithms@2.7.1)(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-parser-algorithms': 2.7.1(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-tokenizer': 2.4.1
      postcss: 8.4.47
      postcss-selector-parser: 6.1.2
    dev: false

  /postcss-dir-pseudo-class@8.0.1(postcss@8.4.47):
    resolution: {integrity: sha512-uULohfWBBVoFiZXgsQA24JV6FdKIidQ+ZqxOouhWwdE+qJlALbkS5ScB43ZTjPK+xUZZhlaO/NjfCt5h4IKUfw==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.47
      postcss-selector-parser: 6.1.2
    dev: false

  /postcss-discard-comments@5.1.2(postcss@8.4.47):
    resolution: {integrity: sha512-+L8208OVbHVF2UQf1iDmRcbdjJkuBF6IS29yBDSiWUIzpYaAhtNl6JYnYm12FnkeCwQqF5LeklOu6rAqgfBZqQ==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.47
    dev: false

  /postcss-discard-comments@7.0.3(postcss@8.4.47):
    resolution: {integrity: sha512-q6fjd4WU4afNhWOA2WltHgCbkRhZPgQe7cXF74fuVB/ge4QbM9HEaOIzGSiMvM+g/cOsNAUGdf2JDzqA2F8iLA==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.47
      postcss-selector-parser: 6.1.2
    dev: false

  /postcss-discard-duplicates@5.1.0(postcss@8.4.47):
    resolution: {integrity: sha512-zmX3IoSI2aoenxHV6C7plngHWWhUOV3sP1T8y2ifzxzbtnuhk1EdPwm0S1bIUNaJ2eNbWeGLEwzw8huPD67aQw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.47
    dev: false

  /postcss-discard-duplicates@7.0.1(postcss@8.4.47):
    resolution: {integrity: sha512-oZA+v8Jkpu1ct/xbbrntHRsfLGuzoP+cpt0nJe5ED2FQF8n8bJtn7Bo28jSmBYwqgqnqkuSXJfSUEE7if4nClQ==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.47
    dev: false

  /postcss-discard-empty@5.1.1(postcss@8.4.47):
    resolution: {integrity: sha512-zPz4WljiSuLWsI0ir4Mcnr4qQQ5e1Ukc3i7UfE2XcrwKK2LIPIqE5jxMRxO6GbI3cv//ztXDsXwEWT3BHOGh3A==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.47
    dev: false

  /postcss-discard-empty@7.0.0(postcss@8.4.47):
    resolution: {integrity: sha512-e+QzoReTZ8IAwhnSdp/++7gBZ/F+nBq9y6PomfwORfP7q9nBpK5AMP64kOt0bA+lShBFbBDcgpJ3X4etHg4lzA==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.47
    dev: false

  /postcss-discard-overridden@5.1.0(postcss@8.4.47):
    resolution: {integrity: sha512-21nOL7RqWR1kasIVdKs8HNqQJhFxLsyRfAnUDm4Fe4t4mCWL9OJiHvlHPjcd8zc5Myu89b/7wZDnOSjFgeWRtw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.47
    dev: false

  /postcss-discard-overridden@7.0.0(postcss@8.4.47):
    resolution: {integrity: sha512-GmNAzx88u3k2+sBTZrJSDauR0ccpE24omTQCVmaTTZFz1du6AasspjaUPMJ2ud4RslZpoFKyf+6MSPETLojc6w==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.47
    dev: false

  /postcss-double-position-gradients@5.0.7(postcss@8.4.47):
    resolution: {integrity: sha512-1xEhjV9u1s4l3iP5lRt1zvMjI/ya8492o9l/ivcxHhkO3nOz16moC4JpMxDUGrOs4R3hX+KWT7gKoV842cwRgg==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/postcss-progressive-custom-properties': 3.3.0(postcss@8.4.47)
      '@csstools/utilities': 1.0.0(postcss@8.4.47)
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-focus-visible@9.0.1(postcss@8.4.47):
    resolution: {integrity: sha512-N2VQ5uPz3Z9ZcqI5tmeholn4d+1H14fKXszpjogZIrFbhaq0zNAtq8sAnw6VLiqGbL8YBzsnu7K9bBkTqaRimQ==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.47
      postcss-selector-parser: 6.1.2
    dev: false

  /postcss-focus-within@8.0.1(postcss@8.4.47):
    resolution: {integrity: sha512-NFU3xcY/xwNaapVb+1uJ4n23XImoC86JNwkY/uduytSl2s9Ekc2EpzmRR63+ExitnW3Mab3Fba/wRPCT5oDILA==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.47
      postcss-selector-parser: 6.1.2
    dev: false

  /postcss-font-variant@5.0.0(postcss@8.4.47):
    resolution: {integrity: sha512-1fmkBaCALD72CK2a9i468mA/+tr9/1cBxRRMXOUaZqO43oWPR5imcyPjXwuv7PXbCid4ndlP5zWhidQVVa3hmA==}
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      postcss: 8.4.47
    dev: false

  /postcss-gap-properties@5.0.1(postcss@8.4.47):
    resolution: {integrity: sha512-k2z9Cnngc24c0KF4MtMuDdToROYqGMMUQGcE6V0odwjHyOHtaDBlLeRBV70y9/vF7KIbShrTRZ70JjsI1BZyWw==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.47
    dev: false

  /postcss-image-set-function@6.0.3(postcss@8.4.47):
    resolution: {integrity: sha512-i2bXrBYzfbRzFnm+pVuxVePSTCRiNmlfssGI4H0tJQvDue+yywXwUxe68VyzXs7cGtMaH6MCLY6IbCShrSroCw==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/utilities': 1.0.0(postcss@8.4.47)
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-import-resolver@2.0.0:
    resolution: {integrity: sha512-y001XYgGvVwgxyxw9J1a5kqM/vtmIQGzx34g0A0Oy44MFcy/ZboZw1hu/iN3VYFjSTRzbvd7zZJJz0Kh0AGkTw==}
    dependencies:
      enhanced-resolve: 4.5.0
    dev: false

  /postcss-import@15.1.0(postcss@8.4.47):
    resolution: {integrity: sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      postcss: ^8.0.0
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
      read-cache: 1.0.0
      resolve: 1.22.8
    dev: false

  /postcss-lab-function@6.0.19(postcss@8.4.47):
    resolution: {integrity: sha512-vwln/mgvFrotJuGV8GFhpAOu9iGf3pvTBr6dLPDmUcqVD5OsQpEFyQMAFTxSxWXGEzBj6ld4pZ/9GDfEpXvo0g==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/css-color-parser': 2.0.5(@csstools/css-parser-algorithms@2.7.1)(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-parser-algorithms': 2.7.1(@csstools/css-tokenizer@2.4.1)
      '@csstools/css-tokenizer': 2.4.1
      '@csstools/postcss-progressive-custom-properties': 3.3.0(postcss@8.4.47)
      '@csstools/utilities': 1.0.0(postcss@8.4.47)
      postcss: 8.4.47
    dev: false

  /postcss-loader@4.3.0(postcss@8.4.47)(webpack@4.47.0):
    resolution: {integrity: sha512-M/dSoIiNDOo8Rk0mUqoj4kpGq91gcxCfb9PoyZVdZ76/AuhxylHDYZblNE8o+EQ9AMSASeMFEKxZf5aU6wlx1Q==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      postcss: ^7.0.0 || ^8.0.1
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      cosmiconfig: 7.1.0
      klona: 2.0.6
      loader-utils: 2.0.4
      postcss: 8.4.47
      schema-utils: 3.3.0
      semver: 7.6.3
      webpack: 4.47.0
    dev: false

  /postcss-logical@7.0.1(postcss@8.4.47):
    resolution: {integrity: sha512-8GwUQZE0ri0K0HJHkDv87XOLC8DE0msc+HoWLeKdtjDZEwpZ5xuK3QdV6FhmHSQW40LPkg43QzvATRAI3LsRkg==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-merge-longhand@5.1.7(postcss@8.4.47):
    resolution: {integrity: sha512-YCI9gZB+PLNskrK0BB3/2OzPnGhPkBEwmwhfYk1ilBHYVAZB7/tkTHFBAnCrvBBOmeYyMYw3DMjT55SyxMBzjQ==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
      stylehacks: 5.1.1(postcss@8.4.47)
    dev: false

  /postcss-merge-longhand@7.0.4(postcss@8.4.47):
    resolution: {integrity: sha512-zer1KoZA54Q8RVHKOY5vMke0cCdNxMP3KBfDerjH/BYHh4nCIh+1Yy0t1pAEQF18ac/4z3OFclO+ZVH8azjR4A==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
      stylehacks: 7.0.4(postcss@8.4.47)
    dev: false

  /postcss-merge-rules@5.1.4(postcss@8.4.47):
    resolution: {integrity: sha512-0R2IuYpgU93y9lhVbO/OylTtKMVcHb67zjWIfCiKR9rWL3GUk1677LAqD/BcHizukdZEjT8Ru3oHRoAYoJy44g==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: 4.23.3
      caniuse-api: 3.0.0
      cssnano-utils: 3.1.0(postcss@8.4.47)
      postcss: 8.4.47
      postcss-selector-parser: 6.1.2
    dev: false

  /postcss-merge-rules@7.0.4(postcss@8.4.47):
    resolution: {integrity: sha512-ZsaamiMVu7uBYsIdGtKJ64PkcQt6Pcpep/uO90EpLS3dxJi6OXamIobTYcImyXGoW0Wpugh7DSD3XzxZS9JCPg==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      browserslist: 4.23.3
      caniuse-api: 3.0.0
      cssnano-utils: 5.0.0(postcss@8.4.47)
      postcss: 8.4.47
      postcss-selector-parser: 6.1.2
    dev: false

  /postcss-minify-font-values@5.1.0(postcss@8.4.47):
    resolution: {integrity: sha512-el3mYTgx13ZAPPirSVsHqFzl+BBBDrXvbySvPGFnQcTI4iNslrPaFq4muTkLZmKlGk4gyFAYUBMH30+HurREyA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-minify-font-values@7.0.0(postcss@8.4.47):
    resolution: {integrity: sha512-2ckkZtgT0zG8SMc5aoNwtm5234eUx1GGFJKf2b1bSp8UflqaeFzR50lid4PfqVI9NtGqJ2J4Y7fwvnP/u1cQog==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-minify-gradients@5.1.1(postcss@8.4.47):
    resolution: {integrity: sha512-VGvXMTpCEo4qHTNSa9A0a3D+dxGFZCYwR6Jokk+/3oB6flu2/PnPXAh2x7x52EkY5xlIHLm+Le8tJxe/7TNhzw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      colord: 2.9.3
      cssnano-utils: 3.1.0(postcss@8.4.47)
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-minify-gradients@7.0.0(postcss@8.4.47):
    resolution: {integrity: sha512-pdUIIdj/C93ryCHew0UgBnL2DtUS3hfFa5XtERrs4x+hmpMYGhbzo6l/Ir5de41O0GaKVpK1ZbDNXSY6GkXvtg==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      colord: 2.9.3
      cssnano-utils: 5.0.0(postcss@8.4.47)
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-minify-params@5.1.4(postcss@8.4.47):
    resolution: {integrity: sha512-+mePA3MgdmVmv6g+30rn57USjOGSAyuxUmkfiWpzalZ8aiBkdPYjXWtHuwJGm1v5Ojy0Z0LaSYhHaLJQB0P8Jw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: 4.23.3
      cssnano-utils: 3.1.0(postcss@8.4.47)
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-minify-params@7.0.2(postcss@8.4.47):
    resolution: {integrity: sha512-nyqVLu4MFl9df32zTsdcLqCFfE/z2+f8GE1KHPxWOAmegSo6lpV2GNy5XQvrzwbLmiU7d+fYay4cwto1oNdAaQ==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      browserslist: 4.23.3
      cssnano-utils: 5.0.0(postcss@8.4.47)
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-minify-selectors@5.2.1(postcss@8.4.47):
    resolution: {integrity: sha512-nPJu7OjZJTsVUmPdm2TcaiohIwxP+v8ha9NehQ2ye9szv4orirRU3SDdtUmKH+10nzn0bAyOXZ0UEr7OpvLehg==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.47
      postcss-selector-parser: 6.1.2
    dev: false

  /postcss-minify-selectors@7.0.4(postcss@8.4.47):
    resolution: {integrity: sha512-JG55VADcNb4xFCf75hXkzc1rNeURhlo7ugf6JjiiKRfMsKlDzN9CXHZDyiG6x/zGchpjQS+UAgb1d4nqXqOpmA==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      cssesc: 3.0.0
      postcss: 8.4.47
      postcss-selector-parser: 6.1.2
    dev: false

  /postcss-modules-extract-imports@3.1.0(postcss@8.4.47):
    resolution: {integrity: sha512-k3kNe0aNFQDAZGbin48pL2VNidTF0w4/eASDsxlyspobzU3wZQLOGj7L9gfRe0Jo9/4uud09DsjFNH7winGv8Q==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      postcss: 8.4.47
    dev: false

  /postcss-modules-local-by-default@4.0.5(postcss@8.4.47):
    resolution: {integrity: sha512-6MieY7sIfTK0hYfafw1OMEG+2bg8Q1ocHCpoWLqOKj3JXlKu4G7btkmM/B7lFubYkYWmRSPLZi5chid63ZaZYw==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      icss-utils: 5.1.0(postcss@8.4.47)
      postcss: 8.4.47
      postcss-selector-parser: 6.1.2
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-modules-scope@3.2.0(postcss@8.4.47):
    resolution: {integrity: sha512-oq+g1ssrsZOsx9M96c5w8laRmvEu9C3adDSjI8oTcbfkrTE8hx/zfyobUoWIxaKPO8bt6S62kxpw5GqypEw1QQ==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      postcss: 8.4.47
      postcss-selector-parser: 6.1.2
    dev: false

  /postcss-modules-values@4.0.0(postcss@8.4.47):
    resolution: {integrity: sha512-RDxHkAiEGI78gS2ofyvCsu7iycRv7oqw5xMWn9iMoR0N/7mf9D50ecQqUo5BZ9Zh2vH4bCUR/ktCqbB9m8vJjQ==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      icss-utils: 5.1.0(postcss@8.4.47)
      postcss: 8.4.47
    dev: false

  /postcss-nesting@12.1.5(postcss@8.4.47):
    resolution: {integrity: sha512-N1NgI1PDCiAGWPTYrwqm8wpjv0bgDmkYHH72pNsqTCv9CObxjxftdYu6AKtGN+pnJa7FQjMm3v4sp8QJbFsYdQ==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/selector-resolve-nested': 1.1.0(postcss-selector-parser@6.1.2)
      '@csstools/selector-specificity': 3.1.1(postcss-selector-parser@6.1.2)
      postcss: 8.4.47
      postcss-selector-parser: 6.1.2
    dev: false

  /postcss-normalize-charset@5.1.0(postcss@8.4.47):
    resolution: {integrity: sha512-mSgUJ+pd/ldRGVx26p2wz9dNZ7ji6Pn8VWBajMXFf8jk7vUoSrZ2lt/wZR7DtlZYKesmZI680qjr2CeFF2fbUg==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.47
    dev: false

  /postcss-normalize-charset@7.0.0(postcss@8.4.47):
    resolution: {integrity: sha512-ABisNUXMeZeDNzCQxPxBCkXexvBrUHV+p7/BXOY+ulxkcjUZO0cp8ekGBwvIh2LbCwnWbyMPNJVtBSdyhM2zYQ==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.47
    dev: false

  /postcss-normalize-display-values@5.1.0(postcss@8.4.47):
    resolution: {integrity: sha512-WP4KIM4o2dazQXWmFaqMmcvsKmhdINFblgSeRgn8BJ6vxaMyaJkwAzpPpuvSIoG/rmX3M+IrRZEz2H0glrQNEA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-normalize-display-values@7.0.0(postcss@8.4.47):
    resolution: {integrity: sha512-lnFZzNPeDf5uGMPYgGOw7v0BfB45+irSRz9gHQStdkkhiM0gTfvWkWB5BMxpn0OqgOQuZG/mRlZyJxp0EImr2Q==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-normalize-positions@5.1.1(postcss@8.4.47):
    resolution: {integrity: sha512-6UpCb0G4eofTCQLFVuI3EVNZzBNPiIKcA1AKVka+31fTVySphr3VUgAIULBhxZkKgwLImhzMR2Bw1ORK+37INg==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-normalize-positions@7.0.0(postcss@8.4.47):
    resolution: {integrity: sha512-I0yt8wX529UKIGs2y/9Ybs2CelSvItfmvg/DBIjTnoUSrPxSV7Z0yZ8ShSVtKNaV/wAY+m7bgtyVQLhB00A1NQ==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-normalize-repeat-style@5.1.1(postcss@8.4.47):
    resolution: {integrity: sha512-mFpLspGWkQtBcWIRFLmewo8aC3ImN2i/J3v8YCFUwDnPu3Xz4rLohDO26lGjwNsQxB3YF0KKRwspGzE2JEuS0g==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-normalize-repeat-style@7.0.0(postcss@8.4.47):
    resolution: {integrity: sha512-o3uSGYH+2q30ieM3ppu9GTjSXIzOrRdCUn8UOMGNw7Af61bmurHTWI87hRybrP6xDHvOe5WlAj3XzN6vEO8jLw==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-normalize-string@5.1.0(postcss@8.4.47):
    resolution: {integrity: sha512-oYiIJOf4T9T1N4i+abeIc7Vgm/xPCGih4bZz5Nm0/ARVJ7K6xrDlLwvwqOydvyL3RHNf8qZk6vo3aatiw/go3w==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-normalize-string@7.0.0(postcss@8.4.47):
    resolution: {integrity: sha512-w/qzL212DFVOpMy3UGyxrND+Kb0fvCiBBujiaONIihq7VvtC7bswjWgKQU/w4VcRyDD8gpfqUiBQ4DUOwEJ6Qg==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-normalize-timing-functions@5.1.0(postcss@8.4.47):
    resolution: {integrity: sha512-DOEkzJ4SAXv5xkHl0Wa9cZLF3WCBhF3o1SKVxKQAa+0pYKlueTpCgvkFAHfk+Y64ezX9+nITGrDZeVGgITJXjg==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-normalize-timing-functions@7.0.0(postcss@8.4.47):
    resolution: {integrity: sha512-tNgw3YV0LYoRwg43N3lTe3AEWZ66W7Dh7lVEpJbHoKOuHc1sLrzMLMFjP8SNULHaykzsonUEDbKedv8C+7ej6g==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-normalize-unicode@5.1.1(postcss@8.4.47):
    resolution: {integrity: sha512-qnCL5jzkNUmKVhZoENp1mJiGNPcsJCs1aaRmURmeJGES23Z/ajaln+EPTD+rBeNkSryI+2WTdW+lwcVdOikrpA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: 4.23.3
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-normalize-unicode@7.0.2(postcss@8.4.47):
    resolution: {integrity: sha512-ztisabK5C/+ZWBdYC+Y9JCkp3M9qBv/XFvDtSw0d/XwfT3UaKeW/YTm/MD/QrPNxuecia46vkfEhewjwcYFjkg==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      browserslist: 4.23.3
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-normalize-url@5.1.0(postcss@8.4.47):
    resolution: {integrity: sha512-5upGeDO+PVthOxSmds43ZeMeZfKH+/DKgGRD7TElkkyS46JXAUhMzIKiCa7BabPeIy3AQcTkXwVVN7DbqsiCew==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      normalize-url: 6.1.0
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-normalize-url@7.0.0(postcss@8.4.47):
    resolution: {integrity: sha512-+d7+PpE+jyPX1hDQZYG+NaFD+Nd2ris6r8fPTBAjE8z/U41n/bib3vze8x7rKs5H1uEw5ppe9IojewouHk0klQ==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-normalize-whitespace@5.1.1(postcss@8.4.47):
    resolution: {integrity: sha512-83ZJ4t3NUDETIHTa3uEg6asWjSBYL5EdkVB0sDncx9ERzOKBVJIUeDO9RyA9Zwtig8El1d79HBp0JEi8wvGQnA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-normalize-whitespace@7.0.0(postcss@8.4.47):
    resolution: {integrity: sha512-37/toN4wwZErqohedXYqWgvcHUGlT8O/m2jVkAfAe9Bd4MzRqlBmXrJRePH0e9Wgnz2X7KymTgTOaaFizQe3AQ==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-opacity-percentage@2.0.0(postcss@8.4.47):
    resolution: {integrity: sha512-lyDrCOtntq5Y1JZpBFzIWm2wG9kbEdujpNt4NLannF+J9c8CgFIzPa80YQfdza+Y+yFfzbYj/rfoOsYsooUWTQ==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.2
    dependencies:
      postcss: 8.4.47
    dev: false

  /postcss-ordered-values@5.1.3(postcss@8.4.47):
    resolution: {integrity: sha512-9UO79VUhPwEkzbb3RNpqqghc6lcYej1aveQteWY+4POIwlqkYE21HKWaLDF6lWNuqCobEAyTovVhtI32Rbv2RQ==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      cssnano-utils: 3.1.0(postcss@8.4.47)
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-ordered-values@7.0.1(postcss@8.4.47):
    resolution: {integrity: sha512-irWScWRL6nRzYmBOXReIKch75RRhNS86UPUAxXdmW/l0FcAsg0lvAXQCby/1lymxn/o0gVa6Rv/0f03eJOwHxw==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      cssnano-utils: 5.0.0(postcss@8.4.47)
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-overflow-shorthand@5.0.1(postcss@8.4.47):
    resolution: {integrity: sha512-XzjBYKLd1t6vHsaokMV9URBt2EwC9a7nDhpQpjoPk2HRTSQfokPfyAS/Q7AOrzUu6q+vp/GnrDBGuj/FCaRqrQ==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-page-break@3.0.4(postcss@8.4.47):
    resolution: {integrity: sha512-1JGu8oCjVXLa9q9rFTo4MbeeA5FMe00/9C7lN4va606Rdb+HkxXtXsmEDrIraQ11fGz/WvKWa8gMuCKkrXpTsQ==}
    peerDependencies:
      postcss: ^8
    dependencies:
      postcss: 8.4.47
    dev: false

  /postcss-place@9.0.1(postcss@8.4.47):
    resolution: {integrity: sha512-JfL+paQOgRQRMoYFc2f73pGuG/Aw3tt4vYMR6UA3cWVMxivviPTnMFnFTczUJOA4K2Zga6xgQVE+PcLs64WC8Q==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-preset-env@9.6.0(postcss@8.4.47):
    resolution: {integrity: sha512-Lxfk4RYjUdwPCYkc321QMdgtdCP34AeI94z+/8kVmqnTIlD4bMRQeGcMZgwz8BxHrzQiFXYIR5d7k/9JMs2MEA==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      '@csstools/postcss-cascade-layers': 4.0.6(postcss@8.4.47)
      '@csstools/postcss-color-function': 3.0.19(postcss@8.4.47)
      '@csstools/postcss-color-mix-function': 2.0.19(postcss@8.4.47)
      '@csstools/postcss-content-alt-text': 1.0.0(postcss@8.4.47)
      '@csstools/postcss-exponential-functions': 1.0.9(postcss@8.4.47)
      '@csstools/postcss-font-format-keywords': 3.0.2(postcss@8.4.47)
      '@csstools/postcss-gamut-mapping': 1.0.11(postcss@8.4.47)
      '@csstools/postcss-gradients-interpolation-method': 4.0.20(postcss@8.4.47)
      '@csstools/postcss-hwb-function': 3.0.18(postcss@8.4.47)
      '@csstools/postcss-ic-unit': 3.0.7(postcss@8.4.47)
      '@csstools/postcss-initial': 1.0.1(postcss@8.4.47)
      '@csstools/postcss-is-pseudo-class': 4.0.8(postcss@8.4.47)
      '@csstools/postcss-light-dark-function': 1.0.8(postcss@8.4.47)
      '@csstools/postcss-logical-float-and-clear': 2.0.1(postcss@8.4.47)
      '@csstools/postcss-logical-overflow': 1.0.1(postcss@8.4.47)
      '@csstools/postcss-logical-overscroll-behavior': 1.0.1(postcss@8.4.47)
      '@csstools/postcss-logical-resize': 2.0.1(postcss@8.4.47)
      '@csstools/postcss-logical-viewport-units': 2.0.11(postcss@8.4.47)
      '@csstools/postcss-media-minmax': 1.1.8(postcss@8.4.47)
      '@csstools/postcss-media-queries-aspect-ratio-number-values': 2.0.11(postcss@8.4.47)
      '@csstools/postcss-nested-calc': 3.0.2(postcss@8.4.47)
      '@csstools/postcss-normalize-display-values': 3.0.2(postcss@8.4.47)
      '@csstools/postcss-oklab-function': 3.0.19(postcss@8.4.47)
      '@csstools/postcss-progressive-custom-properties': 3.3.0(postcss@8.4.47)
      '@csstools/postcss-relative-color-syntax': 2.0.19(postcss@8.4.47)
      '@csstools/postcss-scope-pseudo-class': 3.0.1(postcss@8.4.47)
      '@csstools/postcss-stepped-value-functions': 3.0.10(postcss@8.4.47)
      '@csstools/postcss-text-decoration-shorthand': 3.0.7(postcss@8.4.47)
      '@csstools/postcss-trigonometric-functions': 3.0.10(postcss@8.4.47)
      '@csstools/postcss-unset-value': 3.0.1(postcss@8.4.47)
      autoprefixer: 10.4.20(postcss@8.4.47)
      browserslist: 4.23.3
      css-blank-pseudo: 6.0.2(postcss@8.4.47)
      css-has-pseudo: 6.0.5(postcss@8.4.47)
      css-prefers-color-scheme: 9.0.1(postcss@8.4.47)
      cssdb: 8.1.1
      postcss: 8.4.47
      postcss-attribute-case-insensitive: 6.0.3(postcss@8.4.47)
      postcss-clamp: 4.1.0(postcss@8.4.47)
      postcss-color-functional-notation: 6.0.14(postcss@8.4.47)
      postcss-color-hex-alpha: 9.0.4(postcss@8.4.47)
      postcss-color-rebeccapurple: 9.0.3(postcss@8.4.47)
      postcss-custom-media: 10.0.8(postcss@8.4.47)
      postcss-custom-properties: 13.3.12(postcss@8.4.47)
      postcss-custom-selectors: 7.1.12(postcss@8.4.47)
      postcss-dir-pseudo-class: 8.0.1(postcss@8.4.47)
      postcss-double-position-gradients: 5.0.7(postcss@8.4.47)
      postcss-focus-visible: 9.0.1(postcss@8.4.47)
      postcss-focus-within: 8.0.1(postcss@8.4.47)
      postcss-font-variant: 5.0.0(postcss@8.4.47)
      postcss-gap-properties: 5.0.1(postcss@8.4.47)
      postcss-image-set-function: 6.0.3(postcss@8.4.47)
      postcss-lab-function: 6.0.19(postcss@8.4.47)
      postcss-logical: 7.0.1(postcss@8.4.47)
      postcss-nesting: 12.1.5(postcss@8.4.47)
      postcss-opacity-percentage: 2.0.0(postcss@8.4.47)
      postcss-overflow-shorthand: 5.0.1(postcss@8.4.47)
      postcss-page-break: 3.0.4(postcss@8.4.47)
      postcss-place: 9.0.1(postcss@8.4.47)
      postcss-pseudo-class-any-link: 9.0.2(postcss@8.4.47)
      postcss-replace-overflow-wrap: 4.0.0(postcss@8.4.47)
      postcss-selector-not: 7.0.2(postcss@8.4.47)
    dev: false

  /postcss-pseudo-class-any-link@9.0.2(postcss@8.4.47):
    resolution: {integrity: sha512-HFSsxIqQ9nA27ahyfH37cRWGk3SYyQLpk0LiWw/UGMV4VKT5YG2ONee4Pz/oFesnK0dn2AjcyequDbIjKJgB0g==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.47
      postcss-selector-parser: 6.1.2
    dev: false

  /postcss-reduce-initial@5.1.2(postcss@8.4.47):
    resolution: {integrity: sha512-dE/y2XRaqAi6OvjzD22pjTUQ8eOfc6m/natGHgKFBK9DxFmIm69YmaRVQrGgFlEfc1HePIurY0TmDeROK05rIg==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: 4.23.3
      caniuse-api: 3.0.0
      postcss: 8.4.47
    dev: false

  /postcss-reduce-initial@7.0.2(postcss@8.4.47):
    resolution: {integrity: sha512-pOnu9zqQww7dEKf62Nuju6JgsW2V0KRNBHxeKohU+JkHd/GAH5uvoObqFLqkeB2n20mr6yrlWDvo5UBU5GnkfA==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      browserslist: 4.23.3
      caniuse-api: 3.0.0
      postcss: 8.4.47
    dev: false

  /postcss-reduce-transforms@5.1.0(postcss@8.4.47):
    resolution: {integrity: sha512-2fbdbmgir5AvpW9RLtdONx1QoYG2/EtqpNQbFASDlixBbAYuTcJ0dECwlqNqH7VbaUnEnh8SrxOe2sRIn24XyQ==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-reduce-transforms@7.0.0(postcss@8.4.47):
    resolution: {integrity: sha512-pnt1HKKZ07/idH8cpATX/ujMbtOGhUfE+m8gbqwJE05aTaNw8gbo34a2e3if0xc0dlu75sUOiqvwCGY3fzOHew==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
    dev: false

  /postcss-replace-overflow-wrap@4.0.0(postcss@8.4.47):
    resolution: {integrity: sha512-KmF7SBPphT4gPPcKZc7aDkweHiKEEO8cla/GjcBK+ckKxiZslIu3C4GCRW3DNfL0o7yW7kMQu9xlZ1kXRXLXtw==}
    peerDependencies:
      postcss: ^8.0.3
    dependencies:
      postcss: 8.4.47
    dev: false

  /postcss-selector-not@7.0.2(postcss@8.4.47):
    resolution: {integrity: sha512-/SSxf/90Obye49VZIfc0ls4H0P6i6V1iHv0pzZH8SdgvZOPFkF37ef1r5cyWcMflJSFJ5bfuoluTnFnBBFiuSA==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss: ^8.4
    dependencies:
      postcss: 8.4.47
      postcss-selector-parser: 6.1.2
    dev: false

  /postcss-selector-parser@6.1.2:
    resolution: {integrity: sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==}
    engines: {node: '>=4'}
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2
    dev: false

  /postcss-svgo@5.1.0(postcss@8.4.47):
    resolution: {integrity: sha512-D75KsH1zm5ZrHyxPakAxJWtkyXew5qwS70v56exwvw542d9CRtTo78K0WeFxZB4G7JXKKMbEZtZayTGdIky/eA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
      svgo: 2.8.0
    dev: false

  /postcss-svgo@7.0.1(postcss@8.4.47):
    resolution: {integrity: sha512-0WBUlSL4lhD9rA5k1e5D8EN5wCEyZD6HJk0jIvRxl+FDVOMlJ7DePHYWGGVc5QRqrJ3/06FTXM0bxjmJpmTPSA==}
    engines: {node: ^18.12.0 || ^20.9.0 || >= 18}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
      svgo: 3.3.2
    dev: false

  /postcss-unique-selectors@5.1.1(postcss@8.4.47):
    resolution: {integrity: sha512-5JiODlELrz8L2HwxfPnhOWZYWDxVHWL83ufOv84NrcgipI7TaeRsatAhK4Tr2/ZiYldpK/wBvw5BD3qfaK96GA==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: 8.4.47
      postcss-selector-parser: 6.1.2
    dev: false

  /postcss-unique-selectors@7.0.3(postcss@8.4.47):
    resolution: {integrity: sha512-J+58u5Ic5T1QjP/LDV9g3Cx4CNOgB5vz+kM6+OxHHhFACdcDeKhBXjQmB7fnIZM12YSTvsL0Opwco83DmacW2g==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      postcss: 8.4.47
      postcss-selector-parser: 6.1.2
    dev: false

  /postcss-url@10.1.3(postcss@8.4.47):
    resolution: {integrity: sha512-FUzyxfI5l2tKmXdYc6VTu3TWZsInayEKPbiyW+P6vmmIrrb4I6CGX0BFoewgYHLK+oIL5FECEK02REYRpBvUCw==}
    engines: {node: '>=10'}
    peerDependencies:
      postcss: ^8.0.0
    dependencies:
      make-dir: 3.1.0
      mime: 2.5.2
      minimatch: 3.0.8
      postcss: 8.4.47
      xxhashjs: 0.2.2
    dev: false

  /postcss-value-parser@4.2.0:
    resolution: {integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==}
    dev: false

  /postcss@7.0.39:
    resolution: {integrity: sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==}
    engines: {node: '>=6.0.0'}
    dependencies:
      picocolors: 0.2.1
      source-map: 0.6.1
    dev: false

  /postcss@8.4.47:
    resolution: {integrity: sha512-56rxCq7G/XfB4EkXq9Egn5GCqugWvDFjafDOThIdMBsI15iqPqR5r15TfSr1YPYeEI19YeaXMCbY6u88Y76GLQ==}
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: 3.3.7
      picocolors: 1.1.0
      source-map-js: 1.2.1
    dev: false

  /prepend-http@1.0.4:
    resolution: {integrity: sha512-PhmXi5XmoyKw1Un4E+opM2KcsJInDvKyuOumcjjw3waw86ZNjHwVUOOWLc4bCzLdcKNaWBH9e99sbWzDQsVaYg==}
    engines: {node: '>=0.10.0'}
    dev: false

  /prettier@2.8.8:
    resolution: {integrity: sha512-tdN8qQGvNjw4CHbY+XXk0JgCXn9QiF21a55rBe5LJAU+kDyC4WQn4+awm2Xfk2lQMk5fKup9XgzTZtGkjBdP9Q==}
    engines: {node: '>=10.13.0'}
    hasBin: true
    requiresBuild: true
    dev: false
    optional: true

  /pretty-bytes@5.6.0:
    resolution: {integrity: sha512-FFw039TmrBqFK8ma/7OL3sDz/VytdtJr044/QUJtH0wK9lb9jLq9tJyIxUwtQJHwar2BqtiA4iCWSwo9JLkzFg==}
    engines: {node: '>=6'}
    dev: false

  /pretty-error@2.1.2:
    resolution: {integrity: sha512-EY5oDzmsX5wvuynAByrmY0P0hcp+QpnAKbJng2A2MPjVKXCxrDSUkzghVJ4ZGPIv+JC4gX8fPUWscC0RtjsWGw==}
    dependencies:
      lodash: 4.17.21
      renderkid: 2.0.7
    dev: false

  /pretty-time@1.1.0:
    resolution: {integrity: sha512-28iF6xPQrP8Oa6uxE6a1biz+lWeTOAPKggvjB8HAs6nVMKZwf5bG++632Dx614hIWgUPkgivRfG+a8uAXGTIbA==}
    engines: {node: '>=4'}
    dev: false

  /printj@1.1.2:
    resolution: {integrity: sha512-zA2SmoLaxZyArQTOPj5LXecR+RagfPSU5Kw1qP+jkWeNlrq+eJZyY2oS68SU1Z/7/myXM4lo9716laOFAVStCQ==}
    engines: {node: '>=0.8'}
    hasBin: true
    dev: false

  /process-nextick-args@2.0.1:
    resolution: {integrity: sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==}
    dev: false

  /process@0.11.10:
    resolution: {integrity: sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==}
    engines: {node: '>= 0.6.0'}
    dev: false

  /promise-inflight@1.0.1(bluebird@3.7.2):
    resolution: {integrity: sha512-6zWPyEOFaQBJYcGMHBKTKJ3u6TBsnMFOIZSa6ce1e/ZrrsOlnHRHbabMjLiBYKp+n44X9eUI6VUPaukCXHuG4g==}
    peerDependencies:
      bluebird: '*'
    peerDependenciesMeta:
      bluebird:
        optional: true
    dependencies:
      bluebird: 3.7.2
    dev: false

  /proper-lockfile@4.1.2:
    resolution: {integrity: sha512-TjNPblN4BwAWMXU8s9AEz4JmQxnD1NNL7bNOY/AKUzyamc379FWASUhc/K1pL2noVb+XmZKLL68cjzLsiOAMaA==}
    dependencies:
      graceful-fs: 4.2.11
      retry: 0.12.0
      signal-exit: 3.0.7
    dev: false

  /protocols@2.0.1:
    resolution: {integrity: sha512-/XJ368cyBJ7fzLMwLKv1e4vLxOju2MNAIokcr7meSaNcVbWz/CPcW22cP04mwxOErdA5mwjA8Q6w/cdAQxVn7Q==}
    dev: false

  /prr@1.0.1:
    resolution: {integrity: sha512-yPw4Sng1gWghHQWj0B3ZggWUm4qVbPwPFcRG8KyxiU7J2OHFSoEHKS+EZ3fv5l1t9CyCiop6l/ZYeWbrgoQejw==}
    requiresBuild: true
    dev: false

  /pseudomap@1.0.2:
    resolution: {integrity: sha512-b/YwNhb8lk1Zz2+bXXpS/LK9OisiZZ1SNsSLxN1x2OXVEhW2Ckr/7mWE5vrC1ZTiJlD9g19jWszTmJsB+oEpFQ==}
    dev: false

  /public-encrypt@4.0.3:
    resolution: {integrity: sha512-zVpa8oKZSz5bTMTFClc1fQOnyyEzpl5ozpi1B5YcvBrdohMjH2rfsBtyXcuNuwjsDIXmBYlF2N5FlJYhR29t8Q==}
    dependencies:
      bn.js: 4.12.0
      browserify-rsa: 4.1.0
      create-hash: 1.2.0
      parse-asn1: 5.1.7
      randombytes: 2.1.0
      safe-buffer: 5.2.1
    dev: false

  /pump@2.0.1:
    resolution: {integrity: sha512-ruPMNRkN3MHP1cWJc9OWr+T/xDP0jhXYCLfJcBuX54hhfIBnaQmAUMfDcG4DM5UMWByBbJY69QSphm3jtDKIkA==}
    dependencies:
      end-of-stream: 1.4.4
      once: 1.4.0
    dev: false

  /pump@3.0.2:
    resolution: {integrity: sha512-tUPXtzlGM8FE3P0ZL6DVs/3P58k9nk8/jZeQCurTJylQA8qFYzHFfhBJkuqyE0FifOsQ0uKWekiZ5g8wtr28cw==}
    dependencies:
      end-of-stream: 1.4.4
      once: 1.4.0
    dev: false

  /pumpify@1.5.1:
    resolution: {integrity: sha512-oClZI37HvuUJJxSKKrC17bZ9Cu0ZYhEAGPsPUy9KlMUmv9dKX2o77RUmq7f3XjIxbwyGwYzbzQ1L2Ks8sIradQ==}
    dependencies:
      duplexify: 3.7.1
      inherits: 2.0.4
      pump: 2.0.1
    dev: false

  /punycode@1.4.1:
    resolution: {integrity: sha512-jmYNElW7yvO7TV33CjSmvSiE2yco3bV2czu/OzDKdMNVZQWfxCblURLhf+47syQRBntjfLdd/H0egrzIG+oaFQ==}
    dev: false

  /punycode@2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==}
    engines: {node: '>=6'}
    dev: false

  /qrcode.vue@1.7.0(vue@2.7.16):
    resolution: {integrity: sha512-R7t6Y3fDDtcU7L4rtqwGUDP9xD64gJhIwpfjhRCTKmBoYF6SS49PIJHRJ048cse6OI7iwTwgyy2C46N9Ygoc6g==}
    peerDependencies:
      vue: ^2.0.0
    dependencies:
      vue: 2.7.16
    dev: false

  /qs@6.13.0:
    resolution: {integrity: sha512-+38qI9SOr8tfZ4QmJNplMUxqjbe7LKvvZgWdExBOmd+egZTtjLB67Gu0HRX3u/XOq7UU2Nx6nsjvS16Z9uwfpg==}
    engines: {node: '>=0.6'}
    dependencies:
      side-channel: 1.0.6
    dev: false

  /query-string@4.3.4:
    resolution: {integrity: sha512-O2XLNDBIg1DnTOa+2XrIwSiXEV8h2KImXUnjhhn2+UsvZ+Es2uyd5CCRTNQlDGbzUQOW3aYCBx9rVA6dzsiY7Q==}
    engines: {node: '>=0.10.0'}
    dependencies:
      object-assign: 4.1.1
      strict-uri-encode: 1.1.0
    dev: false

  /querystring-es3@0.2.1:
    resolution: {integrity: sha512-773xhDQnZBMFobEiztv8LIl70ch5MSF/jUQVlhwFyBILqq96anmoctVIYz+ZRp0qbCKATTn6ev02M3r7Ga5vqA==}
    engines: {node: '>=0.4.x'}
    dev: false

  /queue-microtask@1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}
    dev: false

  /randombytes@2.1.0:
    resolution: {integrity: sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==}
    dependencies:
      safe-buffer: 5.2.1
    dev: false

  /randomfill@1.0.4:
    resolution: {integrity: sha512-87lcbR8+MhcWcUiQ+9e+Rwx8MyR2P7qnt15ynUlbm3TU/fjbgz4GsvfSUDTemtCCtVCqb4ZcEFlyPNTh9bBTLw==}
    dependencies:
      randombytes: 2.1.0
      safe-buffer: 5.2.1
    dev: false

  /range-parser@1.2.1:
    resolution: {integrity: sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==}
    engines: {node: '>= 0.6'}
    dev: false

  /rc9@2.1.2:
    resolution: {integrity: sha512-btXCnMmRIBINM2LDZoEmOogIZU7Qe7zn4BpomSKZ/ykbLObuBdvG+mFq11DL6fjH1DRwHhrlgtYWG96bJiC7Cg==}
    dependencies:
      defu: 6.1.4
      destr: 2.0.3
    dev: false

  /read-cache@1.0.0:
    resolution: {integrity: sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==}
    dependencies:
      pify: 2.3.0
    dev: false

  /readable-stream@2.3.8:
    resolution: {integrity: sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==}
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 1.0.0
      process-nextick-args: 2.0.1
      safe-buffer: 5.1.2
      string_decoder: 1.1.1
      util-deprecate: 1.0.2
    dev: false

  /readable-stream@3.6.2:
    resolution: {integrity: sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==}
    engines: {node: '>= 6'}
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2
    dev: false

  /readdirp@2.2.1:
    resolution: {integrity: sha512-1JU/8q+VgFZyxwrJ+SVIOsh+KywWGpds3NTqikiKpDMZWScmAYyKIgqkO+ARvNWJfXeXR1zxz7aHF4u4CyH6vQ==}
    engines: {node: '>=0.10'}
    requiresBuild: true
    dependencies:
      graceful-fs: 4.2.11
      micromatch: 3.1.10
      readable-stream: 2.3.8
    transitivePeerDependencies:
      - supports-color
    dev: false
    optional: true

  /readdirp@3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    engines: {node: '>=8.10.0'}
    dependencies:
      picomatch: 2.3.1
    dev: false

  /regenerate-unicode-properties@10.2.0:
    resolution: {integrity: sha512-DqHn3DwbmmPVzeKj9woBadqmXxLvQoQIwu7nopMc72ztvxVmVk2SBhSnx67zuye5TP+lJsb/TBQsjLKhnDf3MA==}
    engines: {node: '>=4'}
    dependencies:
      regenerate: 1.4.2
    dev: false

  /regenerate@1.4.2:
    resolution: {integrity: sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A==}
    dev: false

  /regenerator-runtime@0.11.1:
    resolution: {integrity: sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg==}
    dev: false

  /regenerator-runtime@0.14.1:
    resolution: {integrity: sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==}
    dev: false

  /regenerator-transform@0.15.2:
    resolution: {integrity: sha512-hfMp2BoF0qOk3uc5V20ALGDS2ddjQaLrdl7xrGXvAIow7qeWRM2VA2HuCHkUKk9slq3VwEwLNK3DFBqDfPGYtg==}
    dependencies:
      '@babel/runtime': 7.25.6
    dev: false

  /regex-not@1.0.2:
    resolution: {integrity: sha512-J6SDjUgDxQj5NusnOtdFxDwN/+HWykR8GELwctJ7mdqhcyy1xEc4SRFHUXvxTp661YaVKAjfRLZ9cCqS6tn32A==}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 3.0.2
      safe-regex: 1.1.0
    dev: false

  /regexp.prototype.flags@1.5.2:
    resolution: {integrity: sha512-NcDiDkTLuPR+++OCKB0nWafEmhg/Da8aUPLPMQbK+bxKKCm1/S5he+AqYa4PlMCVBalb4/yxIRub6qkEx5yJbw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-errors: 1.3.0
      set-function-name: 2.0.2
    dev: false

  /regexpu-core@5.3.2:
    resolution: {integrity: sha512-RAM5FlZz+Lhmo7db9L298p2vHP5ZywrVXmVXpmAD9GuL5MPH6t9ROw1iA/wfHkQ76Qe7AaPF0nGuim96/IrQMQ==}
    engines: {node: '>=4'}
    dependencies:
      '@babel/regjsgen': 0.8.0
      regenerate: 1.4.2
      regenerate-unicode-properties: 10.2.0
      regjsparser: 0.9.1
      unicode-match-property-ecmascript: 2.0.0
      unicode-match-property-value-ecmascript: 2.2.0
    dev: false

  /regjsparser@0.9.1:
    resolution: {integrity: sha512-dQUtn90WanSNl+7mQKcXAgZxvUe7Z0SqXlgzv0za4LwiUhyzBC58yQO3liFoUgu8GiJVInAhJjkj1N0EtQ5nkQ==}
    hasBin: true
    dependencies:
      jsesc: 0.5.0
    dev: false

  /relateurl@0.2.7:
    resolution: {integrity: sha512-G08Dxvm4iDN3MLM0EsP62EDV9IuhXPR6blNz6Utcp7zyV3tr4HVNINt6MpaRWbxoOHT3Q7YN2P+jaHX8vUbgog==}
    engines: {node: '>= 0.10'}
    dev: false

  /remove-trailing-separator@1.1.0:
    resolution: {integrity: sha512-/hS+Y0u3aOfIETiaiirUFwDBDzmXPvO+jAfKTitUngIPzdKc6Z0LoFjM/CK5PL4C+eKwHohlHAb6H0VFfmmUsw==}
    requiresBuild: true
    dev: false
    optional: true

  /renderkid@2.0.7:
    resolution: {integrity: sha512-oCcFyxaMrKsKcTY59qnCAtmDVSLfPbrv6A3tVbPdFMMrv5jaK10V6m40cKsoPNhAqN6rmHW9sswW4o3ruSrwUQ==}
    dependencies:
      css-select: 4.3.0
      dom-converter: 0.2.0
      htmlparser2: 6.1.0
      lodash: 4.17.21
      strip-ansi: 3.0.1
    dev: false

  /repeat-element@1.1.4:
    resolution: {integrity: sha512-LFiNfRcSu7KK3evMyYOuCzv3L10TW7yC1G2/+StMjK8Y6Vqd2MG7r/Qjw4ghtuCOjFvlnms/iMmLqpvW/ES/WQ==}
    engines: {node: '>=0.10.0'}
    dev: false

  /repeat-string@1.6.1:
    resolution: {integrity: sha512-PV0dzCYDNfRi1jCDbJzpW7jNNDRuCOG/jI5ctQcGKt/clZD+YcPS3yIlWuTJMmESC8aevCFmWJy5wjAFgNqN6w==}
    engines: {node: '>=0.10'}
    dev: false

  /require-from-string@2.0.2:
    resolution: {integrity: sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==}
    engines: {node: '>=0.10.0'}
    dev: false

  /requires-port@1.0.0:
    resolution: {integrity: sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==}
    dev: false

  /resize-observer-polyfill@1.5.1:
    resolution: {integrity: sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==}
    dev: false

  /resolve-from@4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}
    dev: false

  /resolve-url@0.2.1:
    resolution: {integrity: sha512-ZuF55hVUQaaczgOIwqWzkEcEidmlD/xl44x1UZnhOXcYuFN2S6+rcxpG+C1N3So0wvNI3DmJICUFfu2SxhBmvg==}
    deprecated: https://github.com/lydell/resolve-url#deprecated
    dev: false

  /resolve@1.22.8:
    resolution: {integrity: sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==}
    hasBin: true
    dependencies:
      is-core-module: 2.15.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0
    dev: false

  /restore-cursor@3.1.0:
    resolution: {integrity: sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==}
    engines: {node: '>=8'}
    dependencies:
      onetime: 5.1.2
      signal-exit: 3.0.7
    dev: false

  /ret@0.1.15:
    resolution: {integrity: sha512-TTlYpa+OL+vMMNG24xSlQGEJ3B/RzEfUlLct7b5G/ytav+wPrplCpVMFuwzXbkecJrb6IYo1iFb0S9v37754mg==}
    engines: {node: '>=0.12'}
    dev: false

  /retry@0.12.0:
    resolution: {integrity: sha512-9LkiTwjUh6rT555DtE9rTX+BKByPfrMzEAtnlEtdEwr3Nkffwiihqe2bWADg+OQRjt9gl6ICdmB/ZFDCGAtSow==}
    engines: {node: '>= 4'}
    dev: false

  /reusify@1.0.4:
    resolution: {integrity: sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}
    dev: false

  /rimraf@2.7.1:
    resolution: {integrity: sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w==}
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true
    dependencies:
      glob: 7.2.3
    dev: false

  /rimraf@3.0.2:
    resolution: {integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==}
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true
    dependencies:
      glob: 7.2.3
    dev: false

  /ripemd160@2.0.2:
    resolution: {integrity: sha512-ii4iagi25WusVoiC4B4lq7pbXfAp3D9v5CwfkY33vffw2+pkDjY1D8GaN7spsxvCSx8dkPqOZCEZyfxcmJG2IA==}
    dependencies:
      hash-base: 3.1.0
      inherits: 2.0.4
    dev: false

  /run-async@2.4.1:
    resolution: {integrity: sha512-tvVnVv01b8c1RrA6Ep7JkStj85Guv/YrMcwqYQnwjsAS2cTmmPGBBjAjpCW7RrSodNSoE2/qg9O4bceNvUuDgQ==}
    engines: {node: '>=0.12.0'}
    dev: false

  /run-parallel@1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}
    dependencies:
      queue-microtask: 1.2.3
    dev: false

  /run-queue@1.0.3:
    resolution: {integrity: sha512-ntymy489o0/QQplUDnpYAYUsO50K9SBrIVaKCWDOJzYJts0f9WH9RFJkyagebkw5+y1oi00R7ynNW/d12GBumg==}
    dependencies:
      aproba: 1.2.0
    dev: false

  /rxjs@6.6.7:
    resolution: {integrity: sha512-hTdwr+7yYNIT5n4AMYp85KA6yw2Va0FLa3Rguvbpa4W3I5xynaBZo41cM3XM+4Q6fRMj3sBYIR1VAmZMXYJvRQ==}
    engines: {npm: '>=2.0.0'}
    dependencies:
      tslib: 1.14.1
    dev: false

  /safe-array-concat@1.1.2:
    resolution: {integrity: sha512-vj6RsCsWBCf19jIeHEfkRMw8DPiBb+DMXklQ/1SGDHOMlHdPUkZXFQ2YdplS23zESTijAcurb1aSgJA3AgMu1Q==}
    engines: {node: '>=0.4'}
    dependencies:
      call-bind: 1.0.7
      get-intrinsic: 1.2.4
      has-symbols: 1.0.3
      isarray: 2.0.5
    dev: false

  /safe-buffer@5.1.2:
    resolution: {integrity: sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==}
    dev: false

  /safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}
    dev: false

  /safe-regex-test@1.0.3:
    resolution: {integrity: sha512-CdASjNJPvRa7roO6Ra/gLYBTzYzzPyyBXxIMdGW3USQLyjWEls2RgW5UBTXaQVp+OrpeCK3bLem8smtmheoRuw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-regex: 1.1.4
    dev: false

  /safe-regex@1.1.0:
    resolution: {integrity: sha512-aJXcif4xnaNUzvUuC5gcb46oTS7zvg4jpMTnuqtrEPlR3vFr4pxtdTwaF1Qs3Enjn9HK+ZlwQui+a7z0SywIzg==}
    dependencies:
      ret: 0.1.15
    dev: false

  /safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}
    dev: false

  /schema-utils@1.0.0:
    resolution: {integrity: sha512-i27Mic4KovM/lnGsy8whRCHhc7VicJajAjTrYg11K9zfZXnYIt4k5F+kZkwjnrhKzLic/HLU4j11mjsz2G/75g==}
    engines: {node: '>= 4'}
    dependencies:
      ajv: 6.12.6
      ajv-errors: 1.0.1(ajv@6.12.6)
      ajv-keywords: 3.5.2(ajv@6.12.6)
    dev: false

  /schema-utils@2.7.1:
    resolution: {integrity: sha512-SHiNtMOUGWBQJwzISiVYKu82GiV4QYGePp3odlY1tuKO7gPtphAT5R/py0fA6xtbgLL/RvtJZnU9b8s0F1q0Xg==}
    engines: {node: '>= 8.9.0'}
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      ajv-keywords: 3.5.2(ajv@6.12.6)
    dev: false

  /schema-utils@3.3.0:
    resolution: {integrity: sha512-pN/yOAvcC+5rQ5nERGuwrjLlYvLTbCibnZ1I7B1LaiAz9BRBlE9GMgE/eqV30P7aJQUf7Ddimy/RsbYO/GrVGg==}
    engines: {node: '>= 10.13.0'}
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      ajv-keywords: 3.5.2(ajv@6.12.6)
    dev: false

  /schema-utils@4.2.0:
    resolution: {integrity: sha512-L0jRsrPpjdckP3oPug3/VxNKt2trR8TcabrM6FOAAlvC/9Phcmm+cuAgTlxBqdBR1WJx7Naj9WHw+aOmheSVbw==}
    engines: {node: '>= 12.13.0'}
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 8.17.1
      ajv-formats: 2.1.1(ajv@8.17.1)
      ajv-keywords: 5.1.0(ajv@8.17.1)
    dev: false

  /scule@0.2.1:
    resolution: {integrity: sha512-M9gnWtn3J0W+UhJOHmBxBTwv8mZCan5i1Himp60t6vvZcor0wr+IM0URKmIglsWJ7bRujNAVVN77fp+uZaWoKg==}
    dev: false

  /select@1.1.2:
    resolution: {integrity: sha512-OwpTSOfy6xSs1+pwcNrv0RBMOzI39Lp3qQKUTPVVPRjCdNa5JH/oPRiqsesIskK8TVgmRiHwO4KXlV2Li9dANA==}
    dev: false

  /semver@5.7.2:
    resolution: {integrity: sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==}
    hasBin: true
    dev: false

  /semver@6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==}
    hasBin: true
    dev: false

  /semver@7.6.3:
    resolution: {integrity: sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==}
    engines: {node: '>=10'}
    hasBin: true
    dev: false

  /send@0.19.0:
    resolution: {integrity: sha512-dW41u5VfLXu8SJh5bwRmyYUbAoSB3c9uQh6L8h/KtsFREPWpbX1lrljJo186Jc4nmci/sGUZ9a0a0J2zgfq2hw==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      fresh: 0.5.2
      http-errors: 2.0.0
      mime: 1.6.0
      ms: 2.1.3
      on-finished: 2.4.1
      range-parser: 1.2.1
      statuses: 2.0.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /serialize-javascript@4.0.0:
    resolution: {integrity: sha512-GaNA54380uFefWghODBWEGisLZFj00nS5ACs6yHa9nLqlLpVLO8ChDGeKRjZnV4Nh4n0Qi7nhYZD/9fCPzEqkw==}
    dependencies:
      randombytes: 2.1.0
    dev: false

  /serialize-javascript@5.0.1:
    resolution: {integrity: sha512-SaaNal9imEO737H2c05Og0/8LUXG7EnsZyMa8MzkmuHoELfT6txuj0cMqRj6zfPKnmQ1yasR4PCJc8x+M4JSPA==}
    dependencies:
      randombytes: 2.1.0
    dev: false

  /serialize-javascript@6.0.2:
    resolution: {integrity: sha512-Saa1xPByTTq2gdeFZYLLo+RFE35NHZkAbqZeWNd3BpzppeVisAqpDjcp8dyf6uIvEqJRd46jemmyA4iFIeVk8g==}
    dependencies:
      randombytes: 2.1.0
    dev: false

  /serve-placeholder@2.0.2:
    resolution: {integrity: sha512-/TMG8SboeiQbZJWRlfTCqMs2DD3SZgWp0kDQePz9yUuCnDfDh/92gf7/PxGhzXTKBIPASIHxFcZndoNbp6QOLQ==}
    dependencies:
      defu: 6.1.4
    dev: false

  /serve-static@1.16.2:
    resolution: {integrity: sha512-VqpjJZKadQB/PEbEwvFdO43Ax5dFBZ2UECszz8bQ7pi7wt//PWe1P6MN7eCnjsatYtBT6EuiClbjSWP2WrIoTw==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      encodeurl: 2.0.0
      escape-html: 1.0.3
      parseurl: 1.3.3
      send: 0.19.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /server-destroy@1.0.1:
    resolution: {integrity: sha512-rb+9B5YBIEzYcD6x2VKidaa+cqYBJQKnU4oe4E3ANwRRN56yk/ua1YCJT1n21NTS8w6CcOclAKNP3PhdCXKYtQ==}
    dev: false

  /set-function-length@1.2.2:
    resolution: {integrity: sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      gopd: 1.0.1
      has-property-descriptors: 1.0.2
    dev: false

  /set-function-name@2.0.2:
    resolution: {integrity: sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      functions-have-names: 1.2.3
      has-property-descriptors: 1.0.2
    dev: false

  /set-value@2.0.1:
    resolution: {integrity: sha512-JxHc1weCN68wRY0fhCoXpyK55m/XPHafOmK4UWD7m2CI14GMcFypt4w/0+NV5f/ZMby2F6S2wwA7fgynh9gWSw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 2.0.1
      is-extendable: 0.1.1
      is-plain-object: 2.0.4
      split-string: 3.1.0
    dev: false

  /set-value@3.0.3:
    resolution: {integrity: sha512-Xsn/XSatoVOGBbp5hs3UylFDs5Bi9i+ArpVJKdHPniZHoEgRniXTqHWrWrGQ0PbEClVT6WtfnBwR8CAHC9sveg==}
    engines: {node: '>=6.0'}
    dependencies:
      is-plain-object: 2.0.4
    dev: false

  /setimmediate@1.0.5:
    resolution: {integrity: sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA==}
    dev: false

  /setprototypeof@1.2.0:
    resolution: {integrity: sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==}
    dev: false

  /sha.js@2.4.11:
    resolution: {integrity: sha512-QMEp5B7cftE7APOjk5Y6xgrbWu+WkLVQwk8JNjZ8nKRciZaByEW6MubieAiToS7+dwvrjGhH8jRXz3MVd0AYqQ==}
    hasBin: true
    dependencies:
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: false

  /shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}
    dependencies:
      shebang-regex: 3.0.0
    dev: false

  /shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}
    dev: false

  /shell-quote@1.8.1:
    resolution: {integrity: sha512-6j1W9l1iAs/4xYBI1SYOVZyFcCis9b4KCLQ8fgAGG07QvzaRLVVRQvAy85yNmmZSjYjg4MWh4gNvlPujU/5LpA==}
    dev: false

  /shvl@2.0.3:
    resolution: {integrity: sha512-V7C6S9Hlol6SzOJPnQ7qzOVEWUQImt3BNmmzh40wObhla3XOYMe4gGiYzLrJd5TFa+cI2f9LKIRJTTKZSTbWgw==}
    deprecated: older versions vulnerable to prototype pollution
    dev: false

  /side-channel@1.0.6:
    resolution: {integrity: sha512-fDW/EZ6Q9RiO8eFG8Hj+7u/oW+XrPTIChwCOM2+th2A6OblDtYYIpve9m+KvI9Z4C9qSEXlaGR6bTEYHReuglA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      get-intrinsic: 1.2.4
      object-inspect: 1.13.2
    dev: false

  /signal-exit@3.0.7:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==}
    dev: false

  /signal-exit@4.1.0:
    resolution: {integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==}
    engines: {node: '>=14'}
    dev: false

  /simple-swizzle@0.2.2:
    resolution: {integrity: sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==}
    dependencies:
      is-arrayish: 0.3.2
    dev: false

  /sirv@2.0.4:
    resolution: {integrity: sha512-94Bdh3cC2PKrbgSOUqTiGPWVZeSiXfKOVZNJniWoqrWrRkB1CJzBU3NEbiTsPcYy1lDsANA/THzS+9WBiy5nfQ==}
    engines: {node: '>= 10'}
    dependencies:
      '@polka/url': 1.0.0-next.28
      mrmime: 2.0.0
      totalist: 3.0.1
    dev: false

  /slash@3.0.0:
    resolution: {integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==}
    engines: {node: '>=8'}
    dev: false

  /snapdragon-node@2.1.1:
    resolution: {integrity: sha512-O27l4xaMYt/RSQ5TR3vpWCAB5Kb/czIcqUFOM/C4fYcLnbZUc1PkjTAMjof2pBWaSTwOUd6qUHcFGVGj7aIwnw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      define-property: 1.0.0
      isobject: 3.0.1
      snapdragon-util: 3.0.1
    dev: false

  /snapdragon-util@3.0.1:
    resolution: {integrity: sha512-mbKkMdQKsjX4BAL4bRYTj21edOf8cN7XHdYUJEe+Zn99hVEYcMvKPct1IqNe7+AZPirn8BCDOQBHQZknqmKlZQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2
    dev: false

  /snapdragon@0.8.2:
    resolution: {integrity: sha512-FtyOnWN/wCHTVXOMwvSv26d+ko5vWlIDD6zoUJ7LW8vh+ZBC8QdljveRP+crNrtBwioEUWy/4dMtbBjA4ioNlg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      base: 0.11.2
      debug: 2.6.9
      define-property: 0.2.5
      extend-shallow: 2.0.1
      map-cache: 0.2.2
      source-map: 0.5.7
      source-map-resolve: 0.5.3
      use: 3.1.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /sort-keys@1.1.2:
    resolution: {integrity: sha512-vzn8aSqKgytVik0iwdBEi+zevbTYZogewTUM6dtpmGwEcdzbub/TX4bCzRhebDCRC3QzXgJsLRKB2V/Oof7HXg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-plain-obj: 1.1.0
    dev: false

  /sort-keys@2.0.0:
    resolution: {integrity: sha512-/dPCrG1s3ePpWm6yBbxZq5Be1dXGLyLn9Z791chDC3NFrpkVbWGzkBwPN1knaciexFXgRJ7hzdnwZ4stHSDmjg==}
    engines: {node: '>=4'}
    dependencies:
      is-plain-obj: 1.1.0
    dev: false

  /sortablejs@1.10.2:
    resolution: {integrity: sha512-YkPGufevysvfwn5rfdlGyrGjt7/CRHwvRPogD/lC+TnvcN29jDpCifKP+rBqf+LRldfXSTh+0CGLcSg0VIxq3A==}
    dev: false

  /sortablejs@1.15.3:
    resolution: {integrity: sha512-zdK3/kwwAK1cJgy1rwl1YtNTbRmc8qW/+vgXf75A7NHag5of4pyI6uK86ktmQETyWRH7IGaE73uZOOBcGxgqZg==}
    dev: false

  /source-list-map@2.0.1:
    resolution: {integrity: sha512-qnQ7gVMxGNxsiL4lEuJwe/To8UnK7fAnmbGEEH8RpLouuKbeEm0lhbQVFIrNSuB+G7tVrAlVsZgETT5nljf+Iw==}
    dev: false

  /source-map-js@1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /source-map-resolve@0.5.3:
    resolution: {integrity: sha512-Htz+RnsXWk5+P2slx5Jh3Q66vhQj1Cllm0zvnaY98+NFx+Dv2CF/f5O/t8x+KaNdrdIAsruNzoh/KpialbqAnw==}
    deprecated: See https://github.com/lydell/source-map-resolve#deprecated
    dependencies:
      atob: 2.1.2
      decode-uri-component: 0.2.2
      resolve-url: 0.2.1
      source-map-url: 0.4.1
      urix: 0.1.0
    dev: false

  /source-map-support@0.5.21:
    resolution: {integrity: sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==}
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1
    dev: false

  /source-map-url@0.4.1:
    resolution: {integrity: sha512-cPiFOTLUKvJFIg4SKVScy4ilPPW6rFgMgfuZJPNoDuMs3nC1HbMUycBoJw77xFIp6z1UJQJOfx6C9GMH80DiTw==}
    deprecated: See https://github.com/lydell/source-map-url#deprecated
    dev: false

  /source-map@0.5.6:
    resolution: {integrity: sha512-MjZkVp0NHr5+TPihLcadqnlVoGIoWo4IBHptutGh9wI3ttUYvCG26HkSuDi+K6lsZ25syXJXcctwgyVCt//xqA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /source-map@0.5.7:
    resolution: {integrity: sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==}
    engines: {node: '>=0.10.0'}
    dev: false

  /source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}
    dev: false

  /source-map@0.7.4:
    resolution: {integrity: sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA==}
    engines: {node: '>= 8'}
    dev: false

  /spark-md5@3.0.2:
    resolution: {integrity: sha512-wcFzz9cDfbuqe0FZzfi2or1sgyIrsDwmPwfZC4hiNidPdPINjeUwNfv5kldczoEAcjl9Y1L3SM7Uz2PUEQzxQw==}
    dev: false

  /split-string@3.1.0:
    resolution: {integrity: sha512-NzNVhJDYpwceVVii8/Hu6DKfD2G+NrQHlS/V/qgv763EYudVwEcMQNxd2lh+0VrUByXN/oJkl5grOhYWvQUYiw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 3.0.2
    dev: false

  /ssf@0.11.2:
    resolution: {integrity: sha512-+idbmIXoYET47hH+d7dfm2epdOMUDjqcB4648sTZ+t2JwoyBFL/insLfB/racrDmsKB3diwsDA696pZMieAC5g==}
    engines: {node: '>=0.8'}
    dependencies:
      frac: 1.1.2
    dev: false

  /ssri@6.0.2:
    resolution: {integrity: sha512-cepbSq/neFK7xB6A50KHN0xHDotYzq58wWCa5LeWqnPrHG8GzfEjO/4O8kpmcGW+oaxkvhEJCWgbgNk4/ZV93Q==}
    dependencies:
      figgy-pudding: 3.5.2
    dev: false

  /ssri@8.0.1:
    resolution: {integrity: sha512-97qShzy1AiyxvPNIkLWoGua7xoQzzPjQ0HAH4B0rWKo7SZ6USuPcrUiAFrws0UH8RrbWmgq3LMTObhPIHbbBeQ==}
    engines: {node: '>= 8'}
    dependencies:
      minipass: 3.3.6
    dev: false

  /stable@0.1.8:
    resolution: {integrity: sha512-ji9qxRnOVfcuLDySj9qzhGSEFVobyt1kIOSkj1qZzYLzq7Tos/oUUWvotUPQLlrsidqsK6tBH89Bc9kL5zHA6w==}
    deprecated: 'Modern JS already guarantees Array#sort() is a stable sort, so this library is deprecated. See the compatibility table on MDN: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort#browser_compatibility'
    dev: false

  /stack-trace@0.0.10:
    resolution: {integrity: sha512-KGzahc7puUKkzyMt+IqAep+TVNbKP+k2Lmwhub39m1AsTSkaDutx56aDCo+HLDzf/D26BIHTJWNiTG1KAJiQCg==}
    dev: false

  /stackframe@1.3.4:
    resolution: {integrity: sha512-oeVtt7eWQS+Na6F//S4kJ2K2VbRlS9D43mAlMyVpVWovy9o+jfgH8O9agzANzaiLjclA0oYzUXEM4PurhSUChw==}
    dev: false

  /static-extend@0.1.2:
    resolution: {integrity: sha512-72E9+uLc27Mt718pMHt9VMNiAL4LMsmDbBva8mxWUCkT07fSzEGMYUCk0XWY6lp0j6RBAG4cJ3mWuZv2OE3s0g==}
    engines: {node: '>=0.10.0'}
    dependencies:
      define-property: 0.2.5
      object-copy: 0.1.0
    dev: false

  /statuses@1.5.0:
    resolution: {integrity: sha512-OpZ3zP+jT1PI7I8nemJX4AKmAX070ZkYPVWV/AaKTJl+tXCTGyVdC1a4SL8RUQYEwk/f34ZX8UTykN68FwrqAA==}
    engines: {node: '>= 0.6'}
    dev: false

  /statuses@2.0.1:
    resolution: {integrity: sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==}
    engines: {node: '>= 0.8'}
    dev: false

  /std-env@3.7.0:
    resolution: {integrity: sha512-JPbdCEQLj1w5GilpiHAx3qJvFndqybBysA3qUOnznweH4QbNYUsW/ea8QzSrnh0vNsezMMw5bcVool8lM0gwzg==}
    dev: false

  /stream-browserify@2.0.2:
    resolution: {integrity: sha512-nX6hmklHs/gr2FuxYDltq8fJA1GDlxKQCz8O/IM4atRqBH8OORmBNgfvW5gG10GT/qQ9u0CzIvr2X5Pkt6ntqg==}
    dependencies:
      inherits: 2.0.4
      readable-stream: 2.3.8
    dev: false

  /stream-each@1.2.3:
    resolution: {integrity: sha512-vlMC2f8I2u/bZGqkdfLQW/13Zihpej/7PmSiMQsbYddxuTsJp8vRe2x2FvVExZg7FaOds43ROAuFJwPR4MTZLw==}
    dependencies:
      end-of-stream: 1.4.4
      stream-shift: 1.0.3
    dev: false

  /stream-http@2.8.3:
    resolution: {integrity: sha512-+TSkfINHDo4J+ZobQLWiMouQYB+UVYFttRA94FpEzzJ7ZdqcL4uUUQ7WkdkI4DSozGmgBUE/a47L+38PenXhUw==}
    dependencies:
      builtin-status-codes: 3.0.0
      inherits: 2.0.4
      readable-stream: 2.3.8
      to-arraybuffer: 1.0.1
      xtend: 4.0.2
    dev: false

  /stream-shift@1.0.3:
    resolution: {integrity: sha512-76ORR0DO1o1hlKwTbi/DM3EXWGf3ZJYO8cXX5RJwnul2DEg2oyoZyjLNoQM8WsvZiFKCRfC1O0J7iCvie3RZmQ==}
    dev: false

  /strict-uri-encode@1.1.0:
    resolution: {integrity: sha512-R3f198pcvnB+5IpnBlRkphuE9n46WyVl8I39W/ZUTZLz4nqSP/oLYUrcnJrw462Ds8he4YKMov2efsTIw1BDGQ==}
    engines: {node: '>=0.10.0'}
    dev: false

  /string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1
    dev: false

  /string.prototype.trim@1.2.9:
    resolution: {integrity: sha512-klHuCNxiMZ8MlsOihJhJEBJAiMVqU3Z2nEXWfWnIqjN0gEFS9J9+IxKozWWtQGcgoa1WUZzLjKPTr4ZHNFTFxw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-object-atoms: 1.0.0
    dev: false

  /string.prototype.trimend@1.0.8:
    resolution: {integrity: sha512-p73uL5VCHCO2BZZ6krwwQE3kCzM7NKmis8S//xEC6fQonchbum4eP6kR4DLEjQFO3Wnj3Fuo8NM0kOSjVdHjZQ==}
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-object-atoms: 1.0.0
    dev: false

  /string.prototype.trimstart@1.0.8:
    resolution: {integrity: sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-object-atoms: 1.0.0
    dev: false

  /string_decoder@1.1.1:
    resolution: {integrity: sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==}
    dependencies:
      safe-buffer: 5.1.2
    dev: false

  /string_decoder@1.3.0:
    resolution: {integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==}
    dependencies:
      safe-buffer: 5.2.1
    dev: false

  /strip-ansi@3.0.1:
    resolution: {integrity: sha512-VhumSSbBqDTP8p2ZLKj40UjBCV4+v8bUSEpUb4KjRgWk9pbqGF4REFj6KEagidb2f/M6AzC0EmFyDNGaw9OCzg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-regex: 2.1.1
    dev: false

  /strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}
    dependencies:
      ansi-regex: 5.0.1
    dev: false

  /strip-final-newline@2.0.0:
    resolution: {integrity: sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==}
    engines: {node: '>=6'}
    dev: false

  /style-resources-loader@1.5.0(webpack@4.47.0):
    resolution: {integrity: sha512-fIfyvQ+uvXaCBGGAgfh+9v46ARQB1AWdaop2RpQw0PBVuROsTBqGvx8dj0kxwjGOAyq3vepe4AOK3M6+Q/q2jw==}
    engines: {node: '>=8.9'}
    peerDependencies:
      webpack: ^3.0.0 || ^4.0.0 || ^5.0.0
    dependencies:
      glob: 7.2.3
      loader-utils: 2.0.4
      schema-utils: 2.7.1
      tslib: 2.7.0
      webpack: 4.47.0
    dev: false

  /stylehacks@5.1.1(postcss@8.4.47):
    resolution: {integrity: sha512-sBpcd5Hx7G6seo7b1LkpttvTz7ikD0LlH5RmdcBNb6fFR0Fl7LQwHDFr300q4cwUqi+IYrFGmsIHieMBfnN/Bw==}
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: 4.23.3
      postcss: 8.4.47
      postcss-selector-parser: 6.1.2
    dev: false

  /stylehacks@7.0.4(postcss@8.4.47):
    resolution: {integrity: sha512-i4zfNrGMt9SB4xRK9L83rlsFCgdGANfeDAYacO1pkqcE7cRHPdWHwnKZVz7WY17Veq/FvyYsRAU++Ga+qDFIww==}
    engines: {node: ^18.12.0 || ^20.9.0 || >=22.0}
    peerDependencies:
      postcss: ^8.4.31
    dependencies:
      browserslist: 4.23.3
      postcss: 8.4.47
      postcss-selector-parser: 6.1.2
    dev: false

  /supports-color@5.5.0:
    resolution: {integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==}
    engines: {node: '>=4'}
    dependencies:
      has-flag: 3.0.0
    dev: false

  /supports-color@7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}
    dependencies:
      has-flag: 4.0.0
    dev: false

  /supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}
    dev: false

  /svg-tags@1.0.0:
    resolution: {integrity: sha512-ovssysQTa+luh7A5Weu3Rta6FJlFBBbInjOh722LIt6klpU2/HtdUbszju/G4devcvk8PGt7FCLv5wftu3THUA==}
    dev: false

  /svgo@2.8.0:
    resolution: {integrity: sha512-+N/Q9kV1+F+UeWYoSiULYo4xYSDQlTgb+ayMobAXPwMnLvop7oxKMo9OzIrX5x3eS4L4f2UHhc9axXwY8DpChg==}
    engines: {node: '>=10.13.0'}
    hasBin: true
    dependencies:
      '@trysound/sax': 0.2.0
      commander: 7.2.0
      css-select: 4.3.0
      css-tree: 1.1.3
      csso: 4.2.0
      picocolors: 1.1.0
      stable: 0.1.8
    dev: false

  /svgo@3.3.2:
    resolution: {integrity: sha512-OoohrmuUlBs8B8o6MB2Aevn+pRIH9zDALSR+6hhqVfa6fRwG/Qw9VUMSMW9VNg2CFc/MTIfabtdOVl9ODIJjpw==}
    engines: {node: '>=14.0.0'}
    hasBin: true
    dependencies:
      '@trysound/sax': 0.2.0
      commander: 7.2.0
      css-select: 5.1.0
      css-tree: 2.3.1
      css-what: 6.1.0
      csso: 5.0.5
      picocolors: 1.1.0
    dev: false

  /tapable@1.1.3:
    resolution: {integrity: sha512-4WK/bYZmj8xLr+HUCODHGF1ZFzsYffasLUgEiMBY4fgtltdO6B4WJtlSbPaDTLpYTcGVwM2qLnFTICEcNxs3kA==}
    engines: {node: '>=6'}
    dev: false

  /tar@6.2.1:
    resolution: {integrity: sha512-DZ4yORTwrbTj/7MZYq2w+/ZFdI6OZ/f9SFHR+71gIVUZhOQPHzVCLpvRnPgyaMpfWxxk/4ONva3GQSyNIKRv6A==}
    engines: {node: '>=10'}
    dependencies:
      chownr: 2.0.0
      fs-minipass: 2.1.0
      minipass: 5.0.0
      minizlib: 2.1.2
      mkdirp: 1.0.4
      yallist: 4.0.0
    dev: false

  /terser-webpack-plugin@1.4.6(webpack@4.47.0):
    resolution: {integrity: sha512-2lBVf/VMVIddjSn3GqbT90GvIJ/eYXJkt8cTzU7NbjKqK8fwv18Ftr4PlbF46b/e88743iZFL5Dtr/rC4hjIeA==}
    engines: {node: '>= 6.9.0'}
    peerDependencies:
      webpack: ^4.0.0
    dependencies:
      cacache: 12.0.4
      find-cache-dir: 2.1.0
      is-wsl: 1.1.0
      schema-utils: 1.0.0
      serialize-javascript: 4.0.0
      source-map: 0.6.1
      terser: 4.8.1
      webpack: 4.47.0
      webpack-sources: 1.4.3
      worker-farm: 1.7.0
    dev: false

  /terser-webpack-plugin@4.2.3(webpack@4.47.0):
    resolution: {integrity: sha512-jTgXh40RnvOrLQNgIkwEKnQ8rmHjHK4u+6UBEi+W+FPmvb+uo+chJXntKe7/3lW5mNysgSWD60KyesnhW8D6MQ==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      cacache: 15.3.0
      find-cache-dir: 3.3.2
      jest-worker: 26.6.2
      p-limit: 3.1.0
      schema-utils: 3.3.0
      serialize-javascript: 5.0.1
      source-map: 0.6.1
      terser: 5.33.0
      webpack: 4.47.0
      webpack-sources: 1.4.3
    transitivePeerDependencies:
      - bluebird
    dev: false

  /terser@4.8.1:
    resolution: {integrity: sha512-4GnLC0x667eJG0ewJTa6z/yXrbLGv80D9Ru6HIpCQmO+Q4PfEtBFi0ObSckqwL6VyQv/7ENJieXHo2ANmdQwgw==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      acorn: 8.12.1
      commander: 2.20.3
      source-map: 0.6.1
      source-map-support: 0.5.21
    dev: false

  /terser@5.33.0:
    resolution: {integrity: sha512-JuPVaB7s1gdFKPKTelwUyRq5Sid2A3Gko2S0PncwdBq7kN9Ti9HPWDQ06MPsEDGsZeVESjKEnyGy68quBk1w6g==}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      '@jridgewell/source-map': 0.3.6
      acorn: 8.12.1
      commander: 2.20.3
      source-map-support: 0.5.21
    dev: false

  /thingies@1.21.0(tslib@2.7.0):
    resolution: {integrity: sha512-hsqsJsFMsV+aD4s3CWKk85ep/3I9XzYV/IXaSouJMYIoDlgyi11cBhsqYe9/geRfB0YIikBQg6raRaM+nIMP9g==}
    engines: {node: '>=10.18'}
    peerDependencies:
      tslib: ^2
    dependencies:
      tslib: 2.7.0
    dev: false

  /thread-loader@3.0.4(webpack@4.47.0):
    resolution: {integrity: sha512-ByaL2TPb+m6yArpqQUZvP+5S1mZtXsEP7nWKKlAUTm7fCml8kB5s1uI3+eHRP2bk5mVYfRSBI7FFf+tWEyLZwA==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^4.27.0 || ^5.0.0
    dependencies:
      json-parse-better-errors: 1.0.2
      loader-runner: 4.3.0
      loader-utils: 2.0.4
      neo-async: 2.6.2
      schema-utils: 3.3.0
      webpack: 4.47.0
    dev: false

  /throttle-debounce@1.1.0:
    resolution: {integrity: sha512-XH8UiPCQcWNuk2LYePibW/4qL97+ZQ1AN3FNXwZRBNPPowo/NRU5fAlDCSNBJIYCKbioZfuYtMhG4quqoJhVzg==}
    engines: {node: '>=4'}
    dev: false

  /through2@2.0.5:
    resolution: {integrity: sha512-/mrRod8xqpA+IHSLyGCQ2s8SPHiCDEeQJSep1jqLYeEUClOFG2Qsh+4FU6G9VeqpZnGW/Su8LQGc4YKni5rYSQ==}
    dependencies:
      readable-stream: 2.3.8
      xtend: 4.0.2
    dev: false

  /through@2.3.8:
    resolution: {integrity: sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg==}
    dev: false

  /time-fix-plugin@2.0.7(webpack@4.47.0):
    resolution: {integrity: sha512-uVFet1LQToeUX0rTcSiYVYVoGuBpc8gP/2jnlUzuHMHe+gux6XLsNzxLUweabMwiUj5ejhoIMsUI55nVSEa/Vw==}
    peerDependencies:
      webpack: '>=4.0.0'
    dependencies:
      webpack: 4.47.0
    dev: false

  /timers-browserify@2.0.12:
    resolution: {integrity: sha512-9phl76Cqm6FhSX9Xe1ZUAMLtm1BLkKj2Qd5ApyWkXzsMRaA7dgr81kf4wJmQf/hAvg8EEyJxDo3du/0KlhPiKQ==}
    engines: {node: '>=0.6.0'}
    dependencies:
      setimmediate: 1.0.5
    dev: false

  /tiny-emitter@2.1.0:
    resolution: {integrity: sha512-NB6Dk1A9xgQPMoGqC5CVXn123gWyte215ONT5Pp5a0yt4nlEoO1ZWeCwpncaekPHXO60i47ihFnZPiRPjRMq4Q==}
    dev: false

  /tinymce@4.9.11:
    resolution: {integrity: sha512-nkSLsax+VY5DBRjMFnHFqPwTnlLEGHCco82FwJF2JNH6W+5/ClvNC1P4uhD5lXPDNiDykSHR0XJdEh7w/ICHzA==}
    dev: false

  /tmp@0.0.33:
    resolution: {integrity: sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==}
    engines: {node: '>=0.6.0'}
    dependencies:
      os-tmpdir: 1.0.2
    dev: false

  /to-arraybuffer@1.0.1:
    resolution: {integrity: sha512-okFlQcoGTi4LQBG/PgSYblw9VOyptsz2KJZqc6qtgGdes8VktzUQkj4BI2blit072iS8VODNcMA+tvnS9dnuMA==}
    dev: false

  /to-fast-properties@2.0.0:
    resolution: {integrity: sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==}
    engines: {node: '>=4'}
    dev: false

  /to-object-path@0.3.0:
    resolution: {integrity: sha512-9mWHdnGRuh3onocaHzukyvCZhzvr6tiflAy/JRFXcJX0TjgfWA9pk9t8CMbzmBE4Jfw58pXbkngtBtqYxzNEyg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2
    dev: false

  /to-regex-range@2.1.1:
    resolution: {integrity: sha512-ZZWNfCjUokXXDGXFpZehJIkZqq91BcULFq/Pi7M5i4JnxXdhMKAK682z8bCW3o8Hj1wuuzoKcW3DfVzaP6VuNg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-number: 3.0.0
      repeat-string: 1.6.1
    dev: false

  /to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}
    dependencies:
      is-number: 7.0.0
    dev: false

  /to-regex@3.0.2:
    resolution: {integrity: sha512-FWtleNAtZ/Ki2qtqej2CXTOayOH9bHDQF+Q48VpWyDXjbYxA4Yz8iDB31zXOBUlOHHKidDbqGVrTUvQMPmBGBw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      define-property: 2.0.2
      extend-shallow: 3.0.2
      regex-not: 1.0.2
      safe-regex: 1.1.0
    dev: false

  /toidentifier@1.0.1:
    resolution: {integrity: sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==}
    engines: {node: '>=0.6'}
    dev: false

  /totalist@3.0.1:
    resolution: {integrity: sha512-sf4i37nQ2LBx4m3wB74y+ubopq6W/dIzXg0FDGjsYnZHVa1Da8FH853wlL2gtUhg+xJXjfk3kUZS3BRoQeoQBQ==}
    engines: {node: '>=6'}
    dev: false

  /tr46@0.0.3:
    resolution: {integrity: sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==}
    dev: false

  /tree-dump@1.0.2(tslib@2.7.0):
    resolution: {integrity: sha512-dpev9ABuLWdEubk+cIaI9cHwRNNDjkBBLXTwI4UCUFdQ5xXKqNXoK4FEciw/vxf+NQ7Cb7sGUyeUtORvHIdRXQ==}
    engines: {node: '>=10.0'}
    peerDependencies:
      tslib: '2'
    dependencies:
      tslib: 2.7.0
    dev: false

  /ts-pnp@1.2.0:
    resolution: {integrity: sha512-csd+vJOb/gkzvcCHgTGSChYpy5f1/XKNsmvBGO4JXS+z1v2HobugDz4s1IeFXM3wZB44uczs+eazB5Q/ccdhQw==}
    engines: {node: '>=6'}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dev: false

  /tslib@1.14.1:
    resolution: {integrity: sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==}
    dev: false

  /tslib@2.7.0:
    resolution: {integrity: sha512-gLXCKdN1/j47AiHiOkJN69hJmcbGTHI0ImLmbYLHykhgeN0jVGola9yVjFgzCUklsZQMW55o+dW7IXv3RCXDzA==}
    dev: false

  /tty-browserify@0.0.0:
    resolution: {integrity: sha512-JVa5ijo+j/sOoHGjw0sxw734b1LhBkQ3bvUGNdxnVXDCX81Yx7TFgnZygxrIIWn23hbfTaMYLwRmAxFyDuFmIw==}
    dev: false

  /type-fest@0.20.2:
    resolution: {integrity: sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==}
    engines: {node: '>=10'}
    dev: false

  /type-fest@0.21.3:
    resolution: {integrity: sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==}
    engines: {node: '>=10'}
    dev: false

  /typed-array-buffer@1.0.2:
    resolution: {integrity: sha512-gEymJYKZtKXzzBzM4jqa9w6Q1Jjm7x2d+sh19AdsD4wqnMPDYyvwpsIc2Q/835kHuo3BEQ7CjelGhfTsoBb2MQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-typed-array: 1.1.13
    dev: false

  /typed-array-byte-length@1.0.1:
    resolution: {integrity: sha512-3iMJ9q0ao7WE9tWcaYKIptkNBuOIcZCCT0d4MRvuuH88fEoEH62IuQe0OtraD3ebQEoTRk8XCBoknUNc1Y67pw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-proto: 1.0.3
      is-typed-array: 1.1.13
    dev: false

  /typed-array-byte-offset@1.0.2:
    resolution: {integrity: sha512-Ous0vodHa56FviZucS2E63zkgtgrACj7omjwd/8lTEMEPFFyjfixMZ1ZXenpgCFBBt4EC1J2XsyVS2gkG0eTFA==}
    engines: {node: '>= 0.4'}
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-proto: 1.0.3
      is-typed-array: 1.1.13
    dev: false

  /typed-array-length@1.0.6:
    resolution: {integrity: sha512-/OxDN6OtAk5KBpGb28T+HZc2M+ADtvRxXrKKbUwtsLgdoxgX13hyy7ek6bFRl5+aBs2yZzB0c4CnQfAtVypW/g==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-proto: 1.0.3
      is-typed-array: 1.1.13
      possible-typed-array-names: 1.0.0
    dev: false

  /typedarray@0.0.6:
    resolution: {integrity: sha512-/aCDEGatGvZ2BIk+HmLf4ifCJFwvKFNb9/JeZPMulfgFracn9QFcAf5GO8B/mweUjSoblS5In0cWhqpfs/5PQA==}
    dev: false

  /ua-parser-js@1.0.39:
    resolution: {integrity: sha512-k24RCVWlEcjkdOxYmVJgeD/0a1TiSpqLg+ZalVGV9lsnr4yqu0w7tX/x2xX6G4zpkgQnRf89lxuZ1wsbjXM8lw==}
    hasBin: true
    dev: false

  /ufo@1.5.4:
    resolution: {integrity: sha512-UsUk3byDzKd04EyoZ7U4DOlxQaD14JUKQl6/P7wiX4FNvUfm3XL246n9W5AmqwW5RSFJ27NAuM0iLscAOYUiGQ==}
    dev: false

  /unbox-primitive@1.0.2:
    resolution: {integrity: sha512-61pPlCD9h51VoreyJ0BReideM3MDKMKnh6+V9L08331ipq6Q8OFXZYiqP6n/tbHx4s5I9uRhcye6BrbkizkBDw==}
    dependencies:
      call-bind: 1.0.7
      has-bigints: 1.0.2
      has-symbols: 1.0.3
      which-boxed-primitive: 1.0.2
    dev: false

  /undici-types@6.19.8:
    resolution: {integrity: sha512-ve2KP6f/JnbPBFyobGHuerC9g1FYGn/F8n1LWTwNxCEzd6IfqTwUQcNXgEtmmQ6DlRrC1hrSrBnCZPokRrDHjw==}
    dev: false

  /undici@7.11.0:
    resolution: {integrity: sha512-heTSIac3iLhsmZhUCjyS3JQEkZELateufzZuBaVM5RHXdSBMb1LPMQf5x+FH7qjsZYDP0ttAc3nnVpUB+wYbOg==}
    engines: {node: '>=20.18.1'}
    dev: false

  /unfetch@5.0.0:
    resolution: {integrity: sha512-3xM2c89siXg0nHvlmYsQ2zkLASvVMBisZm5lF3gFDqfF2xonNStDJyMpvaOBe0a1Edxmqrf2E0HBdmy9QyZaeg==}
    dev: false

  /unicode-canonical-property-names-ecmascript@2.0.1:
    resolution: {integrity: sha512-dA8WbNeb2a6oQzAQ55YlT5vQAWGV9WXOsi3SskE3bcCdM0P4SDd+24zS/OCacdRq5BkdsRj9q3Pg6YyQoxIGqg==}
    engines: {node: '>=4'}
    dev: false

  /unicode-match-property-ecmascript@2.0.0:
    resolution: {integrity: sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q==}
    engines: {node: '>=4'}
    dependencies:
      unicode-canonical-property-names-ecmascript: 2.0.1
      unicode-property-aliases-ecmascript: 2.1.0
    dev: false

  /unicode-match-property-value-ecmascript@2.2.0:
    resolution: {integrity: sha512-4IehN3V/+kkr5YeSSDDQG8QLqO26XpL2XP3GQtqwlT/QYSECAwFztxVHjlbh0+gjJ3XmNLS0zDsbgs9jWKExLg==}
    engines: {node: '>=4'}
    dev: false

  /unicode-property-aliases-ecmascript@2.1.0:
    resolution: {integrity: sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w==}
    engines: {node: '>=4'}
    dev: false

  /union-value@1.0.1:
    resolution: {integrity: sha512-tJfXmxMeWYnczCVs7XAEvIV7ieppALdyepWMkHkwciRpZraG/xwT+s2JN8+pr1+8jCRf80FFzvr+MpQeeoF4Xg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-union: 3.1.0
      get-value: 2.0.6
      is-extendable: 0.1.1
      set-value: 2.0.1
    dev: false

  /union-value@2.0.1:
    resolution: {integrity: sha512-NmcRHHhUy1qWmp6yYWsaURV2qwfS24TmTtO9S9x0L41wCNNVBQFD3toOzO0cd8SsNrFhbw/O0iYO5uffXGYocw==}
    engines: {node: '>=6'}
    dependencies:
      get-value: 3.0.1
      set-value: 3.0.3
    dev: false

  /unique-filename@1.1.1:
    resolution: {integrity: sha512-Vmp0jIp2ln35UTXuryvjzkjGdRyf9b2lTXuSYUiPmzRcl3FDtYqAwOnTJkAngD9SWhnoJzDbTKwaOrZ+STtxNQ==}
    dependencies:
      unique-slug: 2.0.2
    dev: false

  /unique-slug@2.0.2:
    resolution: {integrity: sha512-zoWr9ObaxALD3DOPfjPSqxt4fnZiWblxHIgeWqW8x7UqDzEtHEQLzji2cuJYQFCU6KmoJikOYAZlrTHHebjx2w==}
    dependencies:
      imurmurhash: 0.1.4
    dev: false

  /universalify@0.1.2:
    resolution: {integrity: sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg==}
    engines: {node: '>= 4.0.0'}
    dev: false

  /universalify@2.0.1:
    resolution: {integrity: sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==}
    engines: {node: '>= 10.0.0'}
    dev: false

  /unpipe@1.0.0:
    resolution: {integrity: sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==}
    engines: {node: '>= 0.8'}
    dev: false

  /unset-value@1.0.0:
    resolution: {integrity: sha512-PcA2tsuGSF9cnySLHTLSh2qrQiJ70mn+r+Glzxv2TWZblxsxCC52BDlZoPCsz7STd9pN7EZetkWZBAvk4cgZdQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      has-value: 0.3.1
      isobject: 3.0.1
    dev: false

  /upath@1.2.0:
    resolution: {integrity: sha512-aZwGpamFO61g3OlfT7OQCHqhGnW43ieH9WZeP7QxN/G/jS4jfqUkZxoryvJgVPEcrl5NL/ggHsSmLMHuH64Lhg==}
    engines: {node: '>=4'}
    requiresBuild: true
    dev: false
    optional: true

  /upath@2.0.1:
    resolution: {integrity: sha512-1uEe95xksV1O0CYKXo8vQvN1JEbtJp7lb7C5U9HMsIp6IVwntkH/oNUzyVNQSd4S1sYk2FpSSW44FqMc8qee5w==}
    engines: {node: '>=4'}
    dev: false

  /update-browserslist-db@1.1.0(browserslist@4.23.3):
    resolution: {integrity: sha512-EdRAaAyk2cUE1wOf2DkEhzxqOQvFOoRJFNS6NeyJ01Gp2beMRpBAINjM2iDXE3KCuKhwnvHIQCJm6ThL2Z+HzQ==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'
    dependencies:
      browserslist: 4.23.3
      escalade: 3.2.0
      picocolors: 1.1.0
    dev: false

  /uri-js@4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}
    dependencies:
      punycode: 2.3.1
    dev: false

  /urix@0.1.0:
    resolution: {integrity: sha512-Am1ousAhSLBeB9cG/7k7r2R0zj50uDRlZHPGbazid5s9rlF1F/QKYObEKSIunSjIOkJZqwRRLpvewjEkM7pSqg==}
    deprecated: Please see https://github.com/lydell/urix#deprecated
    dev: false

  /url-loader@4.1.1(file-loader@6.2.0)(webpack@4.47.0):
    resolution: {integrity: sha512-3BTV812+AVHHOJQO8O5MkWgZ5aosP7GnROJwvzLS9hWDj00lZ6Z0wNak423Lp9PBZN05N+Jk/N5Si8jRAlGyWA==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      file-loader: '*'
      webpack: ^4.0.0 || ^5.0.0
    peerDependenciesMeta:
      file-loader:
        optional: true
    dependencies:
      file-loader: 6.2.0(webpack@4.47.0)
      loader-utils: 2.0.4
      mime-types: 2.1.35
      schema-utils: 3.3.0
      webpack: 4.47.0
    dev: false

  /url@0.11.4:
    resolution: {integrity: sha512-oCwdVC7mTuWiPyjLUz/COz5TLk6wgp0RCsN+wHZ2Ekneac9w8uuV0njcbbie2ME+Vs+d6duwmYuR3HgQXs1fOg==}
    engines: {node: '>= 0.4'}
    dependencies:
      punycode: 1.4.1
      qs: 6.13.0
    dev: false

  /use@3.1.1:
    resolution: {integrity: sha512-cwESVXlO3url9YWlFW/TA9cshCEhtu7IKJ/p5soJ/gGpj7vbvFrAY/eIioQ6Dw23KjZhYgiIo8HOs1nQ2vr/oQ==}
    engines: {node: '>=0.10.0'}
    dev: false

  /util-deprecate@1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}
    dev: false

  /util.promisify@1.0.0:
    resolution: {integrity: sha512-i+6qA2MPhvoKLuxnJNpXAGhg7HphQOSUq2LKMZD0m15EiskXUkMvKdF4Uui0WYeCUGea+o2cw/ZuwehtfsrNkA==}
    dependencies:
      define-properties: 1.2.1
      object.getownpropertydescriptors: 2.1.8
    dev: false

  /util@0.10.4:
    resolution: {integrity: sha512-0Pm9hTQ3se5ll1XihRic3FDIku70C+iHUdT/W926rSgHV5QgXsYbKZN8MSC3tJtSkhuROzvsQjAaFENRXr+19A==}
    dependencies:
      inherits: 2.0.3
    dev: false

  /util@0.11.1:
    resolution: {integrity: sha512-HShAsny+zS2TZfaXxD9tYj4HQGlBezXZMZuM/S5PKLLoZkShZiGk9o5CzukI1LVHZvjdvZ2Sj1aW/Ndn2NB/HQ==}
    dependencies:
      inherits: 2.0.3
    dev: false

  /utila@0.4.0:
    resolution: {integrity: sha512-Z0DbgELS9/L/75wZbro8xAnT50pBVFQZ+hUEueGDU5FN51YSCYM+jdxsfCiHjwNP/4LCDD0i/graKpeBnOXKRA==}
    dev: false

  /utils-merge@1.0.1:
    resolution: {integrity: sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==}
    engines: {node: '>= 0.4.0'}
    dev: false

  /vary@1.1.2:
    resolution: {integrity: sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==}
    engines: {node: '>= 0.8'}
    dev: false

  /via-editor@1.1.4:
    resolution: {integrity: sha512-DfEbQm3YQMSofk9fhTNVErYCv7FPea6+SJFwDYzgcRaC+zlH1OthBqVTrTECUuC4GNGHL/8SRiW/NDHKlIgxAA==}
    dev: false

  /vm-browserify@1.1.2:
    resolution: {integrity: sha512-2ham8XPWTONajOR0ohOKOHXkm3+gaBmGut3SRuu75xLd/RRaY6vqgh8NBYYk7+RW3u5AtzPQZG8F10LHkl0lAQ==}
    dev: false

  /vue-client-only@2.1.0:
    resolution: {integrity: sha512-vKl1skEKn8EK9f8P2ZzhRnuaRHLHrlt1sbRmazlvsx6EiC3A8oWF8YCBrMJzoN+W3OnElwIGbVjsx6/xelY1AA==}
    dev: false

  /vue-clipboard2@0.3.3:
    resolution: {integrity: sha512-aNWXIL2DKgJyY/1OOeITwAQz1fHaCIGvUFHf9h8UcoQBG5a74MkdhS/xqoYe7DNZdQmZRL+TAdIbtUs9OyVjbw==}
    dependencies:
      clipboard: 2.0.11
    dev: false

  /vue-focus-lock@1.4.1(vue@2.7.16):
    resolution: {integrity: sha512-Q33bDyy9v+FAsyO0dp7mufOjG8o+O/65/gbiG6BAR4ym6DRAG/za8E2kRj9F8C+s2kbg+Bn/4AFB9Qf+li8i6w==}
    peerDependencies:
      vue: ^2.0.0
    dependencies:
      focus-lock: 0.5.4
      vue: 2.7.16
    dev: false

  /vue-hot-reload-api@2.3.4:
    resolution: {integrity: sha512-BXq3jwIagosjgNVae6tkHzzIk6a8MHFtzAdwhnV5VlvPTFxDCvIttgSiHWjdGoTJvXtmRu5HacExfdarRcFhog==}
    dev: false

  /vue-loader@15.11.1(cache-loader@4.1.0)(css-loader@5.2.7)(lodash@4.17.21)(vue-template-compiler@2.7.16)(webpack@4.47.0):
    resolution: {integrity: sha512-0iw4VchYLePqJfJu9s62ACWUXeSqM30SQqlIftbYWM3C+jpPcEHKSPUZBLjSF9au4HTHQ/naF6OGnO3Q/qGR3Q==}
    peerDependencies:
      '@vue/compiler-sfc': ^3.0.8
      cache-loader: '*'
      css-loader: '*'
      prettier: '*'
      vue-template-compiler: '*'
      webpack: ^3.0.0 || ^4.1.0 || ^5.0.0-0
    peerDependenciesMeta:
      '@vue/compiler-sfc':
        optional: true
      cache-loader:
        optional: true
      prettier:
        optional: true
      vue-template-compiler:
        optional: true
    dependencies:
      '@vue/component-compiler-utils': 3.3.0(lodash@4.17.21)
      cache-loader: 4.1.0(webpack@4.47.0)
      css-loader: 5.2.7(webpack@4.47.0)
      hash-sum: 1.0.2
      loader-utils: 1.4.2
      vue-hot-reload-api: 2.3.4
      vue-style-loader: 4.1.3
      vue-template-compiler: 2.7.16
      webpack: 4.47.0
    transitivePeerDependencies:
      - arc-templates
      - atpl
      - babel-core
      - bracket-template
      - coffee-script
      - dot
      - dust
      - dustjs-helpers
      - dustjs-linkedin
      - eco
      - ect
      - ejs
      - haml-coffee
      - hamlet
      - hamljs
      - handlebars
      - hogan.js
      - htmling
      - jade
      - jazz
      - jqtpl
      - just
      - liquid-node
      - liquor
      - lodash
      - marko
      - mote
      - mustache
      - nunjucks
      - plates
      - pug
      - qejs
      - ractive
      - razor-tmpl
      - react
      - react-dom
      - slm
      - squirrelly
      - swig
      - swig-templates
      - teacup
      - templayed
      - then-jade
      - then-pug
      - tinyliquid
      - toffee
      - twig
      - twing
      - underscore
      - vash
      - velocityjs
      - walrus
      - whiskers
    dev: false

  /vue-meta@2.4.0:
    resolution: {integrity: sha512-XEeZUmlVeODclAjCNpWDnjgw+t3WA6gdzs6ENoIAgwO1J1d5p1tezDhtteLUFwcaQaTtayRrsx7GL6oXp/m2Jw==}
    dependencies:
      deepmerge: 4.3.1
    dev: false

  /vue-no-ssr@1.1.1:
    resolution: {integrity: sha512-ZMjqRpWabMPqPc7gIrG0Nw6vRf1+itwf0Itft7LbMXs2g3Zs/NFmevjZGN1x7K3Q95GmIjWbQZTVerxiBxI+0g==}
    dev: false

  /vue-router@3.6.5(vue@2.7.16):
    resolution: {integrity: sha512-VYXZQLtjuvKxxcshuRAwjHnciqZVoXAjTjcqBTz4rKc8qih9g9pI3hbDjmqXaHdgL3v8pV6P8Z335XvHzESxLQ==}
    peerDependencies:
      vue: ^2
    dependencies:
      vue: 2.7.16
    dev: false

  /vue-server-renderer@2.7.16:
    resolution: {integrity: sha512-U7GgR4rYmHmbs3Z2gqsasfk7JNuTsy/xrR5EMMGRLkjN8+ryDlqQq6Uu3DcmbCATAei814YOxyl0eq2HNqgXyQ==}
    dependencies:
      chalk: 4.1.2
      hash-sum: 2.0.0
      he: 1.2.0
      lodash.template: 4.5.0
      lodash.uniq: 4.5.0
      resolve: 1.22.8
      serialize-javascript: 6.0.2
      source-map: 0.5.6
    dev: false

  /vue-style-loader@4.1.3:
    resolution: {integrity: sha512-sFuh0xfbtpRlKfm39ss/ikqs9AbKCoXZBpHeVZ8Tx650o0k0q/YCM7FRvigtxpACezfq6af+a7JeqVTWvncqDg==}
    dependencies:
      hash-sum: 1.0.2
      loader-utils: 1.4.2
    dev: false

  /vue-template-compiler@2.7.16:
    resolution: {integrity: sha512-AYbUWAJHLGGQM7+cNTELw+KsOG9nl2CnSv467WobS5Cv9uk3wFcnr1Etsz2sEIHEZvw1U+o9mRlEO6QbZvUPGQ==}
    dependencies:
      de-indent: 1.0.2
      he: 1.2.0
    dev: false

  /vue-template-es2015-compiler@1.9.1:
    resolution: {integrity: sha512-4gDntzrifFnCEvyoO8PqyJDmguXgVPxKiIxrBKjIowvL9l+N66196+72XVYR8BBf1Uv1Fgt3bGevJ+sEmxfZzw==}
    dev: false

  /vue@2.7.16:
    resolution: {integrity: sha512-4gCtFXaAA3zYZdTp5s4Hl2sozuySsgz4jy1EnpBHNfpMa9dK1ZCG7viqBPCwXtmgc8nHqUsAu3G4gtmXkkY3Sw==}
    deprecated: Vue 2 has reached EOL and is no longer actively maintained. See https://v2.vuejs.org/eol/ for more details.
    dependencies:
      '@vue/compiler-sfc': 2.7.16
      csstype: 3.1.3
    dev: false

  /vuedraggable@2.24.3:
    resolution: {integrity: sha512-6/HDXi92GzB+Hcs9fC6PAAozK1RLt1ewPTLjK0anTYguXLAeySDmcnqE8IC0xa7shvSzRjQXq3/+dsZ7ETGF3g==}
    dependencies:
      sortablejs: 1.10.2
    dev: false

  /vuex-persistedstate@3.2.1(vue@2.7.16)(vuex@3.6.2):
    resolution: {integrity: sha512-0OnHKGsCHJcvbEraaGZvuvX4aybM2oQWYRuZmIQB7zUjVM6tP+Hg+oXLrq9r6elT4she9SGtEbGE1L2+XdFgUw==}
    deprecated: Package no longer supported. Contact Support at https://www.npmjs.com/support for more info.
    peerDependencies:
      vue: ^2.0.0
      vuex: ^2.0.0 || ^3.0.0
    dependencies:
      deepmerge: 4.3.1
      shvl: 2.0.3
      vue: 2.7.16
      vuex: 3.6.2(vue@2.7.16)
    dev: false

  /vuex@3.6.2(vue@2.7.16):
    resolution: {integrity: sha512-ETW44IqCgBpVomy520DT5jf8n0zoCac+sxWnn+hMe/CzaSejb/eVw2YToiXYX+Ex/AuHHia28vWTq4goAexFbw==}
    peerDependencies:
      vue: ^2.0.0
    dependencies:
      vue: 2.7.16
    dev: false

  /watchpack-chokidar2@2.0.1:
    resolution: {integrity: sha512-nCFfBIPKr5Sh61s4LPpy1Wtfi0HE8isJ3d2Yb5/Ppw2P2B/3eVSEBjKfN0fmHJSK14+31KwMKmcrzs2GM4P0Ww==}
    requiresBuild: true
    dependencies:
      chokidar: 2.1.8
    transitivePeerDependencies:
      - supports-color
    dev: false
    optional: true

  /watchpack@1.7.5:
    resolution: {integrity: sha512-9P3MWk6SrKjHsGkLT2KHXdQ/9SNkyoJbabxnKOoJepsvJjJG8uYTR3yTPxPQvNDI3w4Nz1xnE0TLHK4RIVe/MQ==}
    dependencies:
      graceful-fs: 4.2.11
      neo-async: 2.6.2
    optionalDependencies:
      chokidar: 3.6.0
      watchpack-chokidar2: 2.0.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /watchpack@2.4.2:
    resolution: {integrity: sha512-TnbFSbcOCcDgjZ4piURLCbJ3nJhznVh9kw6F6iokjiFPl8ONxe9A6nMDVXDiNbrSfLILs6vB07F7wLBrwPYzJw==}
    engines: {node: '>=10.13.0'}
    dependencies:
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11
    dev: false

  /webidl-conversions@3.0.1:
    resolution: {integrity: sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==}
    dev: false

  /webpack-bundle-analyzer@4.10.2:
    resolution: {integrity: sha512-vJptkMm9pk5si4Bv922ZbKLV8UTT4zib4FPgXMhgzUny0bfDDkLXAVQs3ly3fS4/TN9ROFtb0NFrm04UXFE/Vw==}
    engines: {node: '>= 10.13.0'}
    hasBin: true
    dependencies:
      '@discoveryjs/json-ext': 0.5.7
      acorn: 8.12.1
      acorn-walk: 8.3.4
      commander: 7.2.0
      debounce: 1.2.1
      escape-string-regexp: 4.0.0
      gzip-size: 6.0.0
      html-escaper: 2.0.2
      opener: 1.5.2
      picocolors: 1.1.0
      sirv: 2.0.4
      ws: 7.5.10
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate
    dev: false

  /webpack-dev-middleware@5.3.4(webpack@4.47.0):
    resolution: {integrity: sha512-BVdTqhhs+0IfoeAf7EoH5WE+exCmqGerHfDM0IL096Px60Tq2Mn9MAbnaGUe6HiMa41KMCYF19gyzZmBcq/o4Q==}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      colorette: 2.0.20
      memfs: 3.5.3
      mime-types: 2.1.35
      range-parser: 1.2.1
      schema-utils: 4.2.0
      webpack: 4.47.0
    dev: false

  /webpack-hot-middleware@2.26.1:
    resolution: {integrity: sha512-khZGfAeJx6I8K9zKohEWWYN6KDlVw2DHownoe+6Vtwj1LP9WFgegXnVMSkZ/dBEBtXFwrkkydsaPFlB7f8wU2A==}
    dependencies:
      ansi-html-community: 0.0.8
      html-entities: 2.5.2
      strip-ansi: 6.0.1
    dev: false

  /webpack-node-externals@3.0.0:
    resolution: {integrity: sha512-LnL6Z3GGDPht/AigwRh2dvL9PQPFQ8skEpVrWZXLWBYmqcaojHNN0onvHzie6rq7EWKrrBfPYqNEzTJgiwEQDQ==}
    engines: {node: '>=6'}
    dev: false

  /webpack-sources@1.4.3:
    resolution: {integrity: sha512-lgTS3Xhv1lCOKo7SA5TjKXMjpSM4sBjNV5+q2bqesbSPs5FjGmU6jjtBSkX9b4qW87vDIsCIlUPOEhbZrMdjeQ==}
    dependencies:
      source-list-map: 2.0.1
      source-map: 0.6.1
    dev: false

  /webpack-sources@2.3.1:
    resolution: {integrity: sha512-y9EI9AO42JjEcrTJFOYmVywVZdKVUfOvDUPsJea5GIr1JOEGFVqwlY2K098fFoIjOkDzHn2AjRvM8dsBZu+gCA==}
    engines: {node: '>=10.13.0'}
    dependencies:
      source-list-map: 2.0.1
      source-map: 0.6.1
    dev: false

  /webpack@4.47.0:
    resolution: {integrity: sha512-td7fYwgLSrky3fI1EuU5cneU4+pbH6GgOfuKNS1tNPcfdGinGELAqsb/BP4nnvZyKSG2i/xFGU7+n2PvZA8HJQ==}
    engines: {node: '>=6.11.5'}
    hasBin: true
    peerDependencies:
      webpack-cli: '*'
      webpack-command: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true
      webpack-command:
        optional: true
    dependencies:
      '@webassemblyjs/ast': 1.9.0
      '@webassemblyjs/helper-module-context': 1.9.0
      '@webassemblyjs/wasm-edit': 1.9.0
      '@webassemblyjs/wasm-parser': 1.9.0
      acorn: 6.4.2
      ajv: 6.12.6
      ajv-keywords: 3.5.2(ajv@6.12.6)
      chrome-trace-event: 1.0.4
      enhanced-resolve: 4.5.0
      eslint-scope: 4.0.3
      json-parse-better-errors: 1.0.2
      loader-runner: 2.4.0
      loader-utils: 1.4.2
      memory-fs: 0.4.1
      micromatch: 3.1.10
      mkdirp: 0.5.6
      neo-async: 2.6.2
      node-libs-browser: 2.2.1
      schema-utils: 1.0.0
      tapable: 1.1.3
      terser-webpack-plugin: 1.4.6(webpack@4.47.0)
      watchpack: 1.7.5
      webpack-sources: 1.4.3
    transitivePeerDependencies:
      - supports-color
    dev: false

  /webpackbar@6.0.1(webpack@4.47.0):
    resolution: {integrity: sha512-TnErZpmuKdwWBdMoexjio3KKX6ZtoKHRVvLIU0A47R0VVBDtx3ZyOJDktgYixhoJokZTYTt1Z37OkO9pnGJa9Q==}
    engines: {node: '>=14.21.3'}
    peerDependencies:
      webpack: 3 || 4 || 5
    dependencies:
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      consola: 3.2.3
      figures: 3.2.0
      markdown-table: 2.0.0
      pretty-time: 1.1.0
      std-env: 3.7.0
      webpack: 4.47.0
      wrap-ansi: 7.0.0
    dev: false

  /whatwg-encoding@3.1.1:
    resolution: {integrity: sha512-6qN4hJdMwfYBtE3YBTTHhoeuUrDBPZmbQaxWAqSALV/MeEnR5z1xd8UKud2RAkFoPkmB+hli1TZSnyi84xz1vQ==}
    engines: {node: '>=18'}
    dependencies:
      iconv-lite: 0.6.3
    dev: false

  /whatwg-mimetype@4.0.0:
    resolution: {integrity: sha512-QaKxh0eNIi2mE9p2vEdzfagOKHCcj1pJ56EEHGQOVxp8r9/iszLUUV7v89x9O1p/T+NlTM5W7jW6+cz4Fq1YVg==}
    engines: {node: '>=18'}
    dev: false

  /whatwg-url@5.0.0:
    resolution: {integrity: sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==}
    dependencies:
      tr46: 0.0.3
      webidl-conversions: 3.0.1
    dev: false

  /which-boxed-primitive@1.0.2:
    resolution: {integrity: sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg==}
    dependencies:
      is-bigint: 1.0.4
      is-boolean-object: 1.1.2
      is-number-object: 1.0.7
      is-string: 1.0.7
      is-symbol: 1.0.4
    dev: false

  /which-typed-array@1.1.15:
    resolution: {integrity: sha512-oV0jmFtUky6CXfkqehVvBP/LSWJ2sy4vWMioiENyJLePrBO/yKyV9OyJySfAKosh+RYkIl5zJCNZ8/4JncrpdA==}
    engines: {node: '>= 0.4'}
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-tostringtag: 1.0.2
    dev: false

  /which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true
    dependencies:
      isexe: 2.0.0
    dev: false

  /widest-line@3.1.0:
    resolution: {integrity: sha512-NsmoXalsWVDMGupxZ5R08ka9flZjjiLvHVAWYOKtiKM8ujtZWr9cRffak+uSE48+Ob8ObalXpwyeUiyDD6QFgg==}
    engines: {node: '>=8'}
    dependencies:
      string-width: 4.2.3
    dev: false

  /wmf@1.0.2:
    resolution: {integrity: sha512-/p9K7bEh0Dj6WbXg4JG0xvLQmIadrner1bi45VMJTfnbVHsc7yIajZyoSoK60/dtVBs12Fm6WkUI5/3WAVsNMw==}
    engines: {node: '>=0.8'}
    dev: false

  /word@0.3.0:
    resolution: {integrity: sha512-OELeY0Q61OXpdUfTp+oweA/vtLVg5VDOXh+3he3PNzLGG/y0oylSOC1xRVj0+l4vQ3tj/bB1HVHv1ocXkQceFA==}
    engines: {node: '>=0.8'}
    dev: false

  /worker-farm@1.7.0:
    resolution: {integrity: sha512-rvw3QTZc8lAxyVrqcSGVm5yP/IJ2UcB3U0graE3LCFoZ0Yn2x4EoVSqJKdB/T5M+FLcRPjz4TDacRf3OCfNUzw==}
    dependencies:
      errno: 0.1.8
    dev: false

  /wrap-ansi@7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1
    dev: false

  /wrappy@1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==}
    dev: false

  /write-file-atomic@2.4.3:
    resolution: {integrity: sha512-GaETH5wwsX+GcnzhPgKcKjJ6M2Cq3/iZp1WyY/X1CSqrW+jVNM9Y7D8EC2sM4ZG/V8wZlSniJnCKWPmBYAucRQ==}
    dependencies:
      graceful-fs: 4.2.11
      imurmurhash: 0.1.4
      signal-exit: 3.0.7
    dev: false

  /write-json-file@2.3.0:
    resolution: {integrity: sha512-84+F0igFp2dPD6UpAQjOUX3CdKUOqUzn6oE9sDBNzUXINR5VceJ1rauZltqQB/bcYsx3EpKys4C7/PivKUAiWQ==}
    engines: {node: '>=4'}
    dependencies:
      detect-indent: 5.0.0
      graceful-fs: 4.2.11
      make-dir: 1.3.0
      pify: 3.0.0
      sort-keys: 2.0.0
      write-file-atomic: 2.4.3
    dev: false

  /ws@7.5.10:
    resolution: {integrity: sha512-+dbF1tHwZpXcbOJdVOkzLDxZP1ailvSxM6ZweXTegylPny803bFhA+vqBYw4s31NSAk4S2Qz+AKXK9a4wkdjcQ==}
    engines: {node: '>=8.3.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ^5.0.2
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true
    dev: false

  /xlsx@0.16.9:
    resolution: {integrity: sha512-gxi1I3EasYvgCX1vN9pGyq920Ron4NO8PNfhuoA3Hpq6Y8f0ECXiy4OLrK4QZBnj1jx3QD+8Fq5YZ/3mPZ5iXw==}
    engines: {node: '>=0.8'}
    hasBin: true
    dependencies:
      adler-32: 1.2.0
      cfb: 1.2.2
      codepage: 1.14.0
      commander: 2.17.1
      crc-32: 1.2.2
      exit-on-epipe: 1.0.1
      fflate: 0.3.11
      ssf: 0.11.2
      wmf: 1.0.2
      word: 0.3.0
    dev: false

  /xtend@4.0.2:
    resolution: {integrity: sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==}
    engines: {node: '>=0.4'}
    dev: false

  /xxhashjs@0.2.2:
    resolution: {integrity: sha512-AkTuIuVTET12tpsVIQo+ZU6f/qDmKuRUcjaqR+OIvm+aCBsZ95i7UVY5WJ9TMsSaZ0DA2WxoZ4acu0sPH+OKAw==}
    dependencies:
      cuint: 0.2.2
    dev: false

  /y18n@4.0.3:
    resolution: {integrity: sha512-JKhqTOwSrqNA1NY5lSztJ1GrBiUodLMmIZuLiDaMRJ+itFd+ABVE8XBjOvIWL+rSqNDC74LCSFmlb/U4UZ4hJQ==}
    dev: false

  /yallist@2.1.2:
    resolution: {integrity: sha512-ncTzHV7NvsQZkYe1DW7cbDLm0YpzHmZF5r/iyP3ZnQtMiJ+pjzisCiMNI+Sj+xQF5pXhSHxSB3uDbsBTzY/c2A==}
    dev: false

  /yallist@3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==}
    dev: false

  /yallist@4.0.0:
    resolution: {integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==}
    dev: false

  /yaml@1.10.2:
    resolution: {integrity: sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==}
    engines: {node: '>= 6'}
    dev: false

  /yocto-queue@0.1.0:
    resolution: {integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==}
    engines: {node: '>=10'}
    dev: false
