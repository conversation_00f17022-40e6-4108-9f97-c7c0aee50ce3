<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei',<PERSON>l,sans-serif;">一口价兑换市场档位</h2>

      <el-form :inline="true" :model="filters">
        <el-form-item>
          <el-form-item label="市场">
            <el-select v-model="filters.market" clearable filterable @change="filter_items" placeholder="<ALL>">
              <el-option v-for="m in markets" :key="m" :label="m" :value="m"> </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="档位">
            <el-select v-model="filters.rank" clearable filterable @change="filter_items" placeholder="<ALL>">
              <el-option v-for="(name, key) in rank_map" :key="key" :label="name" :value="key"> </el-option>
            </el-select>
          </el-form-item>

          <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
            <el-button icon="el-icon-refresh-left" circle @click="handle_page_refresh"></el-button>
          </el-tooltip>
        </el-form-item>
      </el-form>

      <el-table :data="items.slice((page - 1) * limit, page * limit)" v-loading="loading" ref="table" style="width: 100%">
        <el-table-column label="兑换市场" prop="market"> </el-table-column>

        <el-table-column label="档位" prop="rank">
          <template slot-scope="scope">
            <span v-if="scope.row.available">{{ scope.row.rank }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column label="档位深度范围（USD）" prop="min_depth_usd" :formatter="row => format_depth_usd(row)"> </el-table-column>

        <el-table-column label="最大价值范围（USD）" prop="max_exchange_usd">
          <template slot-scope="scope">
            <span v-if="scope.row.available">{{ scope.row.max_exchange_usd }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column
          label="最大价格偏差"
          prop="max_price_slippage"
        >
        </el-table-column>

        <el-table-column label="兑换并发市场限制" prop="max_pending_num"></el-table-column>
      </el-table>

      <el-pagination
        :current-page.sync="page"
        :page-size.sync="limit"
        :page-sizes="[1, 2, 3, 25, 50, 100, 200, 500]"
        :total="total"
        @size-change="handle_limit_change"
        @current-change="handle_page_change"
        :hide-on-single-page="false"
        layout="total, sizes, prev, pager, next, jumper"
      >
      </el-pagination>

      <el-backtop></el-backtop>
    </el-main>
  </el-container>
</template>

<script>
import Vue from 'vue';
import VueClipboard from 'vue-clipboard2';
Vue.use(VueClipboard);

const binding_url = '/api/payment/quote-market-depths';

export default {
  methods: {
    get_data() {
      this.loading = true;
      this.$axios.get(binding_url, { params: this.filters }).then(res => {
        this.loading = false;
        if (res && res.data.code === 0) {
          let data = res.data.data;
          this.all_items = data.items;
          this.items = data.items;
          this.total = data.items.length;
          this.rank_map = data.rank_map;
          this.markets = data.markets;
          this.filter_items();
        } else {
          this.all_items = [];
          this.items = [];
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      });
    },
    filter_items() {
      let items = [];
      for (let index = 0; index < this.all_items.length; index++) {
        const item = this.all_items[index];
        if (this.filters.market && this.filters.market != item.market) continue;
        if (this.filters.rank && this.filters.rank != item.rank) continue;
        items.push(item);
      }
      this.items = items;
    },
    format_depth_usd(row) {
      if (row.min_depth_usd) {
        return row.min_depth_usd + ' <= X < ' + row.max_depth_usd;
      }
      return '-';
    },
    format_price_deviation(row) {
      let n = Number(row.price_deviation) * 100;
      n = n.toFixed(2);
      return n + '%';
    },
    notice(title, message) {
      this.$alert(message, title, {
        confirmButtonText: '确定',
      });
    },
    res_success_notice(res) {
      let title = '提交成功';
      this.notice(title, title);
    },
    res_fail_notice(res) {
      let title = '提交失败';
      let message = `(code: ${res.data?.code}; message: ${res.data?.message})`;
      this.notice(title, message);
    },
    res_error_notice(error) {
      let title = '提交失败';
      let message = `(code: ${error.response.status}; message: ${error.message})`;
      this.notice(title, message);
    },
    handle_page_change(val) {
      this.page = val;
    },
    handle_limit_change(val) {
      this.page = 1;
      this.limit = val;
    },
    handle_page_refresh() {
      this.get_data();
    },
    update_router_query() {
      let query = {};
      Object.assign(query, this.filters);
      Object.keys(query).forEach(key => !query[key] && delete query[key]);
      this.$router.replace({ query: query });
    },
  },
  created() {
    this.$watch('filters', {
      handler: function() {
        this.update_router_query();
      },
      deep: true,
    });
  },
  mounted() {
    this.get_data();
  },
  data() {
    return {
      filters: {
        market: null,
        rank: null,
      },
      page: 1,
      limit: 100,
      total: 0,
      items: [],
      all_items: [], // 前端分页，存原始的全部items
      rank_map: [],
      markets: [],
      loading: true,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
    };
  },
};
</script>
