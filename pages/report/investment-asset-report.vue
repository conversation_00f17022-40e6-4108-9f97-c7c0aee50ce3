<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
        币种理财报表
        <el-tooltip placement="right" :open-delay="500">
          <div slot="content">
            <p>理财收益人数：对应日期有收到理财收益的用户，子账户独立计算</p>
            <p>新增理财收益人数：对应日期新增的理财用户数量，首次收到理财收益，子账户独立计算</p>
            <p>理财人数：对应日期理财账户有资产的用户数，子账户独立计算</p>
            <p>新增理财人数：对应日期新增的理财用户数量，理财账户首次有资产，子账户独立计算</p>
            <p>理财数量：对应日期对应币种的有效理财数量</p>
            <p>理财市值（USD）：对应日期对应币种的有效理财市值</p>
            <p>理财收益：对应日期对应币种的理财收益数量</p>
            <p>年化收益率：年化收益率=理财收益/理财市值*365</p>
          </div>
          <i class="el-icon-question"></i>
        </el-tooltip>
      </h2>
      <el-tabs v-model="search_data.report_type" type="card"  @tab-click="search(true)">
        <el-tab-pane label="日报" name="daily"></el-tab-pane>
        <el-tab-pane label="月报" name="monthly"></el-tab-pane>
      </el-tabs>
      <el-form :inline="true" :model="search_data">
        <el-form-item label="币种">
          <el-select filterable clearable v-model="search_data.asset" placeholder="<ALL>">
            <el-option v-for="value in assets"
                       :key="value"
                       :label="value"
                       :value="value">
            </el-option>
          </el-select>
        </el-form-item>
        <template v-if="search_data.report_type === 'daily'">
          <el-form-item prop="start_date" label="开始时间">
            <el-date-picker
              required
              v-model="search_data.start_date"
              type="date"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              placeholder="开始时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="end_date" label="结束时间">
            <el-date-picker
              required
              v-model="search_data.end_date"
              type="date"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              placeholder="结束时间">
            </el-date-picker>
          </el-form-item>
        </template>

        <template v-if="search_data.report_type === 'monthly'">
          <el-form-item prop="start_date" label="开始时间">
            <el-date-picker
              required
              v-model="search_data.start_date"
              type="month"
              value-format="yyyy-MM-01"
              :picker-options="pickerOptions"
              placeholder="开始时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="end_date" label="结束时间">
            <el-date-picker
              required
              v-model="search_data.end_date"
              type="month"
              value-format="yyyy-MM-01"
              :picker-options="pickerOptions"
              placeholder="结束时间">
            </el-date-picker>
          </el-form-item>
        </template>

        <el-form-item>
          <el-button type="primary" @click="search">查询</el-button>
        </el-form-item>
          <el-form-item>
            <el-tooltip
              content="导出数据"
              placement="right"
              :open-delay="500"
              :hide-after="2000"
            >
              <el-button
                icon="el-icon-download"
                circle
                @click="download"
              ></el-button>
            </el-tooltip>
          </el-form-item>

      </el-form>

      <FixedTable
        :data="items"
        @header-click="show_series_dialog" ref="table"
        size="medium"
        stripe>
        <el-table-column
          label="日期"
          prop="report_date"
          :formatter="row => $formatDate(row.report_date, search_data.report_type === 'daily'?'YYYY-MM-DD':'YYYY-MM')">
        </el-table-column>
        <el-table-column
          label="币种"
          prop="asset">
        </el-table-column>

        <el-table-column
          label="理财收益人数"
          prop="interest_user_count"
          :render-header="renderHeader" column-key="理财收益人数：理财收益人数"
        >
        </el-table-column>
        <el-table-column
          label="新增理财收益人数"
          prop="increase_interest_user"
          :render-header="renderHeader" column-key="新增理财收益人数：新增理财收益人数"
        >
        </el-table-column>
        <el-table-column
          label="理财人数"
          prop="user_count"
          :render-header="renderHeader" column-key="理财人数：理财人数">
        </el-table-column>
        <el-table-column
          label="新增理财人数"
          prop="increase_investment_user"
          :render-header="renderHeader" column-key="新增理财人数：新增理财人数">
        </el-table-column>

        <el-table-column
          label="理财数量"
          :render-header="renderHeader" column-key="理财数量：理财数量"
          prop="amount">
        </el-table-column>

        <el-table-column
          label="理财市值(USD)"
          :render-header="renderHeader" column-key="理财市值(USD)：理财市值(USD)"
          :formatter="row => $formatNumber(row.usd, 2)"
          prop="usd">
        </el-table-column>

        <el-table-column
          label="理财总收益"
          :render-header="renderHeader" column-key="理财收益(USD)：理财收益(USD)"
          :formatter="row => $formatNumber(row.investment_interest_usd, 2)"
          prop="investment_interest_usd">
        </el-table-column>

        <el-table-column
          label="基础收益(USD)"
          :formatter="row => $formatNumber(row.base_interest_usd || 0, 2)"
          prop="base_interest_usd"
          :render-header="renderHeader" column-key="基础收益(USD)：基础收益(USD)"
        >
        </el-table-column>

        <el-table-column
          label="阶梯补贴(USD)"
          :formatter="row => $formatNumber(row.ladder_interest_usd || 0, 2)"
          prop="ladder_interest_usd"
          :render-header="renderHeader" column-key="阶梯补贴(USD)：阶梯补贴(USD)"
        >
        </el-table-column>

        <el-table-column
          label="固定补贴(USD)"
          :formatter="row => $formatNumber(row.fixed_interest_usd || 0, 2)"
          prop="fixed_interest_usd"
          :render-header="renderHeader" column-key="固定补贴(USD)：固定补贴(USD)"
        >
        </el-table-column>

        <el-table-column
          label="收益率(当日年化)"
          :formatter="row => search_data.report_type === 'daily' ? $formatPercent(row.investment_interest_rate, 2): '-'"
          :render-header="renderHeader" column-key="收益率(当日年化)：收益率(当日年化)"
          prop="investment_interest_rate">
        </el-table-column>
      </FixedTable>
      <el-pagination :current-page.sync="search_data.page"
                     :page-size.sync="search_data.limit"
                     @size-change="search"
                     @current-change="search"
                     :page-sizes="[50, 25]"
                     :hide-on-single-page="true"
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>

      <el-dialog :visible.sync="show_dialog" width="80%" :before-close="handleClose">
        <Series ref="series" :render_info="render_info" v-if="show_dialog === true" @getChildValue="setChildValue"></Series>
      </el-dialog>

      <el-backtop></el-backtop>
    </el-main>
  </el-container>
</template>


<script>
  import moment from "moment";
  import Series from '~/components/Series'
  import FixedTable from '~/components/FixedTableRender.vue';
  import series from '~/plugins/report/series'
  import thousandSeperatorMixins from "~/plugins/instances/report";
  const exclude_render_columns = ['report_date', 'asset'];

  const binding_url = "/api/report/investment/asset-investment-report";

  export default {
    components: {
      Series,
      FixedTable,
    },
    mixins: [series, thousandSeperatorMixins],
    watchQuery: ['report_type', 'start_date', 'end_date', 'asset', 'page', 'limit'],
    key: to => to.fullPath,
    asyncData({app, query}) {
      query.limit = query.limit ? query.limit : 50
      query.page = query.page ? query.page : 1
      let new_query = _.clone(query);
      if(!new_query.hasOwnProperty("report_type")) {
        new_query.report_type = 'daily';
      }
      if(!new_query.hasOwnProperty("asset")) {
        new_query.asset = 'USDT';
      }
      return app.$axios["get"](binding_url, {params: new_query})
        .then((res) => {
          return {
            assets: res.data.data.assets,
            total: res.data.data.total,
            items: res.data.data.items,
            search_data: _.clone(new_query),
          }
        })
        .catch((e) => {
        })
    },
    methods: {
      show_series_dialog(column) {
        if (exclude_render_columns.includes(column.property)) {
          return
        }
        this.show_dialog = true;
        this.set_render_info(column, {
          exclude_columns: exclude_render_columns,
          binding_url: binding_url + '?asset=' + this.search_data.asset,
          resp_key: 'items',
          report_type_lower: true,
        });
      },

      search(reset=true) {
        if(reset === true) {
          this.search_data.page = 1;
        }
        this.$router.push({path: this.$route.path, query: this.search_data});
      },


    download() {
      let filename;
        if (this.search_data.report_type && this.search_data.report_type.toLowerCase() === 'monthly') {
          filename = 'investment_asset_monthly.xlsx';
        } else {
          filename = 'investment_asset_daily.xlsx';
        }
        let params =  {...this.search_data, export: true}
        this.$download_from_url(binding_url, filename, params)
      },
    },
    data() {
      return {
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now();
          }
        },
        series_search_data: {
          start_date: null,
          end_date: null,
          series_type: null,
        },
        search_data: {
          asset: "USDT"
        },
        series_types: {},
        show_dialog: false,
        chart_type: "user_count",
        series_chart_options: {},
        series_time_range: null
      }
    },
  }

</script>
