<template>
  <div>
    <el-form :inline="true" style="margin-left: 9%" :model="series_filters">
      <el-form-item label="数据类型">
        <el-select v-model="column" filterable @change="do_render_series"
                   style="width: 120px">
          <el-option v-for="(v, k) in series_data_types" :key="k" :label="v" :value="k"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item prop="start_date" label="开始时间">
        <el-date-picker
          v-model="series_filters.start_date"
          :type="this.report_type_is_daily() ? 'date' : 'month'"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptions"
          placeholder="时间"
          @change="do_render_series"
          style="width: 180px"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item prop="end_date" label="结束时间">
        <el-date-picker
          v-model="series_filters.end_date"
          :type="this.report_type_is_daily() ? 'date' : 'month'"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptions"
          placeholder="时间"
          @change="do_render_series"
          style="width: 180px"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="时间范围">
        <el-select
          v-model="series_time_range"
          filterable
          clearable
          @change="handle_series_time_range_change"
          placeholder="<ALL>"
          style="width: 120px"
        >
          <el-option
            v-for="time_range in this.report_type_is_daily() ? daily_time_ranges: monthly_time_ranges"
            :key="time_range.value"
            :label="time_range.label"
            :value="time_range.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <span v-for="filter in filters">
        <el-form-item :label="filter.label">
          <el-select
            v-model="series_filters[filter.key]"
            filterable
            clearable
            @change="handle_series_time_range_change"
            placeholder="<ALL>"
            style="width: 120px"
          >
            <el-option
              v-for="op in filter.options"
              :key="op"
              :label="op"
              :value="op"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </span>

      <el-form-item>
        <el-button type="primary" @click="do_render_series">查询</el-button>
      </el-form-item>
    </el-form>

    <div style="height: 100%">
      <el-row type="flex" class="row-bg" justify="center" height="100%" width="100%">
        <el-col :span="20">
          <el-card>
            <highcharts class="hc" :reflow="true" :options="chart_options"></highcharts>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import {Decimal} from "decimal.js";
import {Chart} from 'highcharts-vue';
import moment from "moment";

export default {
  components: {
    highcharts: Chart,
  },
  name: "Series",
  props: {
    render_info: {
      require: true,
      type: Object
    },
    filters: {
      default: [],
      type: Array
    },
  },
  mounted() {
    this.render_series_on_mounted();
  },
  methods: {
    render_series_on_mounted() {
      this.parse_series_model();
      this.set_default_filter_value();
      this.set_series_filters_start_date(this.series_time_range);
      this.do_render_series();
    },

    parse_series_model() {
      this.url = this.render_info.url;
      this.report_type = this.render_info.report_type.toUpperCase();
      if (this.render_info.report_type_lower) {
        this.report_type = this.report_type.toLowerCase();
        this.report_type_lower = this.render_info.report_type_lower;
      }
      this.column = this.render_info.column;
      this.columnAxis = this.render_info.columnAxis;
      this.resp_key = this.render_info.resp_key;
      this.series_data_types = this.render_info.series_data_types;
      this.series_time_range = this.set_and_get_series_time_range(this.render_info.series_time_range)      
      if (this.render_info.query_params) {
        Object.keys(this.render_info.query_params).forEach(key => {
          this.$set(this.series_filters, key, this.render_info.query_params[key]);
        })
      }
    },

    set_and_get_series_time_range(value) {
      if (this.report_type_is_daily()) {
        this.daily_series_time_range = value || 90;
        return this.daily_series_time_range
      } else {
        this.monthly_series_time_range = value || 365;
        return this.monthly_series_time_range
      }
    },

    handle_series_time_range_change() {
      this.set_series_filters_start_date(this.series_time_range);
      this.series_filters.end_date = null;
      this.do_render_series();
      this.$emit('getChildValue', this.series_time_range);
    },

    set_series_filters_start_date(value) {
      if (value === -1) {
        this.series_filters.start_date = null;
      } else {
        this.series_filters.start_date = this.getDiffDate(Date.now(), -this.series_time_range);
      }
    },

    set_default_filter_value() {
        this.filters.forEach(filter => {
          if (filter.default && !this.series_filters[filter.key]) {
            // this.series_filters[filter.key] = filter.default;
            this.$set(this.series_filters, filter.key, filter.default);
          }
        })
    },
    getDiffDate(baseDate, diff) {
      let date_ = new Date(baseDate);
      let newDateMilliSecs = date_.getTime() + diff * 24 * 3600 * 1000;
      return new Date(newDateMilliSecs).toLocaleString().split(' ')[0].replaceAll('/', '-');
    },

    do_render_series() {
      this.update_series_data();
      this.update_chart_options();
    },

    set_detail_series_from_remote() {
      const self = this;
      this.series_filters.report_type = this.report_type;
      let params = {params: this.series_filters};
      if (this.report_type_is_daily()) {
        if (this.series_filters.start_date) {
          let start_date = this.getDiffDate(this.series_filters.start_date, -6);
          params = {params: {...this.series_filters, start_date: start_date}};
        }
      }
      this.$axios.get(this.url, params).then(res => {
        if (res && res.data.code === 0) {
          if (self.resp_key) {
            self.ret = res.data.data[self.resp_key];
          } else {
            self.ret = res.data.data['data'];
          }
          self.set_series_data(self.ret);
          self.update_chart_options();
        } else {
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      });
    },

    update_chart_options() {
      this.chart_options = {
        title: {
          text: null,
        },
        chart: {
          height: '50%',
        },
        series: [
          {
            name: this.columnAxis[this.column].xAxis.name,
            data: this.series.data,
          },
        ],
        xAxis: {
          type: 'datetime',
          labels: {},
        },
        yAxis: [
          {
            title: {
              text: this.columnAxis[this.column].yAxis.text,
            },
            labels: {
              style: {
                fontSize: '15px',
              },
            },
          },
        ],
      };

      if (this.report_type_is_daily()) {
        this.chart_options.series.push({
          name: '7日均线',
          data: this.series.avgData,
        });
        this.chart_options.xAxis.labels.format = '{value:%m-%d}';
      } else {
        this.chart_options.xAxis.labels.format = '{value:%Y-%m}';
      }
    },

    update_series_data() {
      this.set_detail_series_from_remote();
      let data = this.ret;
      if (!data) {
        return
      }
      this.set_series_data(data);
    },

    set_series_data(data) {
      // get series data by the column key.
      let series_data = [];  // 按时间倒序
      let avgData = []; // 按时间倒序
      let start_date = null;
      if(this.series_filters.start_date) {
        start_date = this.parse_date(this.series_filters.start_date);
      }
      for (let i in data) {
        let obj = data[i];
        let report_date = this.parse_date(obj['report_date']);
        let column = this.parse_column(obj);
        if(report_date) {
          if (start_date) {
            if (report_date >= start_date) {
              series_data.push([report_date, column]);
            }
          } else {
            series_data.push([report_date, column]);
          }
          avgData.push([report_date, column]);
        }
      }
      let avg_series_data = [];
      for (let i = 0; i < avgData.length; i++) {
        let newAvgData = avgData.slice(i);
        let calcAvgData = newAvgData.slice(0, 7);
        let calcVal = 0;
        let calcLength = calcAvgData.length;
        if (calcLength === 0) {
          break;
        }
        let rt = calcAvgData[0][0];
        if (rt < start_date) {
          break;
        }
        calcAvgData.forEach(t => {
          calcVal += t[1]
        })
        avg_series_data.push([rt, calcVal / calcLength]);
      }
      series_data.reverse();
      avg_series_data.reverse();
      this.series.data = series_data;
      this.series.avgData = avg_series_data;
    },

    parse_date(d) {
      let report_date;
      if (typeof d === 'string') {
        report_date = Date.parse(d);
      } else {
        report_date = d * 1000;
      }
      return report_date;
    },

    is_numeric(value) {
      value = Number(value);
      return typeof value === 'number' && !Number.isNaN(value) && Number.isFinite(value);
    },

    parse_column(obj) {
      let column;
      let func;
      let this_column;
      if (this.column.split('?').length > 1) {
        this_column = this.column.split('?')[0];
        func = this.column.split('=')[1];
      } else {
        this_column = this.column;
      }
      if (this_column.split('.').length > 1) {
        column = eval('obj.' + this_column);
      } else if (this_column.split('/').length > 1) {
        let list = this_column.split('/');
        let attr0 = list[0];
        let attr1 = list[1];
        let left = list.slice(2);
        if (obj[attr1] != 0) {
          column = obj[attr0] / obj[attr1];
          for (let i in left) {
            if (left[i] != 0) {
              column /= obj[left[i]];
            } else {
              column = 0;
              break;
            }
          }
        } else {
          column = 0;
        }
      } else if (this_column.split('-').length > 1) {
        let list = this_column.split('-');
        let incr = obj[this.trim_space(list[0])];
        let decr = obj[this.trim_space(list[1])];
        if (!this.is_numeric(incr)) {
          incr = 0;
        }
        if (!this.is_numeric(decr)) {
          decr = 0;
        }
        incr = Number(Decimal(incr || 0).toDecimalPlaces(8).toString());
        decr = Number(Decimal(decr || 0).toDecimalPlaces(8).toString());
        column = incr - decr;
      } else {
        column = obj[this_column];
      }
      if (typeof column === 'string' && column.endsWith('%')) {
        column = parseFloat(column.replace('%', '')) / 100;
      } else if (typeof column === 'string' && column.split(' ').length === 2) {
        column = column.split(' ')[0];
      }
      if (!this.is_numeric(column)) {
          column = 0;
        }
      column = Number(Decimal(column || 0).toDecimalPlaces(8).toString());
      if (func === 'abs') {
        column = Math.abs(column);
      }
      return column;
    },

    trim_space(s) {
      return s.replace(/(^\s*)|(\s*$)/g, "");
    },

    report_type_is_daily() {
      if (this.report_type_lower) {
        return this.report_type === 'daily';
      } else {
        return this.report_type === 'DAILY';
      }
    }
  },
  data() {
    return {
      render_info: this.render_info,  // props initial

      // from father page
      url: null,
      report_type: null,
      report_type_lower: false,
      column: null,
      series_data_types: {},
      series_filters: {
        'start_date': null,
        'end_date': null,
        'limit': 1000,
      },
      series: {
        'xAxis': {'name': null},
        'yAxis': {'text': null},
        'data': null,  // self-satisfied
        'avgData': null,  // self-satisfied
      },
      columnAxis: {},
      resp_key: null,  //

      // self-satisfied at this page
      series_time_range: null,
      daily_series_time_range: 90,
      monthly_series_time_range: 365,
      daily_time_ranges: [

        {"label": "近30天", "value": 30},
        {"label": "近90天", "value": 90},
        {"label": "近180天", "value": 180},
        {"label": "近1年", "value": 365},
      ],
      monthly_time_ranges: [

        {"label": "近180天", "value": 180},
        {"label": "近1年", "value": 365},
        {"label": "近2年", "value": 365 * 2},
        {"label": "全部", "value": -1},
      ],
      chart_options: {
        series: [
          {
            name: null,
            data: null,
          },
        ],
      },
      ret: [],
    }
  }
}
</script>
