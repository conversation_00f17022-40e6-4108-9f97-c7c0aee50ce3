<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
        收付款-全站报表
        <el-tooltip placement="right" :open-delay="500">
          <div slot="content">
            <p>全站付款金额：周期内全站付款金额累计市值</p>
            <p>全站兑换金额：周期内兑换付款金额累计市值</p>
            <p>手续费市值：一口价兑换手续费收入市值</p>
            <p>兑换收入市值：周期内兑换盈余累计市值</p>
            <p>兑换支出市值：周期内兑换支出累计市值</p>
            <p>收付款人数：收款人数+付款人数排重后的总数</p>
            <p>新增人数：之前未使用过收付款，周期内新增用户</p>
            <p>兑换订单占比：兑换订单占比中付款订单的比例</p>
            <p>平均兑换市值：总兑换市值/兑换付款笔数</p>
          </div>
          <i class="el-icon-question"></i>
        </el-tooltip>
      </h2>

      <el-tabs v-model="filters.report_type" type="card" @tab-click="handle_page_refresh()">
        <el-tab-pane label="日报" name="DAILY"></el-tab-pane>
        <el-tab-pane label="月报" name="MONTHLY"></el-tab-pane>
      </el-tabs>

      <el-form :inline="true" :model="filters">
        <el-form-item prop="start_date" label="开始时间">
          <el-date-picker v-model="filters.start_date" :type="filters.report_type == 'DAILY' ? 'date' : 'month'"
            value-format="yyyy-MM-dd" :picker-options="pickerOptions" placeholder="时间" @change="handle_page_refresh">
          </el-date-picker>
        </el-form-item>

        <el-form-item prop="end_date" label="结束时间">
          <el-date-picker v-model="filters.end_date" :type="filters.report_type == 'DAILY' ? 'date' : 'month'"
            value-format="yyyy-MM-dd" :picker-options="pickerOptions" placeholder="时间" @change="handle_page_refresh">
          </el-date-picker>
        </el-form-item>

        <el-form-item>
          <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
            <el-button icon="el-icon-refresh-left" circle @click="handle_page_refresh"></el-button>
          </el-tooltip>
        </el-form-item>
        <el-form-item>
          <el-tooltip content="导出数据" placement="right" :open-delay="500" :hide-after="2000">
            <el-button icon="el-icon-download" circle @click="download"></el-button>
          </el-tooltip>
        </el-form-item>
      </el-form>

      <FixedTable :data="items" style="width: 100%" @header-click="show_series_dialog" ref="table">
        <el-table-column label="日期" prop="report_date" min-width="120px">
          <template slot-scope="scope">
            {{ $formatDate(scope.row.report_date, filters.report_type === 'DAILY' ? 'YYYY-MM-DD' : 'YYYY-MM') }}
          </template>
        </el-table-column>

        <el-table-column prop="pay_usd" label="全站付款金额" :render-header="renderHeader" column-key="全站付款金额：全站付款金额">
        </el-table-column>

        <el-table-column prop="exchange_usd" label="全站兑换金额" :render-header="renderHeader" column-key="全站兑换金额：全站兑换金额">
        </el-table-column>

        <el-table-column prop="fee_usd" label="手续费市值" :render-header="renderHeader" column-key="手续费市值：手续费市值">
        </el-table-column>

        <el-table-column prop="exchange_profit_usd" label="兑换收入市值" :render-header="renderHeader"
          column-key="兑换收入市值：兑换收入市值">
        </el-table-column>

        <el-table-column prop="exchange_loss_usd" label="兑换支出市值" :render-header="renderHeader"
          column-key="兑换收入市值：兑换收入市值">
        </el-table-column>

        <el-table-column prop="user_count" label="收付款人数" :render-header="renderHeader" column-key="人数：人数">
        </el-table-column>

        <el-table-column prop="new_user_count" label="新增人数" :render-header="renderHeader" column-key="人数：人数">
        </el-table-column>

        <el-table-column prop="exchange_count" label="兑换订单占比" :render-header="renderHeader" column-key="兑换订单笔数：兑换订单笔数">
          <template slot-scope="scope">
            {{ scope.row.exchange_count }} / {{ $formatPercent(scope.row.exchange_percent, 4) }}
          </template>
        </el-table-column>

        <el-table-column prop="avg_exchange_usd" label="平均兑换市值" :render-header="renderHeader"
          column-key="平均兑换市值：平均兑换市值">
        </el-table-column>

      </FixedTable>

      <el-pagination :current-page.sync="filters.page" :page-size.sync="filters.limit" :page-sizes="[50, 100, 200, 500]"
        @current-change="handle_page_change" :hide-on-single-page="false"
        layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>

      <el-dialog :visible.sync="show_dialog" width="80%" :before-close="handleClose">
        <Series ref="series" :render_info="render_info" v-if="show_dialog === true" @getChildValue="setChildValue">
        </Series>
      </el-dialog>

      <el-backtop></el-backtop>
    </el-main>
  </el-container>
</template>

<script>
import Vue from 'vue';
import VueClipboard from 'vue-clipboard2';
import Series from '~/components/Series';
import FixedTable from '~/components/FixedTableRender.vue';
import series from '~/plugins/report/series';
Vue.use(VueClipboard);

const binding_url = '/api/report/payment/site-report';
const exclude_render_columns = ['report_date'];
export default {
  components: {
    Series,
    FixedTable,
  },
  mixins: [series],
  methods: {
    show_series_dialog(column) {
      if (exclude_render_columns.includes(column.property)) {
        return;
      }
      this.show_dialog = true;
      this.set_render_info(column, {
        exclude_columns: exclude_render_columns,
        binding_url: binding_url,
        resp_key: 'items',
      });
    },
    get_data() {
      this.loading = true;
      this.$axios.get(binding_url, { params: this.filters }).then(res => {
        this.loading = false;
        if (res && res.data.code === 0) {
          let data = res.data.data;
          this.items = data.items;
          this.total = data.total;
        } else {
          this.items = [];
          this.total = 0;
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      });
    },
    handle_page_change() {
      this.get_data();
    },
    handle_page_refresh() {
      this.reset_page();
      this.get_data();
    },
    reset_page() {
      this.filters.page = 1;
    },
    download() {
      let filename;
      if (this.filters.report_type && this.filters.report_type.toLowerCase() === 'monthly') {
        filename = 'payment_monthly.xlsx';
      } else {
        filename = 'payment_daily.xlsx';
      }
      let params = { ...this.filters, export: true };
      this.$download_from_url(binding_url, filename, params);
    },
    update_router_query() {
      let query = {};
      Object.assign(query, this.filters);
      Object.keys(query).forEach(key => !query[key] && delete query[key]);
      this.$router.replace({ query: query });
    },
  },
  created() {
    let query = this.$route.query;
    let filters = this.filters;
    filters.page = query.page ? parseInt(query.page) : 1;
    filters.report_type = query.report_type || 'DAILY';
    this.$watch('filters', {
      handler: function () {
        this.update_router_query();
      },
      deep: true,
    });
  },
  mounted() {
    this.get_data();
  },
  data() {
    return {
      filters: {
        page: 1,
        limit: 50,
        start_date: null,
        end_date: null,
        type: '',
      },
      items: [],
      total: 0,
      loading: true,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
    };
  },
};
</script>
