<template>
  <div class="table-data" id="table-frame">
    <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
      收付款-相关记录
      <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
        <el-button icon="el-icon-refresh-left" circle @click="rehresh_all"></el-button>
      </el-tooltip>
    </h2>

    <div>
      <el-tabs v-model="active_tab_name" @tab-click="handleTabClick">
        <el-tab-pane label="收付款-划转记录" v-loading="loading" name="payment_tran_his" value="payment_tran_his"
          @tab-click="get_common_hisotry_data">

          <el-form :inline="true" :model="history_filters">
            <el-form-item label="订单ID">
              <el-input v-model="history_filters.transaction_id" clearable @change="get_common_hisotry_data"
                style="width: 200px;"></el-input>
            </el-form-item>
            <el-form-item label="用户">
              <UserSearch v-model="history_filters.user_id" :refresh_method="get_common_hisotry_data"
                @change="get_common_hisotry_data"></UserSearch>
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="history_filters.pay_tran_status" clearable placeholder="<ALL>"
                @change="get_common_hisotry_data" style="width: 180px;">
                <el-option v-for="(name, v) in pay_tran_status_dict" :key="v" :label="name" :value="v"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="时间">
              <el-date-picker v-model="filters_mid.date_range" type="datetimerange" range-separator="至"
                start-placeholder="∞" end-placeholder="∞" @change="handle_date_range_selection"
                style="width: 360px;"></el-date-picker>
            </el-form-item>

            <el-form-item>
              <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
                <el-button icon="el-icon-refresh-left" circle @click="get_common_hisotry_data"></el-button>
              </el-tooltip>
            </el-form-item>
          </el-form>

          <el-table v-loading="loading" :data="payment_tran_his_items" stripe>
            <el-table-column prop="id" label="记录ID"></el-table-column>
            <el-table-column prop="transaction_id" label="订单ID">
              <template slot-scope="scope">
                <el-link :href="'/spot/payment/pay-transaction-list?row_id=' + scope.row.transaction_id" type="primary"
                  target="_blank" :underline="false"
                  style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{{
                    scope.row.transaction_id }}</el-link>
              </template>
            </el-table-column>
            <el-table-column label="用户" show-overflow-tooltip>
              <template slot-scope="scope">
                <el-link :href="'/users/user-details?id=' + scope.row.user_id" type="primary" target="_blank"
                  :underline="false"
                  style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{{
                    scope.row.user_email || scope.row.user_id }}</el-link>
              </template>
            </el-table-column>
            <el-table-column prop="amount" label="划转数量">
              <template slot-scope="scope"> {{ scope.row.amount }} {{ scope.row.asset }} </template>
            </el-table-column>
            <el-table-column prop="type" label="类型">
              <template slot-scope="scope">{{ pay_tran_type_dict[scope.row.type] || scope.row.type }}</template>
            </el-table-column>
            <el-table-column prop="status" label="状态">
              <template slot-scope="scope">{{ pay_tran_status_dict[scope.row.status] || scope.row.status }}</template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间">
              <template slot-scope="scope">{{ format_date(scope.row.created_at) }}</template>
            </el-table-column>
            <el-table-column prop="deducted_at" label="扣减时间">
              <template slot-scope="scope">
                <div v-if="scope.row.deducted_at">
                  {{ format_date(scope.row.deducted_at) }}
                </div>
                <div v-else>-</div>
              </template>
            </el-table-column>
            <el-table-column prop="finished_at" label="完成时间">
              <template slot-scope="scope">
                <div v-if="scope.row.finished_at">
                  {{ format_date(scope.row.finished_at) }}
                </div>
                <div v-else>-</div>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination :current-page.sync="history_filters.page" :page-size.sync="history_filters.limit"
            @size-change="get_common_hisotry_data" @current-change="get_common_hisotry_data" :page-sizes="[50, 100]"
            :hide-on-single-page="false"
            :total="history_item_total" layout="total, sizes, prev, pager, next, jumper"></el-pagination>
        </el-tab-pane>

        <el-tab-pane label="收付款-对冲记录" v-loading="loading" name="hedging_his" value="hedging_his"
          @tab-click="get_common_hisotry_data">
          <el-form :inline="true" :model="history_filters">
            <el-form-item label="订单ID">
              <el-input v-model="history_filters.transaction_id" clearable @change="get_common_hisotry_data"
                style="width: 200px;"></el-input>
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="history_filters.hedging_his_status" clearable placeholder="<ALL>"
                @change="get_common_hisotry_data" style="width: 180px;">
                <el-option v-for="(name, v) in hedging_his_status_dict" :key="v" :label="name" :value="v"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="时间">
              <el-date-picker v-model="filters_mid.date_range" type="datetimerange" range-separator="至"
                start-placeholder="∞" end-placeholder="∞" @change="handle_date_range_selection"
                style="width: 360px;"></el-date-picker>
            </el-form-item>

            <el-form-item>
              <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
                <el-button icon="el-icon-refresh-left" circle @click="get_common_hisotry_data"></el-button>
              </el-tooltip>
            </el-form-item>
          </el-form>

          <el-table v-loading="loading" :data="hedging_his_items" stripe>
            <el-table-column prop="id" label="记录ID"></el-table-column>
            <el-table-column prop="transaction_id" label="订单ID">
              <template slot-scope="scope">
                <el-link :href="'/spot/payment/pay-transaction-list?row_id=' + scope.row.transaction_id" type="primary"
                  target="_blank" :underline="false"
                  style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{{
                    scope.row.transaction_id }}</el-link>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间">
              <template slot-scope="scope">{{ format_date(scope.row.created_at) }}</template>
            </el-table-column>
            <el-table-column prop="status" label="状态">
              <template slot-scope="scope">{{ hedging_his_status_dict[scope.row.status] || scope.row.status
                }}</template>
            </el-table-column>
            <el-table-column prop="source_amount" label="兑换币种数目">
              <template slot-scope="scope">{{ scope.row.source_amount }} {{ scope.row.source_asset }}</template>
            </el-table-column>
            <el-table-column prop="target_amount" label="目标币种数目">
              <template slot-scope="scope">{{ scope.row.target_amount }} {{ scope.row.target_asset }}</template>
            </el-table-column>
            <el-table-column prop="source_filled_amount" label="兑换币种成交数目">
              <template slot-scope="scope">{{ scope.row.source_filled_amount }} {{ scope.row.source_asset }}</template>
            </el-table-column>
            <el-table-column prop="target_filled_amount" label="目标币种成交数目">
              <template slot-scope="scope">{{ scope.row.target_filled_amount }} {{ scope.row.target_asset }}</template>
            </el-table-column>
            <el-table-column prop="profit_loss_data" label="盈亏数目">
              <template slot-scope="scope">
                <div v-if="scope.row.finished_at">
                  <div v-for="(amount, asset) in scope.row.profit_loss_data" :key="'short_' + asset"
                    style="display: flex; align-items: center;">
                    <span>
                      {{ amount }} {{ asset }}
                    </span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="finished_at" label="完成时间">
              <template slot-scope="scope">
                <div v-if="scope.row.finished_at">
                  {{ format_date(scope.row.finished_at) }}
                </div>
                <div v-else>-</div>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination :current-page.sync="history_filters.page" :page-size.sync="history_filters.limit"
            @size-change="get_common_hisotry_data" @current-change="get_common_hisotry_data" :page-sizes="[50, 100]"
            :hide-on-single-page="false"
            :total="history_item_total" layout="total, sizes, prev, pager, next, jumper"></el-pagination>
        </el-tab-pane>

        <el-tab-pane label="对冲-划转记录" v-loading="loading" name="hedging_tran_his" value="hedging_tran_his"
          @tab-click="get_common_hisotry_data">

          <el-form :inline="true" :model="history_filters">
            <el-form-item label="对冲ID">
              <el-input v-model="history_filters.hedging_his_id" clearable @change="get_common_hisotry_data"
                style="width: 200px;"></el-input>
            </el-form-item>
            <el-form-item label="系统用户ID">
              <UserSearch v-model="history_filters.user_id" :refresh_method="get_common_hisotry_data"
                @change="get_common_hisotry_data"></UserSearch>
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="history_filters.hedging_tran_his_status" clearable placeholder="<ALL>"
                @change="get_common_hisotry_data" style="width: 180px;">
                <el-option v-for="(name, v) in hedging_tran_his_status_dict" :key="v" :label="name"
                  :value="v"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="时间">
              <el-date-picker v-model="filters_mid.date_range" type="datetimerange" range-separator="至"
                start-placeholder="∞" end-placeholder="∞" @change="handle_date_range_selection"
                style="width: 360px;"></el-date-picker>
            </el-form-item>

            <el-form-item>
              <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
                <el-button icon="el-icon-refresh-left" circle @click="get_common_hisotry_data"></el-button>
              </el-tooltip>
            </el-form-item>
          </el-form>

          <el-table v-loading="loading" :data="hedging_tran_his_items" stripe>
            <el-table-column prop="id" label="记录ID"></el-table-column>
            <el-table-column prop="hedging_his_id" label="对冲ID">
            </el-table-column>
            <el-table-column label="系统用户ID" show-overflow-tooltip>
              <template slot-scope="scope">
                <el-link :href="'/users/user-details?id=' + scope.row.user_id" type="primary" target="_blank"
                  :underline="false"
                  style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{{
                    scope.row.user_email || scope.row.user_id }}</el-link>
              </template>
            </el-table-column>
            <el-table-column prop="type" label="类型">
              <template slot-scope="scope">{{ hedging_tran_his_type_dict[scope.row.type] || scope.row.type }}</template>
            </el-table-column>
            <el-table-column prop="status" label="状态">
              <template slot-scope="scope">{{ hedging_tran_his_status_dict[scope.row.status] || scope.row.status
              }}</template>
            </el-table-column>
            <el-table-column prop="amount" label="划转数量">
              <template slot-scope="scope"> {{ scope.row.amount }} {{ scope.row.asset }} </template>
            </el-table-column>
            <el-table-column prop="deducted_at" label="扣减时间">
              <template slot-scope="scope">
                <div v-if="scope.row.deducted_at">
                  {{ format_date(scope.row.deducted_at) }}
                </div>
                <div v-else>-</div>
              </template>
            </el-table-column>
            <el-table-column prop="finished_at" label="完成时间">
              <template slot-scope="scope">
                <div v-if="scope.row.finished_at">
                  {{ format_date(scope.row.finished_at) }}
                </div>
                <div v-else>-</div>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination :current-page.sync="history_filters.page" :page-size.sync="history_filters.limit"
            @size-change="get_common_hisotry_data" @current-change="get_common_hisotry_data" :page-sizes="[50, 100]"
            :hide-on-single-page="false"
            :total="history_item_total" layout="total, sizes, prev, pager, next, jumper"></el-pagination>
        </el-tab-pane>

        <el-tab-pane label="币种对冲数目" v-loading="loading" name="asset_hedging_amount" value="asset_hedging_amount"
          @tab-click="get_common_hisotry_data">
          <el-form :inline="true" :model="history_filters">
            <el-form-item label="币种">
              <el-input v-model="history_filters.asset" clearable @change="get_common_hisotry_data"
                style="width: 200px;"></el-input>
            </el-form-item>
            <el-form-item>
              <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
                <el-button icon="el-icon-refresh-left" circle @click="get_common_hisotry_data"></el-button>
              </el-tooltip>
            </el-form-item>
          </el-form>

          <el-table v-loading="loading" :data="asset_hedging_amount_items" stripe>
            <el-table-column prop="id" label="记录ID"></el-table-column>
            <el-table-column prop="asset" label="币种"></el-table-column>
            <el-table-column prop="amount" label="待对冲数目"></el-table-column>
          </el-table>

          <el-pagination :current-page.sync="history_filters.page" :page-size.sync="history_filters.limit"
            @size-change="get_common_hisotry_data" @current-change="get_common_hisotry_data" :page-sizes="[50, 100]"
            :hide-on-single-page="false"
            :total="history_item_total" layout="total, sizes, prev, pager, next, jumper"></el-pagination>
        </el-tab-pane>

        <el-tab-pane label="币种对冲数目流水" v-loading="loading" name="asset_hedging_amount_change_his"
          value="asset_hedging_amount_change_his" @tab-click="get_common_hisotry_data">

          <el-form :inline="true" :model="history_filters">
            <el-form-item label="币种">
              <el-input v-model="history_filters.asset" clearable @change="get_common_hisotry_data"
                style="width: 200px;"></el-input>
            </el-form-item>
            <el-form-item label="业务ID">
              <el-input v-model="history_filters.biz_id" clearable @change="get_common_hisotry_data"
                style="width: 200px;"></el-input>
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="history_filters.change_his_status" clearable placeholder="<ALL>"
                @change="get_common_hisotry_data" style="width: 180px;">
                <el-option v-for="(name, v) in change_his_status_dict" :key="v" :label="name" :value="v"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="时间">
              <el-date-picker v-model="filters_mid.date_range" type="datetimerange" range-separator="至"
                start-placeholder="∞" end-placeholder="∞" @change="handle_date_range_selection"
                style="width: 360px;"></el-date-picker>
            </el-form-item>

            <el-form-item>
              <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
                <el-button icon="el-icon-refresh-left" circle @click="get_common_hisotry_data"></el-button>
              </el-tooltip>
            </el-form-item>
          </el-form>

          <el-table v-loading="loading" :data="asset_hedging_amount_change_his_items" stripe>
            <el-table-column prop="id" label="记录ID"></el-table-column>
            <el-table-column prop="asset" label="币种"></el-table-column>
            <el-table-column prop="change_amount" label="变更变更"></el-table-column>
            <el-table-column prop="after_amount" label="变更后数目"></el-table-column>
            <el-table-column prop="status" label="状态">
              <template slot-scope="scope">
                {{ change_his_status_dict[scope.row.status] || scope.row.status }}
              </template>
            </el-table-column>
            <el-table-column prop="change_type" label="变更类型"></el-table-column>
            <el-table-column prop="biz_id" label="业务ID"></el-table-column>
            <el-table-column prop="biz_type" label="业务类型">
              <template slot-scope="scope">
                {{ change_his_biz_type_dict[scope.row.biz_type] || scope.row.biz_type }}
              </template>
            </el-table-column>
          </el-table>

          <el-pagination :current-page.sync="history_filters.page" :page-size.sync="history_filters.limit"
            @size-change="get_common_hisotry_data" @current-change="get_common_hisotry_data" :page-sizes="[50, 100]"
            :hide-on-single-page="false"
            :total="history_item_total" layout="total, sizes, prev, pager, next, jumper"></el-pagination>
        </el-tab-pane>

        <el-tab-pane label="对冲-兑换记录" v-loading="loading" name="fixed_exchange_his" value="fixed_exchange_his"
          @tab-click="get_common_hisotry_data">
          <el-form :inline="true" :model="history_filters">
            <el-form-item label="业务ID">
              <el-input v-model="history_filters.biz_id" clearable @change="get_common_hisotry_data"
                style="width: 200px;"></el-input>
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="history_filters.fixed_exchange_his_status" clearable placeholder="<ALL>"
                @change="get_common_hisotry_data" style="width: 180px;">
                <el-option v-for="(name, v) in fixed_exchange_his_status_dict" :key="v" :label="name"
                  :value="v"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="时间">
              <el-date-picker v-model="filters_mid.date_range" type="datetimerange" range-separator="至"
                start-placeholder="∞" end-placeholder="∞" @change="handle_date_range_selection"
                style="width: 360px;"></el-date-picker>
            </el-form-item>

            <el-form-item>
              <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
                <el-button icon="el-icon-refresh-left" circle @click="get_common_hisotry_data"></el-button>
              </el-tooltip>
            </el-form-item>
          </el-form>

          <el-table v-loading="loading" :data="fixed_exchange_his_items" stripe>
            <el-table-column prop="id" label="记录ID"></el-table-column>
            <el-table-column prop="created_at" label="创建时间">
              <template slot-scope="scope">{{ format_date(scope.row.created_at) }}</template>
            </el-table-column>
            <el-table-column prop="status" label="状态">
              <template slot-scope="scope">{{ hedging_his_status_dict[scope.row.status] || scope.row.status
                }}</template>
            </el-table-column>
            <el-table-column prop="biz_id" label="业务ID"></el-table-column>
            <el-table-column prop="biz_type" label="业务类型">
              <template slot-scope="scope">
                {{ fixed_exchange_his_biz_type_dict[scope.row.biz_type] || scope.row.biz_type }}
              </template>
            </el-table-column>
            <el-table-column label="系统用户ID" show-overflow-tooltip>
              <template slot-scope="scope">
                <el-link :href="'/users/user-details?id=' + scope.row.sys_user_id" type="primary" target="_blank"
                  :underline="false"
                  style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{{
                    scope.row.user_email || scope.row.sys_user_id }}</el-link>
              </template>
            </el-table-column>
            <el-table-column prop="source_amount" label="兑换币种数目">
              <template slot-scope="scope">
                <div v-for="(amount, asset) in scope.row.source_asset_data" :key="'source_' + asset"
                  style="display: flex; align-items: center;">
                  <span>
                    {{ amount }} {{ asset }}
                  </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="target_amount" label="目标币种数目">
              <template slot-scope="scope">
                <div v-for="(amount, asset) in scope.row.target_asset_data" :key="'target_' + asset"
                  style="display: flex; align-items: center;">
                  <span>
                    {{ amount }} {{ asset }}
                  </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="source_filled_amount" label="兑换币种成交数目">
              <template slot-scope="scope">
                <div v-for="(amount, asset) in scope.row.used_source_data" :key="'source_filled_' + asset"
                  style="display: flex; align-items: center;">
                  <span>
                    {{ amount }} {{ asset }}
                  </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="target_filled_amount" label="目标币种成交数目">
              <template slot-scope="scope">
                <div v-for="(amount, asset) in scope.row.filled_target_data" :key="'target_filled_' + asset"
                  style="display: flex; align-items: center;">
                  <span>
                    {{ amount }} {{ asset }}
                  </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="finished_at" label="完成时间">
              <template slot-scope="scope">
                <div v-if="scope.row.finished_at">
                  {{ format_date(scope.row.finished_at) }}
                </div>
                <div v-else>-</div>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination :current-page.sync="history_filters.page" :page-size.sync="history_filters.limit"
            @size-change="get_common_hisotry_data" @current-change="get_common_hisotry_data" :page-sizes="[50, 100]"
            :hide-on-single-page="false"
            :total="history_item_total" layout="total, sizes, prev, pager, next, jumper"></el-pagination>
        </el-tab-pane>  

        <el-tab-pane label="对冲-兑换订单" v-loading="loading" name="fixed_exchange_order_his"
          value="fixed_exchange_order_his" @tab-click="get_common_hisotry_data">

          <el-form :inline="true" :model="history_filters">
            <el-form-item label="兑换ID">
              <el-input v-model="history_filters.exchange_id" clearable @change="get_common_hisotry_data"
                style="width: 200px;"></el-input>
            </el-form-item>
            <el-form-item label="订单ID">
              <el-input v-model="history_filters.order_id" clearable @change="get_common_hisotry_data"
                style="width: 200px;"></el-input>
            </el-form-item>
            <el-form-item label="市场">
              <el-input v-model="history_filters.market" clearable @change="get_common_hisotry_data"
                style="width: 200px;"></el-input>
            </el-form-item>
            <el-form-item label="方向">
              <el-select v-model="history_filters.side" clearable placeholder="<ALL>" @change="get_common_hisotry_data"
                style="width: 180px;">
                <el-option label="卖" value="1"></el-option>
                <el-option label="买" value="2"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
                <el-button icon="el-icon-refresh-left" circle @click="get_common_hisotry_data"></el-button>
              </el-tooltip>
            </el-form-item>
          </el-form>

          <el-table v-loading="loading" :data="fixed_exchange_order_his_items" stripe>
            <el-table-column prop="id" label="记录ID"></el-table-column>
            <el-table-column label="兑换记录ID" prop="exchange_id" show-overflow-tooltip min-width="80%"> </el-table-column>
            <el-table-column label="系统用户ID" show-overflow-tooltip>
              <template slot-scope="scope">
                <el-link :href="'/users/user-details?id=' + scope.row.sys_user_id" type="primary" target="_blank"
                  :underline="false"
                  style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{{
                    scope.row.user_email || scope.row.sys_user_id }}</el-link>
              </template>
            </el-table-column>
            <el-table-column prop="order_id" label="订单ID">
              <template slot-scope="scope">
                <el-link type="primary" :href="`/spot/pending-order-detail?order_id=${scope.row.order_id}&market_type=spot&market=${scope.row.market}&user_id=${scope.row.sys_user_id}`
                  " :underline="false" target="_blank">
                  {{ scope.row.order_id }}
                </el-link>
              </template>
            </el-table-column>
            <el-table-column label="市场" prop="market" show-overflow-tooltip min-width="50%"> </el-table-column>
            <el-table-column label="方向" prop="side" :formatter="row => format_order_side(row.side)" min-width="50%">
            </el-table-column>
            <el-table-column label="类型" prop="type" :formatter="row => order_type_dict[row.type]" min-width="50%">
            </el-table-column>
            <el-table-column prop="price" label="委托价">
              <template slot-scope="scope">
                <span v-if="scope.row.type == 1"> {{ scope.row.price }} </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="amount" label="委托数量">
              <template slot-scope="scope">
                <span> {{ scope.row.amount }} {{ scope.row.base_asset }} </span>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" :formatter="row => format_date(row.created_at)" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="price_deviation" label="价格偏差"
              :formatter="row => (row.price_deviation ? $formatPercent(row.price_deviation, 8) : '-')">
            </el-table-column>
          </el-table>

          <el-pagination :current-page.sync="history_filters.page" :page-size.sync="history_filters.limit"
            @size-change="get_common_hisotry_data" @current-change="get_common_hisotry_data" :page-sizes="[50, 100]"
            :hide-on-single-page="false"
            :total="history_item_total" layout="total, sizes, prev, pager, next, jumper"></el-pagination>
        </el-tab-pane>

      </el-tabs>
    </div>

    <el-backtop></el-backtop>
  </div>
</template>

<style scoped>
#sty_params .el-form-item {
  margin-bottom: 5px;
  font-size: 16px;
  color: #000000;
}
</style>

<script>
import moment from "moment";
import UserSearch from "../../../components/user/UserSearch";

const history_url = "/api/payment/relation-history";

export default {
  components: { UserSearch },
  methods: {
    cleanUpParams(params, keysToClean) {
      keysToClean.forEach((key) => {
        if (
          params[key] == undefined ||
          params[key] == null ||
          params[key] == ""
        ) {
          delete params[key];
        }
      });
    },
    get_common_hisotry_data() {
      let params = Object.assign({}, this.history_filters);
      let tab_name = this.active_tab_name;
      params.history_type = tab_name.replaceAll("_history", "");
      this.cleanUpParams(params, [
        "row_id",
        "transaction_id",
        "hedging_his_id",
        "exchange_id",
        "biz_id",
        "order_id",
      ]);
      this.loading = true;
      this.$axios.get(`${history_url}`, { params: params }).then((res) => {
        this.loading = false;
        if (res && res.data.code === 0) {
          let data = res.data.data;
          this.history_item_total = data.total;
          this.set_history_items(tab_name, data.items);
          this.pay_tran_status_dict = data.extra.pay_tran_status_dict;
          this.hedging_his_status_dict = data.extra.hedging_his_status_dict;
          this.change_his_status_dict = data.extra.change_his_status_dict;
          this.pay_tran_type_dict = data.extra.pay_tran_type_dict;
          this.hedging_tran_his_type_dict = data.extra.hedging_tran_his_type_dict;
          this.hedging_tran_his_status_dict = data.extra.hedging_tran_his_status_dict;
          this.change_his_biz_type_dict = data.extra.change_his_biz_type_dict;
          this.fixed_exchange_his_status_dict = data.extra.fixed_exchange_his_status_dict;
          this.fixed_exchange_his_biz_type_dict = data.extra.fixed_exchange_his_biz_type_dict;
        } else {
          this.history_item_total = 1;
          this.$message.error(
            `code: ${res.data?.code}; message: ${res.data?.message
            }; data: ${JSON.stringify(res.data?.data)}`
          );
        }
      });
    },
    set_history_items(tab_name, items) {
      if (tab_name == "payment_tran_his") {
        this.payment_tran_his_items = items;
      } else if (tab_name == "hedging_his") {
        this.hedging_his_items = items;
      } else if (tab_name == "hedging_tran_his") {
        this.hedging_tran_his_items = items;
      } else if (tab_name == "asset_hedging_amount") {
        this.asset_hedging_amount_items = items;
      } else if (tab_name == "asset_hedging_amount_change_his") {
        this.asset_hedging_amount_change_his_items = items;
      } else if (tab_name == "fixed_exchange_his") {
        this.fixed_exchange_his_items = items;
      } else if (tab_name == "fixed_exchange_order_his") {
        this.fixed_exchange_order_his_items = items;
      }
    },
    handleTabClick(target) {
      this.get_common_hisotry_data();
    },
    rehresh_all() {
      this.reset_page();
      this.get_common_hisotry_data();
    },
    handle_history_refresh() {
      this.get_common_hisotry_data();
    },
    handle_date_range_selection() {
      this.reset_page();
      this.handle_history_refresh();
    },
    handle_limit_change() {
      this.reset_page();
      this.handle_history_refresh();
    },
    handle_page_change() {
      this.handle_history_refresh();
    },
    reset_page() {
      this.history_filters.page = 1;
    },
    handle_content_copy() {
      this.$message.success("内容已复制到剪贴板");
    },
    format_order_side(value) {
      return value === 1 ? '卖出' : '买入';
    },
    format_date(timestamp, pattern = "YYYY-MM-DD HH:mm:ss") {
      if (timestamp) {
        return moment(Number(timestamp) * 1000).format(pattern);
      }
      return "-";
    },
  },
  created() {
  },
  mounted() {
    this.get_common_hisotry_data();
  },
  data() {
    return {
      active_tab_name: "payment_tran_his",
      payment_tran_his_items: [],
      hedging_his_items: [],
      hedging_tran_his_items: [],
      asset_hedging_amount_items: [],
      asset_hedging_amount_change_his_items: [],
      fixed_exchange_his_items: [],
      fixed_exchange_order_his_items: [],
      history_item_total: 0,
      history_filters: {
        history_type: null,
        row_id: null,
        user_id: null,
        equity_id: null,
        main_user_id: null,
        user_equity_id: null,
        trade_business_id: null,
        settle_his_id: null,
        start: null,
        end: null,
        settlement_status: null,
        page: 1,
        limit: 50,
      },
      filters_mid: {
        date_range: null,
      },
      pay_tran_status_dict: {},
      change_his_status_dict: {},
      pay_tran_type_dict: {},
      hedging_tran_his_type_dict: {},
      hedging_tran_his_status_dict: {},
      change_his_biz_type_dict: {},
      fixed_exchange_his_status_dict: {},
      fixed_exchange_his_biz_type_dict: {},
      order_type_dict: { 1: '限价单', 2: '市价单' },
      loading: true,
    };
  },
  watch: {
    "filters_mid.date_range": function (date_range) {
      Object.assign(this.history_filters, {
        start:
          date_range && date_range[0] ? date_range[0].getTime() / 1000 : null,
        end:
          date_range && date_range[1] ? date_range[1].getTime() / 1000 : null,
      });
    },
  },
};
</script>
