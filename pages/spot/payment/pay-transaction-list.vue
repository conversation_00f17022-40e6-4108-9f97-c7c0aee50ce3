<template>
  <div class="table-data" id="table-frame">
    <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">收付款记录</h2>

    <el-form :inline="true">
      <el-form-item label="订单ID">
        <el-input v-model="filters.row_id" clearable :refresh_method="get_data" @change="get_data"></el-input>
      </el-form-item>

      <el-form-item label="付款用户">
        <UserSearch v-model="filters.payer_id" :refresh_method="get_data" @change="get_data"></UserSearch>
      </el-form-item>

      <el-form-item label="收款用户">
        <UserSearch v-model="filters.receiver_id" :refresh_method="get_data" @change="get_data"></UserSearch>
      </el-form-item>

      <el-form-item label="付款币种">
        <el-select v-model="filters.pay_asset" clearable filterable @change="handle_page_refresh" placeholder="<ALL>">
          <el-option v-for="m in assets" :key="m" :label="m" :value="m"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="收款币种">
        <el-select v-model="filters.receive_asset" clearable filterable @change="handle_page_refresh"
          placeholder="<ALL>">
          <el-option v-for="m in assets" :key="m" :label="m" :value="m"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="是否兑换">
        <el-select v-model="filters.is_diff_asset" clearable placeholder="<ALL>" @change="handle_page_refresh"
          style="width: 180px;">
          <el-option label="是" value="1"></el-option>
          <el-option label="否" value="0"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="状态">
        <el-select v-model="filters.status" clearable placeholder="<ALL>" @change="handle_page_refresh"
          style="width: 180px;">
          <el-option v-for="(name, status) in status_dict" :key="status" :label="name" :value="status"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="开始时间">
        <el-date-picker @change="handle_date_range_selection" v-model="filters_mid.date_range[0]" type="datetime"
          value-format="timestamp">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="结束时间">
        <el-date-picker @change="handle_date_range_selection" v-model="filters_mid.date_range[1]" type="datetime"
          value-format="timestamp">
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
          <el-button icon="el-icon-refresh-left" circle @click="handle_page_refresh"></el-button>
        </el-tooltip>
      </el-form-item>
    </el-form>

    <el-table :data="items" v-loading="loading" style="width: 100%" stripe>
      <el-table-column label="订单ID" prop="id" show-overflow-tooltip min-width="100px"></el-table-column>

      <el-table-column label="付款时间" :formatter="row => format_date(row.created_at)" min-width="160px"
        show-overflow-tooltip></el-table-column>

      <el-table-column label="是否兑换" :formatter="row => row.is_diff_asset === true ? '是' : '否'" min-width="100px"
        show-overflow-tooltip></el-table-column>

      <el-table-column label="收款币种" prop="receive_asset" show-overflow-tooltip min-width="100px"></el-table-column>
      <el-table-column label="收款数目" prop="receive_amount" show-overflow-tooltip min-width="120px"></el-table-column>

      <el-table-column label="付款币种" prop="pay_asset" show-overflow-tooltip min-width="100px"></el-table-column>
      <el-table-column label="付款数目" prop="pay_amount" show-overflow-tooltip min-width="120px"></el-table-column>

      <el-table-column label="兑换报价" prop="quote_price" show-overflow-tooltip min-width="180px">
        <template slot-scope="scope">
          <div v-if="scope.row.is_diff_asset === true">
            1 {{ scope.row.pay_asset }} = {{ scope.row.quote_price }} {{ scope.row.receive_asset }}
          </div>
          <div v-else>
            -
          </div>
        </template>
      </el-table-column>

      <el-table-column label="付款人" show-overflow-tooltip min-width="160px">
        <template slot-scope="scope">
          <el-link :href="'/users/user-details?id=' + scope.row.payer_id" type="primary" target="_blank"
            :underline="false"
            style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{{
              scope.row.payer_email || scope.row.payer_id }}</el-link>
        </template>
      </el-table-column>

      <el-table-column label="收款人" show-overflow-tooltip min-width="160px">
        <template slot-scope="scope">
          <el-link :href="'/users/user-details?id=' + scope.row.receiver_id" type="primary" target="_blank"
            :underline="false"
            style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{{
              scope.row.receiver_email || scope.row.receiver_id }}</el-link>
        </template>
      </el-table-column>

      <el-table-column label="完成时间" :formatter="row => format_date(row.finished_at)" min-width="190px"
        show-overflow-tooltip></el-table-column>

      <el-table-column label="订单状态" :formatter="row => status_dict[row.status]" min-width="100px"
        show-overflow-tooltip></el-table-column>

      <el-table-column prop="hedging_profit_loss_usd" label="兑换盈亏市值" min-width="180px">
        <template slot-scope="scope">
          <div v-if="scope.row.is_diff_asset && scope.row.hedging_row_info && scope.row.hedging_row_info.status == 'FINISHED'">
            {{ scope.row.hedging_profit_loss_usd }} USD

            <div slot="reference">
              <div v-for="(amount, asset) in scope.row.hedging_profit_loss_data" :key="'short_' + asset"
                style="display: flex; align-items: center;">
                <span
                  v-if="amount != 0"
                  style="font-weight: bold; color: #3498db; background-color: #f0f0f0; padding: 2px 5px; border-radius: 3px;">
                  {{ amount }} {{ asset }}
                </span>
              </div>
            </div>
        </div>
        </template>
      </el-table-column>

    </el-table>

    <el-pagination :current-page.sync="filters.page" :page-size.sync="filters.limit" :page-sizes="[100, 200, 500]"
      :total="total" @size-change="handle_limit_change" @current-change="handle_page_change"
      :hide-on-single-page="false" layout="total, sizes, prev, pager, next, jumper"></el-pagination>

    <el-backtop></el-backtop>
  </div>
</template>

<style scoped></style>

<script>
import moment from "moment";
import UserSearch from "../../../components/user/UserSearch";

const list_url = "/api/payment/transaction-list";

export default {
  components: { UserSearch },
  methods: {
    get_data() {
      this.del_filter_empty_field("row_id");
      this.del_filter_empty_field("is_diff_asset");
      this.loading = true;
      this.$axios.get(list_url, { params: this.filters }).then((res) => {
        this.loading = false;
        if (res && res.data.code === 0) {
          let data = res.data.data;
          this.items = data.items;
          this.total = data.total;

          let extra = data.extra;
          this.assets = extra.assets;
          this.status_dict = extra.status_dict;
        } else {
          this.items = [];
          this.total = 0;

          this.$message.error(
            `code: ${res.data?.code}; message: ${res.data?.message}`
          );
        }
      });
    },
    del_filter_empty_field(fileld_name) {
      let v = this.filters[fileld_name];
      if (v === undefined || v === null || v === "") {
        delete this.filters[fileld_name];
      }
    },
    handle_date_range_selection() {
      this.reset_page();
      this.get_data();
    },
    handle_limit_change() {
      this.reset_page();
      this.get_data();
    },
    handle_page_change() {
      this.get_data();
    },
    handle_page_refresh() {
      this.reset_page();
      this.get_data();
    },
    reset_page() {
      this.filters.page = 1;
    },
    handle_content_copy() {
      this.$message.success("内容已复制到剪贴板");
    },
    format_date(timestamp, pattern = "YYYY-MM-DD HH:mm:ss") {
      if (timestamp) {
        return moment(Number(timestamp) * 1000).format(pattern);
      }
      return timestamp;
    },
    notice(title, message) {
      this.$alert(message, title, {
        confirmButtonText: "确定",
      });
    },
    res_success_notice(res) {
      let title = "提交成功";
      this.notice(title, title);
    },
    res_fail_notice(res) {
      let title = "提交失败";
      let message = `(code: ${res.data?.code}; data: ${res.data?.data}; message: ${res.data?.message})`;
      this.notice(title, message);
    },
    res_error_notice(error) {
      let title = "提交失败";
      let message = `(code: ${error.response.status}; message: ${error.message})`;
      this.notice(title, message);
    },
    update_router_query() {
      let query = {};
      Object.assign(query, this.filters);
      Object.keys(query).forEach((key) => !query[key] && delete query[key]);
      this.$router.replace({ query: query });
    },
  },
  created() {
    this.$sync_router_query(this, "filters", {
      payer_id: String,
      receiver_id: String,
      pay_asset: String,
      receive_asset: String,
      row_id: String,
      is_diff_asset: String,
      status: String,
      start: String,
      end: String,
      page: String,
      limit: String,
    });
    let filters = this.filters;
    if (filters.start && filters.end) {
      this.filters_mid.date_range = [
        new Date(filters.start * 1000),
        new Date(filters.end * 1000),
      ];
    }
  },
  mounted() {
    this.get_data();
  },
  data() {
    return {
      filters: {
        row_id: null,
        payer_id: null,
        receiver_id: null,
        pay_asset: null,
        receive_asset: null,
        is_diff_asset: null,
        status: null,
        start: null,
        end: null,
        page: 1,
        limit: 100,
      },
      filters_mid: {
        date_range: [null, null],
      },
      items: [],
      total: 0,
      status_dict: {},
      assets: [],
      loading: true,
    };
  },
  watch: {
    "filters_mid.date_range": function (date_range) {
      Object.assign(this.filters, {
        start:
          date_range && date_range[0] ? date_range[0] / 1000 : null,
        end:
          date_range && date_range[1] ? date_range[1] / 1000 : null,
      });
    },
  },
};
</script>

<style>
.el-table .warning {
  color: #000000;
  background: #fff2a7;
}

.el-table .error {
  color: #000000;
  background: #ff8b8b;
}
</style>
