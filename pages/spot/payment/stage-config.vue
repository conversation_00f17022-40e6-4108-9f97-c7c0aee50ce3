<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">一口价兑换档位配置</h2>

      <el-form :inline="true" :model="filters">
        <el-form-item>
          <el-button type="primary" @click="handle_creation">+ 新增档位</el-button>
        </el-form-item>

        <el-form-item>
          <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
            <el-button icon="el-icon-refresh-left" circle @click="handle_page_refresh"></el-button>
          </el-tooltip>
        </el-form-item>
      </el-form>

      <el-table :data="items" v-loading="loading" ref="table" style="width: 100%">
        <el-table-column label="档位" prop="stage"> </el-table-column>

        <el-table-column label="档位市值范围（USD）" prop="min_depth_usd" :formatter="row => format_depth_usd(row)"> </el-table-column>

        <el-table-column label="最大价值范围（USD）" prop="max_exchange_usd"></el-table-column>

        <el-table-column label="最大价格偏差" prop="max_price_slippage"></el-table-column>

        <el-table-column label="兑换并发市场限制" prop="max_pending_num"></el-table-column>

        <el-table-column label="操作">
          <template slot-scope="scope">
            <span>
              <el-tooltip content="编辑">
                <el-button size="mini" @click="handle_edit(scope.row)" type="primary" icon="el-icon-edit" circle></el-button>
              </el-tooltip>
            </span>
            <span>
              <el-tooltip content="删除">
                <el-button size="mini" @click="handle_delete(scope.row)" type="danger" icon="el-icon-delete" circle></el-button>
              </el-tooltip>
            </span>
          </template>
        </el-table-column>
      </el-table>

      <el-dialog
        :title="dialog_type === DIALOG_CREATION ? '添加档位配置' : '编辑档位配置'"
        :visible.sync="dialog_visible"
        destroy-on-close
        width="60%"
        :before-close="handle_close"
      >
        <el-form :model="form" ref="form" label-width="200px">
          <span v-if="dialog_type === DIALOG_EDIT">
            <el-form-item prop="user_id" label="当前档位">
              {{ form.stage }}
            </el-form-item>
          </span>

          <el-form-item label="最小档位市值" prop="min_depth_usd" required>
            <el-input
              type="number"
              @input="handle_number_input('min_depth_usd', 4)"
              style="width:300px"
              v-model="form.min_depth_usd"
              :min="0"
              :precision="4"
            >
              <template slot="append">USD</template>
            </el-input>
          </el-form-item>

          <el-form-item label="最大价值范围" prop="max_exchange_usd" required>
            <el-input
              type="number"
              @input="handle_number_input('max_usd', 4)"
              style="width:300px"
              v-model="form.max_exchange_usd"
              :min="0"
              :precision="4"
            >
              <template slot="append">USD</template>
            </el-input>
          </el-form-item>


          <el-form-item label="最大价格偏差" prop="max_price_slippage" required>
            <el-input
              type="number"
              @input="handle_number_input('max_price_slippage', 4)"
              style="width:300px"
              v-model="form.max_price_slippage"
              :min="0"
              :precision="4"
            >
            </el-input>
          </el-form-item>

          <el-form-item label="兑换并发市场限制" prop="max_pending_num" required>
            <el-input
              style="width:300px"
              v-model="form.max_pending_num"
            >
            </el-input>
          </el-form-item>
        </el-form>

        <span slot="footer" class="dialog-footer">
          <el-button @click="handle_close">取 消</el-button>
          <el-button type="primary" @click="handle_submit">确 定</el-button>
        </span>
      </el-dialog>

      <el-backtop></el-backtop>
    </el-main>
  </el-container>
</template>

<script>
import Vue from 'vue';
import VueClipboard from 'vue-clipboard2';
Vue.use(VueClipboard);

const binding_url = '/api/payment/stage-config';
const DIALOG_CREATION = 'creation';
const DIALOG_EDIT = 'edit';

export default {
  methods: {
    get_data() {
      this.loading = true;
      this.$axios.get(binding_url, { params: this.filters }).then(res => {
        this.loading = false;
        if (res && res.data.code === 0) {
          let data = res.data.data;
          this.items = data;
          this.total = data.length;
        } else {
          this.items = [];
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      });
    },
    format_depth_usd(row) {
      let str = row.min_depth_usd + ' <= X';
      if (row.max_depth_usd) {
        str = str + ' < ' + row.max_depth_usd;
      }
      return str;
    },
    handle_creation() {
      this.form = {};
      this.dialog_type = this.DIALOG_CREATION;
      this.dialog_visible = true;
    },
    handle_edit(row) {
      this.form = _.clone(row);
      this.dialog_type = this.DIALOG_EDIT;
      this.dialog_visible = true;
    },
    handle_close() {
      this.form = {};
      this.dialog_visible = false;
    },
    handle_submit() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          this.$alert('校验失败请修改', '校验失败请修改', {
            confirmButtonText: '确定',
          });
          return false;
        } else {
          this.submit_validate_data();
        }
      });
    },
    submit_validate_data() {
      let configs = [];
      this.items.forEach(item => {
        configs.push({
          stage: item.stage,
          min_depth_usd: item.min_depth_usd,
          max_exchange_usd: item.max_exchange_usd,
          max_price_slippage: item.max_price_slippage,
          max_pending_num: item.max_pending_num,
        });
      });
      if (this.dialog_type === DIALOG_CREATION) {
        // 新增一条
        configs.push({
          stage: -1,
          min_depth_usd: this.form.min_depth_usd,
          max_exchange_usd: this.form.max_exchange_usd,
          max_price_slippage: this.form.max_price_slippage,
          max_pending_num: this.form.max_pending_num,
        });
      } else {
        // 编辑一条
        for (let index = 0; index < configs.length; index++) {
          let item = configs[index];
          if (item.stage == this.form.stage) {
            item.min_depth_usd = this.form.min_depth_usd;
            item.max_exchange_usd = this.form.max_exchange_usd;
            item.max_price_slippage = this.form.max_price_slippage;
            item.max_pending_num = this.form.max_pending_num;
          }
        }
      }
      this.$axios['put'](binding_url, { configs: configs })
        .then(res => {
          if (res?.data?.code === 0) {
            this.form = {};
            this.dialog_visible = false;
            this.res_success_notice(res);
            this.get_data();
          } else {
            this.res_fail_notice(res);
          }
        })
        .catch(e => {
          console.log(e);
          this.res_error_notice(e);
        });
    },
    handle_delete(row) {
      this.$confirm(`确认删除?`)
        .then(() => {
          let configs = [];
          for (let index = 0; index < this.items.length; index++) {
            let item = this.items[index];
            if (item.min_depth_usd == row.min_depth_usd) {
              continue;
            }
            configs.push({
              stage: item.stage,
              min_depth_usd: item.min_depth_usd,
              max_exchange_usd: item.max_exchange_usd,
              max_price_slippage: item.max_price_slippage,
              max_pending_num: item.max_pending_num,
            });
          }
          this.$axios
            .put(binding_url + '?delete=1', { configs: configs })
            .then(res => {
              if (res?.data?.code === 0) {
                this.notice('删除成功', '删除成功');
                this.get_data();
              } else {
                this.res_fail_notice(res);
              }
            })
            .catch(e => {
              console.log(e);
              this.res_error_notice(e);
            });
        })
        .catch(e => {});
    },
    handle_number_input(field, place) {
      place = place || 8;
      let val = this.form[field];
      if (val === undefined || val === '') {
        return;
      }
      // /^\d*(\.?\d{0,8})/g
      var re = new RegExp('^\\d*(\.?\\d{0,' + place + '})', 'g');
      let res = String(val).match(re)[0];
      this.form[field] = res;
    },
    notice(title, message) {
      this.$alert(message, title, {
        confirmButtonText: '确定',
      });
    },
    res_success_notice(res) {
      let title = '提交成功';
      this.notice(title, title);
    },
    res_fail_notice(res) {
      let title = '提交失败';
      let message = `(code: ${res.data?.code}; message: ${res.data?.message})`;
      this.notice(title, message);
    },
    res_error_notice(error) {
      let title = '提交失败';
      let message = `(code: ${error.response.status}; message: ${error.message})`;
      this.notice(title, message);
    },
    handle_page_change(val) {
      this.page = val;
    },
    handle_limit_change(val) {
      this.page = 1;
      this.limit = val;
    },
    handle_page_refresh() {
      this.get_data();
    },
    update_router_query() {
      let query = {};
      Object.assign(query, this.filters);
      Object.keys(query).forEach(key => !query[key] && delete query[key]);
      this.$router.replace({ query: query });
    },
  },
  created() {
    this.DIALOG_CREATION = DIALOG_CREATION;
    this.DIALOG_EDIT = DIALOG_EDIT;
    this.$watch('filters', {
      handler: function() {
        this.update_router_query();
      },
      deep: true,
    });
  },
  mounted() {
    this.get_data();
  },
  data() {
    return {
      filters: {},
      page: 1,
      limit: 100,
      total: 0,
      items: [],
      form: {},
      dialog_type: null,
      dialog_visible: false,
      loading: true,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
    };
  },
};
</script>
